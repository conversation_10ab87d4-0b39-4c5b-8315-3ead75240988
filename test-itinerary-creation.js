#!/usr/bin/env node

/**
 * Test script to verify itinerary creation functionality
 * This script tests the API endpoint for creating itineraries from bookings
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:9000';
const BOOKING_ID = 'order_01K0EYFMQT4P6Y5E86Q02D82ZF'; // Example booking ID

async function testItineraryCreation() {
  console.log('🧪 Testing Itinerary Creation from Booking...');
  
  try {
    // Test the API endpoint
    const response = await fetch(
      `${BASE_URL}/admin/hotel-management/bookings/${BOOKING_ID}/itinerary/create`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          created_by: 'test_user',
        }),
      }
    );

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Itinerary creation successful!');
      console.log('📋 Response:', JSON.stringify(data, null, 2));
      
      // Test the redirect URL
      if (data.redirect_url) {
        console.log(`🔗 Redirect URL: ${data.redirect_url}`);
      }
    } else {
      console.log('❌ Itinerary creation failed');
      console.log('📋 Error response:', JSON.stringify(data, null, 2));
    }
  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
  }
}

// Run the test
testItineraryCreation();
