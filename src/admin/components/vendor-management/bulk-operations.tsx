import React, { useState } from "react";
import {
  Button,
  DropdownMenu,
  Text,
  Badge,
  FocusModal,
  Select,
  Textarea,
  toast,
} from "@camped-ai/ui";
import {
  MoreHorizontal,
  Check,
  X,
  Trash2,
  Mail,
  Download,
  Upload,
  Users,
  AlertTriangle,
} from "lucide-react";

interface BulkOperationsProps {
  selectedVendors: string[];
  onClearSelection: () => void;
  onRefresh: () => void;
}

interface BulkAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  color: "default" | "success" | "danger" | "warning";
  requiresConfirmation: boolean;
  description: string;
}

const BulkOperations: React.FC<BulkOperationsProps> = ({
  selectedVendors,
  onClearSelection,
  onRefresh,
}) => {
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedAction, setSelectedAction] = useState<BulkAction | null>(null);
  const [actionData, setActionData] = useState<Record<string, any>>({});
  const [isProcessing, setIsProcessing] = useState(false);

  const bulkActions: BulkAction[] = [
    {
      id: "activate",
      label: "Activate Vendors",
      icon: <Check className="h-4 w-4" />,
      color: "success",
      requiresConfirmation: true,
      description: "Activate the selected vendors to make them available for orders.",
    },
    {
      id: "deactivate",
      label: "Deactivate Vendors",
      icon: <X className="h-4 w-4" />,
      color: "warning",
      requiresConfirmation: true,
      description: "Deactivate the selected vendors. They will not be available for new orders.",
    },
    {
      id: "delete",
      label: "Delete Vendors",
      icon: <Trash2 className="h-4 w-4" />,
      color: "danger",
      requiresConfirmation: true,
      description: "Permanently delete the selected vendors. This action cannot be undone.",
    },
    {
      id: "send_email",
      label: "Send Email",
      icon: <Mail className="h-4 w-4" />,
      color: "default",
      requiresConfirmation: true,
      description: "Send an email to the selected vendors.",
    },
    {
      id: "export",
      label: "Export Data",
      icon: <Download className="h-4 w-4" />,
      color: "default",
      requiresConfirmation: false,
      description: "Export vendor data to CSV file.",
    },
    {
      id: "update_category",
      label: "Update Category",
      icon: <Users className="h-4 w-4" />,
      color: "default",
      requiresConfirmation: true,
      description: "Update the business category for selected vendors.",
    },
  ];

  const handleActionSelect = (action: BulkAction) => {
    setSelectedAction(action);
    setActionData({});
    
    if (action.requiresConfirmation) {
      setShowConfirmModal(true);
    } else {
      executeAction(action);
    }
  };

  const executeAction = async (action: BulkAction) => {
    setIsProcessing(true);
    
    try {
      switch (action.id) {
        case "activate":
          await handleStatusUpdate("active");
          break;
        case "deactivate":
          await handleStatusUpdate("inactive");
          break;
        case "delete":
          await handleDelete();
          break;
        case "send_email":
          await handleSendEmail();
          break;
        case "export":
          await handleExport();
          break;
        case "update_category":
          await handleUpdateCategory();
          break;
        default:
          throw new Error("Unknown action");
      }
      
      toast.success(`${action.label} completed successfully`);
      onClearSelection();
      onRefresh();
    } catch (error) {
      console.error("Bulk operation failed:", error);
      toast.error(`Failed to ${action.label.toLowerCase()}`);
    } finally {
      setIsProcessing(false);
      setShowConfirmModal(false);
      setSelectedAction(null);
    }
  };

  const handleStatusUpdate = async (status: string) => {
    // Implementation for status update
    const promises = selectedVendors.map(vendorId =>
      fetch(`/admin/vendor_management/vendors`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          id: vendorId, 
          activate: status === "active" 
        }),
      })
    );
    
    await Promise.all(promises);
  };

  const handleDelete = async () => {
    // Implementation for bulk delete
    await fetch(`/admin/vendor_management/vendors`, {
      method: "DELETE",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ ids: selectedVendors.join(",") }),
    });
  };

  const handleSendEmail = async () => {
    // Implementation for sending emails
    console.log("Sending email to vendors:", selectedVendors);
    console.log("Email data:", actionData);
  };

  const handleExport = async () => {
    // Implementation for export
    const params = new URLSearchParams();
    selectedVendors.forEach(id => params.append("vendor_ids", id));
    
    const response = await fetch(`/admin/vendor_management/vendors/export?${params.toString()}`);
    const blob = await response.blob();
    
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `vendors-${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  const handleUpdateCategory = async () => {
    // Implementation for category update
    const promises = selectedVendors.map(vendorId =>
      fetch(`/admin/vendor_management/vendors`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id: vendorId,
          categories: [actionData.category] // Use categories array instead
        }),
      })
    );

    await Promise.all(promises);
  };

  const renderActionForm = () => {
    if (!selectedAction) return null;

    switch (selectedAction.id) {
      case "send_email":
        return (
          <div className="space-y-4">
            <div>
              <Text size="small" weight="plus" className="mb-2">Subject</Text>
              <input
                type="text"
                className="w-full p-2 border rounded"
                placeholder="Email subject"
                value={actionData.subject || ""}
                onChange={(e) => setActionData({ ...actionData, subject: e.target.value })}
              />
            </div>
            <div>
              <Text size="small" weight="plus" className="mb-2">Message</Text>
              <Textarea
                placeholder="Email message"
                value={actionData.message || ""}
                onChange={(e) => setActionData({ ...actionData, message: e.target.value })}
                rows={4}
              />
            </div>
          </div>
        );
      
      case "update_category":
        return (
          <div>
            <Text size="small" weight="plus" className="mb-2">New Business Category</Text>
            <Select
              value={actionData.category || ""}
              onValueChange={(value) => setActionData({ ...actionData, category: value })}
            >
              <Select.Trigger>
                <Select.Value placeholder="Select category" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="housekeeping">Housekeeping</Select.Item>
                <Select.Item value="maintenance">Maintenance</Select.Item>
                <Select.Item value="catering">Catering</Select.Item>
                <Select.Item value="transportation">Transportation</Select.Item>
                <Select.Item value="laundry">Laundry</Select.Item>
                <Select.Item value="security">Security</Select.Item>
                <Select.Item value="landscaping">Landscaping</Select.Item>
                <Select.Item value="supplies">Supplies</Select.Item>
                <Select.Item value="other">Other</Select.Item>
              </Select.Content>
            </Select>
          </div>
        );
      
      default:
        return null;
    }
  };

  if (selectedVendors.length === 0) {
    return null;
  }

  return (
    <>
      <div className="flex items-center justify-between p-4 bg-ui-bg-subtle border rounded-lg">
        <div className="flex items-center space-x-3">
          <Badge variant="secondary">
            {selectedVendors.length} vendor{selectedVendors.length !== 1 ? "s" : ""} selected
          </Badge>
          <Button
            variant="transparent"
            size="small"
            onClick={onClearSelection}
          >
            Clear selection
          </Button>
        </div>
        
        <DropdownMenu>
          <DropdownMenu.Trigger asChild>
            <Button variant="secondary" size="small">
              <MoreHorizontal className="h-4 w-4" />
              Bulk Actions
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content align="end" className="w-56">
            {bulkActions.map((action) => (
              <DropdownMenu.Item
                key={action.id}
                onClick={() => handleActionSelect(action)}
                className="flex items-center space-x-2"
              >
                {action.icon}
                <span>{action.label}</span>
              </DropdownMenu.Item>
            ))}
          </DropdownMenu.Content>
        </DropdownMenu>
      </div>

      {/* Confirmation Modal */}
      <FocusModal open={showConfirmModal} onOpenChange={setShowConfirmModal}>
        <FocusModal.Content>
          <FocusModal.Header>
            <div className="flex items-center space-x-2">
              {selectedAction?.color === "danger" && (
                <AlertTriangle className="h-5 w-5 text-red-500" />
              )}
              <FocusModal.Title>
                {selectedAction?.label}
              </FocusModal.Title>
            </div>
          </FocusModal.Header>
          
          <FocusModal.Body className="space-y-4">
            <Text>{selectedAction?.description}</Text>
            
            <div className="p-3 bg-ui-bg-subtle rounded">
              <Text size="small" weight="plus">
                Selected vendors: {selectedVendors.length}
              </Text>
            </div>
            
            {renderActionForm()}
          </FocusModal.Body>
          
          <FocusModal.Footer>
            <div className="flex justify-end space-x-2">
              <Button
                variant="secondary"
                onClick={() => setShowConfirmModal(false)}
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button
                variant={selectedAction?.color === "danger" ? "danger" : "primary"}
                onClick={() => selectedAction && executeAction(selectedAction)}
                disabled={isProcessing}
              >
                {isProcessing ? "Processing..." : "Confirm"}
              </Button>
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>
    </>
  );
};

export default BulkOperations;
