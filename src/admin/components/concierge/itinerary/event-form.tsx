import React, { useState, useEffect } from "react";
import {
  Button,
  Input,
  Select,
  Textarea,
  Text,
} from "@camped-ai/ui";
import { MapPin, DollarSign } from "lucide-react";
import TimePicker from "./time-picker";

interface EventFormProps {
  initialData?: any;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

interface EventFormData {
  category: string;
  type: string;
  title: string;
  notes: string;
  start_time: string;
  end_time: string;
  duration: string;
  timezone: string;
  details: Record<string, any>;
  price: number | null;
  currency: string;
  media: string[];
}



const EventForm: React.FC<EventFormProps> = ({ initialData, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState<EventFormData>({
    category: "Activity",
    type: "",
    title: "",
    notes: "",
    start_time: "",
    end_time: "",
    duration: "",
    timezone: "",
    details: {},
    price: null,
    currency: "USD",
    media: [],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with existing data or template data
  useEffect(() => {
    if (initialData) {
      setFormData({
        category: initialData.category || "Activity",
        type: initialData.type || "",
        title: initialData.title || "",
        notes: initialData.notes || "",
        start_time: initialData.start_time || "",
        end_time: initialData.end_time || "",
        duration: initialData.duration || "",
        timezone: initialData.timezone || "",
        details: initialData.details || {},
        price: initialData.price || null,
        currency: initialData.currency || "USD",
        media: initialData.media || [],
      });
    }
  }, [initialData]);

  // Validation functions
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.title.trim()) {
      newErrors.title = "Event title is required";
    }

    if (!formData.category) {
      newErrors.category = "Category is required";
    }

    // Time validation
    if (formData.start_time && formData.end_time) {
      const start = new Date(`2000-01-01T${formData.start_time}`);
      const end = new Date(`2000-01-01T${formData.end_time}`);

      if (end <= start) {
        newErrors.end_time = "End time must be after start time";
      }
    }

    // Price validation
    if (formData.price !== null && formData.price < 0) {
      newErrors.price = "Price cannot be negative";
    }

    // Category-specific validation
    if (formData.category === "Flight") {
      if (formData.details.flight_number && !/^[A-Z]{2,3}\d{1,4}$/i.test(formData.details.flight_number)) {
        newErrors.flight_number = "Invalid flight number format (e.g., AA123)";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Clean up data before submission
      const submitData = {
        ...formData,
        price: formData.price || undefined,
        type: formData.type || undefined,
        notes: formData.notes || undefined,
        start_time: formData.start_time || undefined,
        end_time: formData.end_time || undefined,
        duration: formData.duration || undefined,
        timezone: formData.timezone || undefined,
        details: Object.keys(formData.details).length > 0 ? formData.details : undefined,
        media: Array.isArray(formData.media) ? formData.media : [],
      };

      console.log({submitData})

      await onSubmit(submitData);
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update form field with validation
  const updateField = (field: keyof EventFormData, value: any) => {
    // Handle clearing of optional fields
    const processedValue = value === undefined ? "" : value;
    setFormData(prev => ({ ...prev, [field]: processedValue }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Update details field with validation
  const updateDetails = (field: string, value: any) => {
    // Handle clearing of optional fields
    const processedValue = value === undefined ? "" : value;
    setFormData(prev => ({
      ...prev,
      details: { ...prev.details, [field]: processedValue }
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Get type options based on category
  const getTypeOptions = (category: string) => {
    const typeOptions: Record<string, string[]> = {
      Flight: ["Departure", "Arrival", "Connection"],
      Lodging: ["Check-in", "Check-out", "Room Service"],
      Activity: ["Food/Drink", "Sightseeing", "Entertainment", "Shopping", "Tour"],
      Cruise: ["Embarkation", "Disembarkation", "Port", "Sea Day"],
      Transport: ["Pickup", "Drop-off", "Transfer"],
      Info: ["General", "Important", "Emergency"],
    };
    return typeOptions[category] || [];
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-0 bg-white">

      {/* Category Tabs */}
      <EventCell title="Category">
        <div className="flex-1 flex gap-1 p-2 border-l border-gray-200">
          {[
            { value: "Activity", icon: "📅", label: "Activity" },
            { value: "Lodging", icon: "🏨", label: "Lodging" },
            { value: "Flight", icon: "✈️", label: "Flight" },
            { value: "Transport", icon: "🚗", label: "Transportation" },
            { value: "Cruise", icon: "🚢", label: "Cruise" },
            { value: "Info", icon: "ℹ️", label: "Info" }
          ].map((category) => (
            <button
              key={category.value}
              type="button"
              onClick={() => updateField("category", category.value)}
              className={`flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-all ${formData.category === category.value
                ? 'bg-blue-600 text-white shadow-sm'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              <span className="text-base">{category.icon}</span>
              <span>{category.label}</span>
            </button>
          ))}
        </div>
      </EventCell>


      {/* Sub-Category Tabs */}
      <EventCell title="Sub-Category">
        <div className="flex-1 flex gap-1 p-2 border-l border-gray-200">
          {getTypeOptions(formData.category).map((type) => (
            <button
              key={type}
              type="button"
              onClick={() => updateField("type", type)}
              className={`px-4 py-2 text-sm font-medium rounded-lg transition-all ${formData.type === type
                ? 'bg-blue-600 text-white shadow-sm'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              {type}
            </button>
          ))}
        </div>
      </EventCell>

      {/* Title Section */}
      <EventCell title="Title">
        <div className="w-full h-full flex-1">
          <Input
            placeholder="Add a title..."
            value={formData.title}
            onChange={(e) => updateField("title", e.target.value)}
            className={`text-base border-none border-0 rounded-none bg-white w-full h-[52px] focus:ring-0 ${errors.title ? 'border-red-500' : ''}`}
          />
          {errors.title && (
            <Text className="text-xs text-red-600 mt-1">{errors.title}</Text>
          )}
        </div>
      </EventCell>



      {/* Rich Text Editor for Notes */}
      <EventCell title="Notes">
        <Textarea
          placeholder="To add a single line use shift+enter"
          value={formData.notes}
          onChange={(e) => updateField("notes", e.target.value)}
          rows={4}
          className="border-none border-0 rounded-none resize-none focus:ring-0 w-full h-[100px] flex-1 bg-white"
        />
      </EventCell>


      {/* Timing & Schedule Section */}
      <TimePicker
        startTime={formData.start_time}
        endTime={formData.end_time}
        duration={formData.duration}
        timezone={formData.timezone}
        onStartTimeChange={(time) => updateField("start_time", time)}
        onEndTimeChange={(time) => updateField("end_time", time)}
        onDurationChange={(duration) => updateField("duration", duration)}
        onTimezoneChange={(timezone) => updateField("timezone", timezone)}
      />

      {/* Category-Specific Details */}
      {(formData.category === "Flight" || formData.category === "Lodging" || formData.category === "Activity" || formData.category === "Cruise") && (
        <div className="bg-green-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-lg">
              {formData.category === "Flight" && "✈️"}
              {formData.category === "Lodging" && "🛏️"}
              {formData.category === "Activity" && "🎯"}
              {formData.category === "Cruise" && "🚢"}
            </span>
            <Text className="text-sm font-medium text-gray-900">
              {formData.category} Details
            </Text>
          </div>

          {formData.category === "Flight" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Flight Number</label>
                <Input
                  placeholder="e.g., AA123"
                  value={formData.details.flight_number || ""}
                  onChange={(e) => updateDetails("flight_number", e.target.value)}
                  className={errors.flight_number ? 'border-red-500' : ''}
                />
                {errors.flight_number && (
                  <Text className="text-xs text-red-600 mt-1">{errors.flight_number}</Text>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Airline</label>
                <Input
                  placeholder="e.g., American Airlines"
                  value={formData.details.airline || ""}
                  onChange={(e) => updateDetails("airline", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Departure Airport</label>
                <Input
                  placeholder="e.g., JFK"
                  value={formData.details.departure_airport || ""}
                  onChange={(e) => updateDetails("departure_airport", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Arrival Airport</label>
                <Input
                  placeholder="e.g., LAX"
                  value={formData.details.arrival_airport || ""}
                  onChange={(e) => updateDetails("arrival_airport", e.target.value)}
                />
              </div>
            </div>
          )}

          {formData.category === "Lodging" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Hotel Name</label>
                <Input
                  placeholder="Hotel name"
                  value={formData.details.hotel_name || ""}
                  onChange={(e) => updateDetails("hotel_name", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Room Number</label>
                <Input
                  placeholder="Room number"
                  value={formData.details.room_number || ""}
                  onChange={(e) => updateDetails("room_number", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Check-in Time</label>
                <Input
                  type="time"
                  value={formData.details.checkin_time || ""}
                  onChange={(e) => updateDetails("checkin_time", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Check-out Time</label>
                <Input
                  type="time"
                  value={formData.details.checkout_time || ""}
                  onChange={(e) => updateDetails("checkout_time", e.target.value)}
                />
              </div>
            </div>
          )}

          {formData.category === "Activity" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Activity Provider</label>
                <Input
                  placeholder="e.g., Local Tours Inc."
                  value={formData.details.provider || ""}
                  onChange={(e) => updateDetails("provider", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty Level</label>
                <div className="flex gap-2">
                  <Select
                    value={formData.details.difficulty || undefined}
                    onValueChange={(value) => updateDetails("difficulty", value)}
                  >
                    <Select.Trigger className="flex-1">
                      <Select.Value placeholder="Select difficulty..." />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="Easy">Easy</Select.Item>
                      <Select.Item value="Moderate">Moderate</Select.Item>
                      <Select.Item value="Challenging">Challenging</Select.Item>
                    </Select.Content>
                  </Select>
                  {formData.details.difficulty && (
                    <Button
                      type="button"
                      variant="secondary"
                      size="small"
                      onClick={() => updateDetails("difficulty", "")}
                      className="px-2"
                    >
                      Clear
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}

          {formData.category === "Cruise" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Port Name</label>
                <Input
                  placeholder="e.g., Port of Miami"
                  value={formData.details.port_name || ""}
                  onChange={(e) => updateDetails("port_name", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Deck/Location</label>
                <Input
                  placeholder="e.g., Deck 7, Pool Area"
                  value={formData.details.deck_location || ""}
                  onChange={(e) => updateDetails("deck_location", e.target.value)}
                />
              </div>
            </div>
          )}
        </div>
      )}

      {/* Location & Pricing Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2">
        {/* Location */}
        <div className="bg-orange-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <MapPin className="h-4 w-4 text-orange-600" />
            <Text className="text-sm font-medium text-gray-900">Location Details</Text>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
            <Input
              placeholder="Enter specific location or address"
              value={formData.details.location || ""}
              onChange={(e) => updateDetails("location", e.target.value)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Meeting Point</label>
            <Input
              placeholder="Where to meet (optional)"
              value={formData.details.meeting_point || ""}
              onChange={(e) => updateDetails("meeting_point", e.target.value)}
            />
          </div>
        </div>

        {/* Pricing */}
        <div className="bg-purple-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <DollarSign className="h-4 w-4 text-purple-600" />
            <Text className="text-sm font-medium text-gray-900">Pricing Information</Text>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price</label>
              <Input
                type="number"
                placeholder="0.00"
                value={formData.price || ""}
                onChange={(e) => updateField("price", e.target.value ? parseFloat(e.target.value) : null)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
              <Select
                value={formData.currency}
                onValueChange={(value) => updateField("currency", value)}
              >
                <Select.Trigger>
                  <Select.Value placeholder="Select currency..." />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="USD">USD ($)</Select.Item>
                  <Select.Item value="EUR">EUR (€)</Select.Item>
                  <Select.Item value="GBP">GBP (£)</Select.Item>
                  <Select.Item value="CHF">CHF</Select.Item>
                </Select.Content>
              </Select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Price Type</label>
            <div className="flex gap-2">
              <Select
                value={formData.details.price_type || undefined}
                onValueChange={(value) => updateDetails("price_type", value)}
              >
                <Select.Trigger className="flex-1">
                  <Select.Value placeholder="Select price type..." />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="per_person">Per Person</Select.Item>
                  <Select.Item value="per_group">Per Group</Select.Item>
                  <Select.Item value="total">Total Cost</Select.Item>
                  <Select.Item value="included">Included</Select.Item>
                </Select.Content>
              </Select>
              {formData.details.price_type && (
                <Button
                  type="button"
                  variant="secondary"
                  size="small"
                  onClick={() => updateDetails("price_type", "")}
                  className="px-2"
                >
                  Clear
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>


      {/* Multimedia Section */}
      <EventCell title="Multimedia">
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center bg-gray-50 flex-1">
          <div className="flex flex-col items-center gap-2">
            <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-2xl">📷</span>
            </div>
            <p className="text-sm text-gray-600">Click to Add up to 5 Photos or Videos</p>
          </div>
          <input
            type="file"
            multiple
            accept="image/*,video/*"
            className="hidden"
            onChange={(e) => {
              const files = Array.from(e.target.files || []);
              updateField("media", files.slice(0, 5));
            }}
          />
        </div>
      </EventCell>

      {/* Form Actions */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <div className="flex items-center gap-2">
        </div>

        <div className="flex gap-3">
          <Button type="button" variant="secondary" onClick={onCancel} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 text-white"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                "Saving..."
              </div>
            ) : (
              "✓ Done Editing"
            )}
          </Button>
        </div>
      </div>
    </form>
  );
};

const EventCell = ({ children, title }: any) => {
  return (
    <div className="flex border-b border-gray-200">
      <div className="w-40 flex items-center gap-1 text-sm text-gray-600 px-6">
        <span className="font-bold">{title}</span>
      </div>
      {children}
    </div>

  )
}

export default EventForm;
