import React, { useState } from "react";
import { Combobox } from "../common/combobox";
import { useQuery } from "@tanstack/react-query";
import { User } from "lucide-react";

interface UserOption {
  value: string;
  label: string;
  email?: string;
}

interface UserSelectorProps {
  value?: string;
  onChange?: (value?: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  error?: string;
}

// API function to fetch users
const fetchUsers = async (): Promise<UserOption[]> => {
  try {
    const response = await fetch("/admin/users/roles?limit=100", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
    });

    if (!response.ok) {
      // If the roles endpoint fails, try a fallback approach
      console.warn("Users/roles endpoint failed, using fallback");
      return [
        { value: "admin", label: "Admin User", email: "<EMAIL>" },
        { value: "manager", label: "Manager", email: "<EMAIL>" },
        { value: "staff", label: "Staff Member", email: "<EMAIL>" },
      ];
    }

    const data = await response.json();

    // Transform users into options
    const users = data.users || [];
    return users.map((user: any) => ({
      value: user.id,
      label: user.email || `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.id,
      email: user.email,
    }));
  } catch (error) {
    console.error("Error fetching users:", error);
    // Return fallback users if API fails
    return [
      { value: "admin", label: "Admin User", email: "<EMAIL>" },
      { value: "manager", label: "Manager", email: "<EMAIL>" },
      { value: "staff", label: "Staff Member", email: "<EMAIL>" },
    ];
  }
};

export const UserSelector: React.FC<UserSelectorProps> = ({
  value,
  onChange,
  placeholder = "Select a user...",
  disabled = false,
  className = "",
  error,
}) => {
  const [searchValue, setSearchValue] = useState("");

  // Fetch users
  const { data: users = [], isLoading, error: fetchError } = useQuery({
    queryKey: ["users", "selector"],
    queryFn: fetchUsers,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    cacheTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });

  // Filter users based on search
  const filteredUsers = users.filter((user) =>
    user.label.toLowerCase().includes(searchValue.toLowerCase()) ||
    (user.email && user.email.toLowerCase().includes(searchValue.toLowerCase()))
  );

  // Add "Unassigned" option at the top
  const options = [
    { value: "", label: "Unassigned" },
    ...filteredUsers,
  ];

  return (
    <Combobox
      value={value}
      onChange={onChange}
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      options={options}
      placeholder={placeholder}
      disabled={disabled || isLoading}
      className={className}
      noResultsPlaceholder={
        <div className="flex items-center gap-x-2 rounded-[4px] px-2 py-1.5">
          <User className="h-4 w-4 text-ui-fg-subtle" />
          <span className="text-ui-fg-subtle text-sm">
            {searchValue ? `No users found matching "${searchValue}"` : "No users available"}
          </span>
        </div>
      }
    />
  );
};

export default UserSelector;
