import React, { useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Button,
  Input,
  Textarea,
  Select,
  Label,
  FocusModal,
  Heading,
  Text,
  toast,
} from "@camped-ai/ui";
import { useTranslation } from "react-i18next";
import {
  useCreateConciergeTask,
  useCreateBookingTask,
  useUpdateConciergeTask,
  ConciergeTask,
} from "../../hooks/api/concierge-tasks";
import { UserSelector } from "./user-selector";
import "../../styles/task-modal.css";

// Form validation schema
const taskFormSchema = z.object({
  title: z.string()
    .min(1, "Title is required")
    .max(255, "Title must be less than 255 characters")
    .trim(),
  description: z.string()
    .max(1000, "Description must be less than 1000 characters")
    .optional()
    .or(z.literal("")),
  status: z.enum(["pending", "in_progress", "review", "completed", "cancelled"], {
    errorMap: () => ({ message: "Please select a valid status" })
  }).default("pending"),
  priority: z.enum(["low", "medium", "high", "urgent"], {
    errorMap: () => ({ message: "Please select a valid priority" })
  }).default("medium"),
  entity_type: z.string().optional().or(z.literal("")),
  entity_id: z.string().optional().or(z.literal("")),
  assigned_to: z.string()
    .max(100, "Assigned to field must be less than 100 characters")
    .optional()
    .or(z.literal("")),
  due_date: z.string()
    .optional()
    .or(z.literal(""))
    .refine((date) => {
      if (!date) return true; // Optional field
      const parsedDate = new Date(date);
      return !isNaN(parsedDate.getTime());
    }, "Please enter a valid date"),
});

type TaskFormData = z.infer<typeof taskFormSchema>;

interface TaskFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  task?: ConciergeTask; // For editing existing tasks
  bookingId?: string; // For creating tasks linked to a booking
  entityType?: string; // For creating tasks linked to other entities
  entityId?: string;
  bookingDisplayId?: string; // For showing booking context
}

export const TaskFormModal: React.FC<TaskFormModalProps> = ({
  isOpen,
  onClose,
  task,
  bookingId,
  entityType,
  entityId,
  bookingDisplayId,
}) => {
  const { t } = useTranslation();
  const isEditing = !!task;
  const isBookingContext = !!bookingId;

  // Choose the appropriate mutation based on context
  const createTaskMutation = useCreateConciergeTask();
  const createBookingTaskMutation = useCreateBookingTask(bookingId || "");
  const updateTaskMutation = useUpdateConciergeTask();

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<TaskFormData>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      title: task?.title || "",
      description: task?.description || "",
      status: task?.status || "pending",
      priority: task?.priority || "medium",
      entity_type: task?.entity_type || (isBookingContext ? "booking" : entityType) || "",
      entity_id: task?.entity_id || (isBookingContext ? bookingId : entityId) || "",
      assigned_to: task?.assigned_to || "",
      due_date: task?.due_date ? new Date(task.due_date).toISOString().split('T')[0] : "",
    },
  });

  // Auto-populate entity fields when in booking context
  useEffect(() => {
    if (isBookingContext && !isEditing) {
      setValue("entity_type", "booking");
      setValue("entity_id", bookingId);
    }
  }, [isBookingContext, isEditing, bookingId, setValue]);

  const onSubmit = async (data: TaskFormData) => {
    try {
      const taskData = {
        ...data,
        due_date: data.due_date ? new Date(data.due_date).toISOString() : undefined,
        // Ensure entity fields are set for booking context
        entity_type: isBookingContext && !isEditing ? "booking" : data.entity_type,
        entity_id: isBookingContext && !isEditing ? bookingId : data.entity_id,
      };

      if (isEditing && task) {
        // Update existing task
        await updateTaskMutation.mutateAsync({
          taskId: task.id,
          data: taskData,
        });
        toast.success("Task updated successfully");
      } else if (bookingId) {
        // Create task for booking
        await createBookingTaskMutation.mutateAsync(taskData);
        toast.success("Task created successfully");
      } else {
        // Create general task
        await createTaskMutation.mutateAsync(taskData);
        toast.success("Task created successfully");
      }

      reset();
      onClose();
    } catch (error) {
      console.error("Error saving task:", error);
      toast.error("Failed to save task. Please try again.");
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <FocusModal open={isOpen} onOpenChange={handleClose}>
      <FocusModal.Content className="task-modal-content">
        <FocusModal.Header className="task-modal-header">
          <Heading level="h2">
            {isEditing ? "Edit Task" : "Create New Task"}
          </Heading>
          {isBookingContext && !isEditing && (
            <Text className="text-ui-fg-subtle">
              📋 Linked to booking {bookingDisplayId || bookingId}
            </Text>
          )}
        </FocusModal.Header>

        <form onSubmit={handleSubmit(onSubmit)} className={isSubmitting ? "task-form-loading" : ""}>
          <FocusModal.Body className="task-modal-body">
            {/* Title */}
            <div className="task-form-field">
              <Label htmlFor="title" className="task-form-label">
                Title <span className="task-form-required">*</span>
              </Label>
              <Input
                id="title"
                {...register("title")}
                placeholder="Enter task title"
                className={`task-form-input ${errors.title ? 'error' : ''}`}
                disabled={isSubmitting}
              />
              {errors.title && (
                <span className="task-form-error">{errors.title.message}</span>
              )}
            </div>

            {/* Description */}
            <div className="task-form-field">
              <Label htmlFor="description" className="task-form-label">Description</Label>
              <Textarea
                id="description"
                {...register("description")}
                placeholder="Enter task description (optional)"
                rows={3}
                className={`task-form-textarea ${errors.description ? 'error' : ''}`}
                disabled={isSubmitting}
              />
              {errors.description && (
                <span className="task-form-error">{errors.description.message}</span>
              )}
            </div>

            {/* Status and Priority Row */}
            <div className="task-form-grid">
              <div className="task-form-field">
                <Label htmlFor="status" className="task-form-label">Status</Label>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={isSubmitting}
                    >
                      <Select.Trigger className={`w-full ${errors.status ? 'border-red-500' : ''}`}>
                        <Select.Value placeholder="Select status" />
                      </Select.Trigger>
                      <Select.Content>
                        <Select.Item value="pending">Pending</Select.Item>
                        <Select.Item value="in_progress">In Progress</Select.Item>
                        <Select.Item value="review">Review</Select.Item>
                        <Select.Item value="completed">Completed</Select.Item>
                        <Select.Item value="cancelled">Cancelled</Select.Item>
                      </Select.Content>
                    </Select>
                  )}
                />
                {errors.status && (
                  <span className="task-form-error">{errors.status.message}</span>
                )}
              </div>

              <div className="task-form-field">
                <Label htmlFor="priority" className="task-form-label">Priority</Label>
                <Controller
                  name="priority"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={isSubmitting}
                    >
                      <Select.Trigger className={`w-full ${errors.priority ? 'border-red-500' : ''}`}>
                        <Select.Value placeholder="Select priority" />
                      </Select.Trigger>
                      <Select.Content>
                        <Select.Item value="low">Low</Select.Item>
                        <Select.Item value="medium">Medium</Select.Item>
                        <Select.Item value="high">High</Select.Item>
                        <Select.Item value="urgent">Urgent</Select.Item>
                      </Select.Content>
                    </Select>
                  )}
                />
                {errors.priority && (
                  <span className="task-form-error">{errors.priority.message}</span>
                )}
              </div>
            </div>

            {/* Entity Information (hidden when in booking context or editing) */}
            {!isEditing && !isBookingContext && (
              <div className="task-form-grid">
                <div className="task-form-field">
                  <Label htmlFor="entity_type" className="task-form-label">Entity Type</Label>
                  <Controller
                    name="entity_type"
                    control={control}
                    render={({ field }) => (
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <Select.Trigger className={`w-full ${errors.entity_type ? 'border-red-500' : ''}`}>
                          <Select.Value placeholder="Select entity type" />
                        </Select.Trigger>
                        <Select.Content>
                          <Select.Item value="">General Task</Select.Item>
                          <Select.Item value="booking">Booking</Select.Item>
                          <Select.Item value="deal">Deal</Select.Item>
                          <Select.Item value="guest">Guest</Select.Item>
                          <Select.Item value="itinerary">Itinerary</Select.Item>
                        </Select.Content>
                      </Select>
                    )}
                  />
                  {errors.entity_type && (
                    <span className="task-form-error">{errors.entity_type.message}</span>
                  )}
                </div>

                <div className="task-form-field">
                  <Label htmlFor="entity_id" className="task-form-label">Entity ID</Label>
                  <Input
                    id="entity_id"
                    {...register("entity_id")}
                    placeholder="Enter entity ID (optional)"
                    className={`task-form-input ${errors.entity_id ? 'error' : ''}`}
                    disabled={isSubmitting}
                  />
                  {errors.entity_id && (
                    <span className="task-form-error">{errors.entity_id.message}</span>
                  )}
                </div>
              </div>
            )}

            {/* Assignment and Due Date Row */}
            <div className="task-form-grid">
              <div className="task-form-field">
                <Label htmlFor="assigned_to" className="task-form-label">Assigned To</Label>
                <UserSelector
                  value={watch("assigned_to")}
                  onChange={(value) => setValue("assigned_to", value || "")}
                  placeholder="Select a user (optional)"
                  disabled={isSubmitting}
                  className={`task-form-input ${errors.assigned_to ? 'error' : ''}`}
                />
                {errors.assigned_to && (
                  <span className="task-form-error">{errors.assigned_to.message}</span>
                )}
              </div>

              <div className="task-form-field">
                <Label htmlFor="due_date" className="task-form-label">Due Date</Label>
                <Input
                  id="due_date"
                  type="date"
                  {...register("due_date")}
                  className={`task-form-input ${errors.due_date ? 'error' : ''}`}
                  disabled={isSubmitting}
                />
                {errors.due_date && (
                  <span className="task-form-error">{errors.due_date.message}</span>
                )}
              </div>
            </div>
          </FocusModal.Body>

          <FocusModal.Footer className="task-modal-footer">
            <div className="task-form-buttons">
              <Button
                type="button"
                variant="secondary"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                loading={isSubmitting}
              >
                {isEditing ? "Update Task" : "Create Task"}
              </Button>
            </div>
          </FocusModal.Footer>
        </form>
      </FocusModal.Content>
    </FocusModal>
  );
};
