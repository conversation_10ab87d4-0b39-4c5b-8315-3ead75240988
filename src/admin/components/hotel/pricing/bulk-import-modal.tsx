import React, { useState, useRef } from "react";
import { Button, FocusModal, Text, Heading, toast ,} from "@camped-ai/ui";
import {
  Upload,
  Download,
  AlertCircle,
  CheckCircle,
  FileIcon,
  X,
} from "lucide-react";
import * as XLSX from "xlsx";
import {
  transformExcelDataToApiFormat,
  executeBulkImport,
  validateExcelData,
  type ExcelRowData,
} from "../../../utils/bulk-import-transformer";
import ImportPreviewTable from "./import-preview-table";

export type BulkImportError = {
  row: number;
  field: string;
  message: string;
  value?: any;
};

export type BulkImportResult = {
  success: boolean;
  message: string;
  imported: number;
  errors: BulkImportError[];
};

type BulkImportModalProps = {
  open: boolean;
  onClose: () => void;
  hotelId: string;
  currentCurrency: string;
  roomConfigs: any[];
  occupancyConfigs: any[];
  mealPlans: any[];
  seasonalPeriods: any[];
  onImportComplete?: () => void;
};

const BulkImportModal: React.FC<BulkImportModalProps> = ({
  open,
  onClose,
  hotelId,
  currentCurrency,
  roomConfigs,
  occupancyConfigs,
  mealPlans,
  seasonalPeriods,
  onImportComplete,
}) => {
  const [importFile, setImportFile] = useState<File | null>(null);
  const [parsedData, setParsedData] = useState<any[]>([]);
  const [importErrors, setImportErrors] = useState<BulkImportError[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSuccess, setImportSuccess] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Reset state when modal opens/closes
  React.useEffect(() => {
    if (!open) {
      setImportFile(null);
      setParsedData([]);
      setImportErrors([]);
      setIsProcessing(false);
      setImportProgress(0);
      setImportSuccess(false);
      setShowPreview(false);
    }
  }, [open]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportFile(file);
      parseExcelFile(file);
    }
  };

  const parseExcelFile = async (file: File) => {
    setIsProcessing(true);
    setImportErrors([]);

    try {
      const buffer = await file.arrayBuffer();
      const workbook = XLSX.read(buffer, { type: "array" });

      // Get the first sheet
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) {
        throw new Error("No sheets found in the uploaded file");
      }

      const worksheet = workbook.Sheets[sheetName];
      const rawData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
      }) as any[][];

      if (rawData.length < 2) {
        throw new Error(
          "File must contain at least a header row and one data row"
        );
      }

      // Parse headers and data
      const headers = rawData[0] as string[];
      const dataRows = rawData.slice(1);

      console.log("[Bulk Import] Headers found:", headers);
      console.log("[Bulk Import] Data rows:", dataRows.length);

      // Validate required columns
      const requiredColumns = [
        "room_config_name",
        "occupancy_name",
        "meal_plan_name",
        "currency_code",
      ];

      const missingColumns = requiredColumns.filter(
        (col) => !headers.some((header) => normalizeColumnName(header) === col)
      );

      if (missingColumns.length > 0) {
        throw new Error(
          `Missing required columns: ${missingColumns.join(", ")}`
        );
      }

      // Parse and validate data
      const parsedRows = [];
      const errors: BulkImportError[] = [];

      for (let i = 0; i < dataRows.length; i++) {
        const rowData = dataRows[i];
        if (
          !rowData ||
          rowData.every((cell) => !cell || cell.toString().trim() === "")
        ) {
          continue; // Skip empty rows
        }

        const row: any = {};
        headers.forEach((header, index) => {
          const normalizedHeader = normalizeColumnName(header);
          row[normalizedHeader] = rowData[index]?.toString().trim() || "";
        });

        // Validate row
        const rowValidation = validateImportRow(row, i + 2); // +2 for header and 0-based index
        if (rowValidation.isValid) {
          parsedRows.push(row);
        } else {
          errors.push(...rowValidation.errors);
        }
      }

      console.log("[Bulk Import] Parsed rows:", parsedRows.length);
      console.log("[Bulk Import] Validation errors:", errors.length);

      setParsedData(parsedRows);
      setImportErrors(errors);

      if (parsedRows.length === 0) {
        toast.error("No valid data found in the file");
        setShowPreview(false);
      } else {
        toast.success(`Parsed ${parsedRows.length} rows successfully`);
        setShowPreview(true);
      }
    } catch (error) {
      console.error("Error parsing Excel file:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to parse file"
      );
      setImportErrors([
        {
          row: 0,
          field: "file",
          message:
            error instanceof Error ? error.message : "Failed to parse file",
        },
      ]);
    } finally {
      setIsProcessing(false);
    }
  };

  // Normalize column names to match expected format
  const normalizeColumnName = (header: string): string => {
    return header
      .toLowerCase()
      .replace(/\s+/g, "_")
      .replace(/[^\w]/g, "")
      .replace(/_+/g, "_")
      .replace(/^_|_$/g, "");
  };

  // Helper function to normalize meal plan names
  const normalizeMealPlanName = (
    mealPlanName: string | undefined | null
  ): string | null => {
    if (!mealPlanName || typeof mealPlanName !== "string") return null;

    const normalized = mealPlanName.trim().toLowerCase();

    // Common variations that mean "no meal plan"
    // NOTE: "No Meals" is a real meal plan name, not a null indicator
    const noMealPlanVariations = ["-", "none", "n/a", "na", ""];

    if (noMealPlanVariations.includes(normalized)) {
      return null;
    }

    return mealPlanName.trim();
  };

  // Validate individual row
  const validateImportRow = (row: any, rowNumber: number) => {
    const errors: BulkImportError[] = [];

    // Check required fields
    if (!row.room_config_name) {
      errors.push({
        row: rowNumber,
        field: "room_config_name",
        message: "Room config name is required",
      });
    }
    if (!row.occupancy_name) {
      errors.push({
        row: rowNumber,
        field: "occupancy_name",
        message: "Occupancy name is required",
      });
    }
    if (!row.meal_plan_name) {
      errors.push({
        row: rowNumber,
        field: "meal_plan_name",
        message: "Meal plan name is required",
      });
    }
    if (!row.currency_code) {
      errors.push({
        row: rowNumber,
        field: "currency_code",
        message: "Currency code is required",
      });
    }

    // Validate currency matches current currency
    if (row.currency_code && row.currency_code !== currentCurrency) {
      errors.push({
        row: rowNumber,
        field: "currency_code",
        message: `Currency ${row.currency_code} does not match current currency ${currentCurrency}`,
      });
    }

    // Validate room config exists
    if (
      row.room_config_name &&
      !roomConfigs.find((rc) => rc.title === row.room_config_name)
    ) {
      errors.push({
        row: rowNumber,
        field: "room_config_name",
        message: `Room config "${row.room_config_name}" not found`,
      });
    }

    // Validate occupancy config exists
    if (
      row.occupancy_name &&
      !occupancyConfigs.find((oc) => oc.name === row.occupancy_name)
    ) {
      errors.push({
        row: rowNumber,
        field: "occupancy_name",
        message: `Occupancy config "${row.occupancy_name}" not found`,
      });
    }

    // Validate meal plan exists (normalize meal plan name first)
    const normalizedMealPlanName = normalizeMealPlanName(row.meal_plan_name);
    if (
      normalizedMealPlanName &&
      !mealPlans.find((mp) => mp.name === normalizedMealPlanName)
    ) {
      errors.push({
        row: rowNumber,
        field: "meal_plan_name",
        message: `Meal plan "${row.meal_plan_name}" not found`,
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  const handleImport = async () => {
    if (parsedData.length === 0) return;

    setIsProcessing(true);
    setImportProgress(0);

    try {
      // Transform parsed data to match the existing API structure
      const transformedData = await transformToApiFormat(parsedData);

      // Execute bulk import using existing APIs
      const result = await executeBulkImportOperation(transformedData);

      setImportSuccess(true);
      toast.success(
        `Successfully imported ${result.results.length} pricing records`
      );

      // Close modal after successful import
      setTimeout(() => {
        onImportComplete?.();
        onClose();
      }, 1500);
    } catch (error) {
      console.error("Import failed:", error);
      toast.error(error instanceof Error ? error.message : "Import failed");
    } finally {
      setIsProcessing(false);
    }
  };

  const transformToApiFormat = async (data: ExcelRowData[]) => {
    console.log("[Bulk Import Modal] Starting data transformation...");
    console.log("[Bulk Import Modal] Input data:", data);

    // Validate data before transformation
    const validation = validateExcelData(
      data,
      roomConfigs,
      occupancyConfigs,
      mealPlans
    );
    if (!validation.isValid) {
      console.error(
        "[Bulk Import Modal] Validation failed:",
        validation.errors
      );
      throw new Error(`Validation failed: ${validation.errors.join(", ")}`);
    }

    console.log("[Bulk Import Modal] Validation passed, transforming data...");

    // Transform Excel data to API format
    const transformedData = transformExcelDataToApiFormat(
      data,
      roomConfigs,
      occupancyConfigs,
      mealPlans,
      seasonalPeriods
    );

    console.log(
      "[Bulk Import Modal] Transformation complete:",
      transformedData
    );

    return transformedData;
  };

  const executeBulkImportOperation = async (transformedData: any) => {
    console.log("[Bulk Import Modal] Starting API execution...");
    console.log(
      "[Bulk Import Modal] Transformed data to be sent:",
      transformedData
    );

    // Execute bulk import using existing APIs
    const result = await executeBulkImport(transformedData, (progress) => {
      console.log(`[Bulk Import Modal] Progress update: ${progress}%`);
      setImportProgress(progress);
    });

    console.log("[Bulk Import Modal] API execution result:", result);

    if (!result.success) {
      console.error("[Bulk Import Modal] Import failed:", result.errors);
      throw new Error(
        `Import failed: ${result.errors.map((e) => e.error).join(", ")}`
      );
    }

    console.log("[Bulk Import Modal] Import completed successfully");
    return result;
  };

  return (
    <FocusModal open={open} onOpenChange={onClose}>
      <FocusModal.Content>
        <FocusModal.Header>
          <Heading level="h2">
            {showPreview ? "Preview Import Data" : "Bulk Import Pricing Data"}
          </Heading>
        </FocusModal.Header>

        <FocusModal.Body className="p-6 overflow-y-auto">
          {/* File Upload Section */}
          {!showPreview && (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                ref={fileInputRef}
                type="file"
                accept=".xlsx,.xls,.csv"
                onChange={handleFileSelect}
                className="hidden"
              />

              {!importFile ? (
                <div>
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <Text className="mt-2 text-sm text-gray-600">
                    Upload Excel or CSV file with pricing data
                  </Text>
                  <Button
                    variant="secondary"
                    onClick={() => fileInputRef.current?.click()}
                    className="mt-4"
                  >
                    Choose File
                  </Button>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <FileIcon className="h-8 w-8 text-blue-500" />
                  <Text>{importFile.name}</Text>
                  <Button size="small" onClick={() => setImportFile(null)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Progress and Status */}
          {isProcessing && (
            <div className="space-y-2">
              <Text className="text-sm text-gray-600">Processing...</Text>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${importProgress}%` }}
                ></div>
              </div>
              <Text className="text-xs text-gray-500 text-center">
                {importProgress}% complete
              </Text>
            </div>
          )}

          {/* Results Summary */}
          {parsedData.length > 0 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <Text className="text-green-800">
                  Found {parsedData.length} valid pricing records
                </Text>
              </div>
            </div>
          )}

          {/* Errors Summary */}
          {importErrors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                <Text className="text-red-800">
                  {importErrors.length} validation errors found
                </Text>
              </div>
              <div className="max-h-32 overflow-y-auto">
                {importErrors.slice(0, 5).map((error, index) => (
                  <Text key={index} className="text-sm text-red-600">
                    Row {error.row}: {error.message}
                  </Text>
                ))}
                {importErrors.length > 5 && (
                  <Text className="text-sm text-red-600">
                    ... and {importErrors.length - 5} more errors
                  </Text>
                )}
              </div>
            </div>
          )}

          {/* Preview Summary */}
          {showPreview && parsedData.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <div className="flex items-center justify-between">
                <div>
                  <Text className="font-medium text-blue-800">
                    Ready to Import: {parsedData.length} pricing records
                  </Text>
                  <Text className="text-sm text-blue-600">
                    File: {importFile?.name} • Currency: {currentCurrency}
                  </Text>
                </div>
                {importErrors.length === 0 && (
                  <CheckCircle className="h-8 w-8 text-green-500" />
                )}
              </div>
            </div>
          )}

          {/* Preview Table */}
          {showPreview && parsedData.length > 0 && (
            <div className="mt-6">
              <ImportPreviewTable
                importData={parsedData}
                currentCurrency={currentCurrency}
                maxHeight="400px"
                validationErrors={importErrors.map((error) => ({
                  row: error.row - 2, // Adjust for header row and 0-based indexing
                  field: error.field,
                  message: error.message,
                  value: error.value,
                }))}
              />
            </div>
          )}
        </FocusModal.Body>

        <FocusModal.Footer>
          <div className="flex justify-between w-full">
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>

            <div className="flex gap-2">
              {showPreview && (
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowPreview(false);
                    setImportFile(null);
                    setParsedData([]);
                    setImportErrors([]);
                  }}
                >
                  Back to Upload
                </Button>
              )}
              <Button
                variant="primary"
                onClick={handleImport}
                disabled={
                  parsedData.length === 0 ||
                  isProcessing ||
                  importErrors.length > 0
                }
              >
                {isProcessing
                  ? "Importing..."
                  : `Import ${parsedData.length} Records`}
              </Button>
            </div>
          </div>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default BulkImportModal;
