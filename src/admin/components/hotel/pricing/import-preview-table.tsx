import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON>, Text } from "@camped-ai/ui";
import { ChevronDown, ChevronUp } from "lucide-react";

// Types for the import preview - flexible to handle any columns
type ImportPreviewRow = Record<string, any>;

type ImportValidationError = {
  row: number;
  field: string;
  message: string;
  value?: any;
};

type ImportPreviewTableProps = {
  importData: ImportPreviewRow[];
  currentCurrency: string;
  maxHeight?: string;
  validationErrors?: ImportValidationError[];
};

const ImportPreviewTable: React.FC<ImportPreviewTableProps> = ({
  importData,
  currentCurrency,
  maxHeight = "400px",
  validationErrors = [],
}) => {
  const [showCostMargin, setShowCostMargin] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  // Get all available columns from the import data
  const availableColumns =
    importData.length > 0 ? Object.keys(importData[0]) : [];

  // Define column categories for better organization
  const coreColumns = [
    "room_config_name",
    "occupancy_name",
    "meal_plan_name",
    "currency_code",
  ];
  const priceColumns = [
    "monday_price",
    "tuesday_price",
    "wednesday_price",
    "thursday_price",
    "friday_price",
    "saturday_price",
    "sunday_price",
  ];
  const defaultCostMarginColumns = [
    "default_gross_cost",
    "default_fixed_margin",
    "default_margin_percentage",
  ];
  const weekdayCostColumns = [
    "monday_gross_cost",
    "tuesday_gross_cost",
    "wednesday_gross_cost",
    "thursday_gross_cost",
    "friday_gross_cost",
    "saturday_gross_cost",
    "sunday_gross_cost",
  ];
  const weekdayMarginColumns = [
    "monday_fixed_margin",
    "tuesday_fixed_margin",
    "wednesday_fixed_margin",
    "thursday_fixed_margin",
    "friday_fixed_margin",
    "saturday_fixed_margin",
    "sunday_fixed_margin",
  ];
  const weekdayMarginPercentColumns = [
    "monday_margin_percentage",
    "tuesday_margin_percentage",
    "wednesday_margin_percentage",
    "thursday_margin_percentage",
    "friday_margin_percentage",
    "saturday_margin_percentage",
    "sunday_margin_percentage",
  ];
  const metadataColumns = [
    "hotel_name",
    "hotel_id",
    "room_config_id",
    "room_type",
    "max_occupancy",
    "max_cots",
    "occupancy_adults",
    "occupancy_children",
    "occupancy_infants",
    "meal_plan_type",
    "pricing_type",
    "seasonal_period",
    "seasonal_start_date",
    "seasonal_end_date",
    "priority",
  ];

  // Organize columns by category
  const organizedColumns = {
    core: availableColumns.filter((col) => coreColumns.includes(col)),
    prices: availableColumns.filter((col) => priceColumns.includes(col)),
    defaultCostMargin: availableColumns.filter((col) =>
      defaultCostMarginColumns.includes(col)
    ),
    weekdayCost: availableColumns.filter((col) =>
      weekdayCostColumns.includes(col)
    ),
    weekdayMargin: availableColumns.filter((col) =>
      weekdayMarginColumns.includes(col)
    ),
    weekdayMarginPercent: availableColumns.filter((col) =>
      weekdayMarginPercentColumns.includes(col)
    ),
    metadata: availableColumns.filter((col) => metadataColumns.includes(col)),
    other: availableColumns.filter(
      (col) =>
        !coreColumns.includes(col) &&
        !priceColumns.includes(col) &&
        !defaultCostMarginColumns.includes(col) &&
        !weekdayCostColumns.includes(col) &&
        !weekdayMarginColumns.includes(col) &&
        !weekdayMarginPercentColumns.includes(col) &&
        !metadataColumns.includes(col)
    ),
  };

  // Group data by room config and occupancy for better display
  const groupedData = importData.reduce((acc, row) => {
    const key = `${row.room_config_name}-${row.occupancy_name}`;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(row);
    return acc;
  }, {} as Record<string, ImportPreviewRow[]>);

  const formatPrice = (price: number | string | undefined | null) => {
    if (price === undefined || price === null || price === "") return "-";

    // Convert to number if it's a string
    const numPrice = typeof price === "string" ? parseFloat(price) : price;

    // Check if it's a valid number
    if (isNaN(numPrice)) return "-";

    return numPrice.toFixed(2);
  };

  const formatMealPlan = (mealPlan: string) => {
    if (!mealPlan || mealPlan === "-" || mealPlan === "null") {
      return "-";
    }
    return mealPlan;
  };

  // Helper function to format column headers
  const formatColumnHeader = (columnName: string) => {
    // Convert snake_case to Title Case
    return columnName
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  // Helper function to check if a column is a price/cost/margin column
  const isPriceColumn = (columnName: string) => {
    return (
      columnName.includes("price") ||
      columnName.includes("cost") ||
      columnName.includes("margin") ||
      columnName.includes("percentage")
    );
  };

  // Helper function to check if a row has errors
  const hasRowError = (rowIndex: number) => {
    return validationErrors.some((error) => error.row === rowIndex);
  };

  // Helper function to get error for a specific field in a row
  const getFieldError = (rowIndex: number, fieldName: string) => {
    return validationErrors.find(
      (error) => error.row === rowIndex && error.field === fieldName
    );
  };

  // Get columns to display based on toggle states
  const getDisplayColumns = () => {
    let columnsToShow = [...organizedColumns.core, ...organizedColumns.prices];

    if (showCostMargin) {
      columnsToShow = [
        ...columnsToShow,
        ...organizedColumns.defaultCostMargin,
        ...organizedColumns.weekdayCost,
        ...organizedColumns.weekdayMargin,
        ...organizedColumns.weekdayMarginPercent,
      ];
    }

    // Always show metadata and other columns
    columnsToShow = [
      ...columnsToShow,
      ...organizedColumns.metadata,
      ...organizedColumns.other,
    ];

    return columnsToShow;
  };

  const displayColumns = getDisplayColumns();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Import Preview
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {importData.length} pricing records to be imported •{" "}
            {displayColumns.length} columns
            {validationErrors.length > 0 && (
              <span className="ml-2 text-red-600 dark:text-red-400">
                • {validationErrors.length} validation errors
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={() => setShowCostMargin(!showCostMargin)}
            variant="secondary"
          >
            {showCostMargin ? "Hide" : "Show"} Cost/Margin
          </Button>
          <Button
            onClick={() => setIsExpanded(!isExpanded)}
            variant="secondary"
          >
            {isExpanded ? (
              <>
                <ChevronUp className="w-4 h-4 mr-1" />
                Collapse
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4 mr-1" />
                Expand
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Table Container */}
      <div
        className="overflow-auto"
        style={{
          maxHeight: isExpanded ? "600px" : maxHeight,
          transition: "max-height 0.3s ease-in-out",
        }}
      >
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-900 sticky top-0 z-10">
            <tr>
              {displayColumns.map((columnName) => (
                <th
                  key={columnName}
                  className={`px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${
                    isPriceColumn(columnName) ? "text-center" : "text-left"
                  }`}
                >
                  {formatColumnHeader(columnName)}
                  {isPriceColumn(columnName) && currentCurrency && (
                    <span className="ml-1 text-gray-400">
                      ({currentCurrency})
                    </span>
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {importData.map((row, index) => {
              const rowHasError = hasRowError(index);
              return (
                <tr
                  key={index}
                  className={`
                    ${
                      rowHasError
                        ? "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700"
                        : index % 2 === 0
                        ? "bg-white dark:bg-gray-800"
                        : "bg-gray-50 dark:bg-gray-900"
                    }
                  `}
                >
                  {displayColumns.map((columnName) => {
                    const cellValue = row[columnName];
                    const fieldError = getFieldError(index, columnName);

                    return (
                      <td
                        key={columnName}
                        className={`px-3 py-2 text-sm ${
                          isPriceColumn(columnName)
                            ? "text-center"
                            : "text-left"
                        } text-gray-900 dark:text-gray-100 ${
                          fieldError ? "bg-red-100 dark:bg-red-900/30" : ""
                        }`}
                        title={fieldError ? fieldError.message : undefined}
                      >
                        {columnName === "meal_plan_name" ? (
                          <Text size="small">{formatMealPlan(cellValue)}</Text>
                        ) : isPriceColumn(columnName) ? (
                          formatPrice(cellValue)
                        ) : columnName.includes("percentage") && cellValue ? (
                          `${cellValue}%`
                        ) : (
                          cellValue?.toString() || "-"
                        )}
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Summary Footer */}
      <div className="px-4 py-3 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-4">
            <span className="text-gray-600 dark:text-gray-400">
              Total records: {importData.length}
            </span>
            <span className="text-gray-600 dark:text-gray-400">
              Columns: {displayColumns.length}
            </span>
          </div>
          <div className="flex items-center gap-4">
            <span className="text-gray-600 dark:text-gray-400">
              Currency: <Badge>{currentCurrency}</Badge>
            </span>
            <span className="text-gray-600 dark:text-gray-400">
              Unique combinations: {Object.keys(groupedData).length}
            </span>
          </div>
        </div>
        {availableColumns.length > displayColumns.length && (
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            {availableColumns.length - displayColumns.length} additional columns
            hidden (toggle Cost/Margin to show more)
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportPreviewTable;
