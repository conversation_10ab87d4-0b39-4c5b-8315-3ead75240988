import React from "react";

export type RoomConfig = {
  id: string;
  title: string;
  handle?: string;
  description?: string;
};

export type OccupancyConfig = {
  id: string;
  name: string;
  is_default?: boolean;
};

export type MealPlan = {
  id: string;
  name: string;
  is_default?: boolean;
  metadata?: Record<string, any> & {
    applicable_occupancy_types?: string[] | null;
  };
};

export type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

export type WeekdayValues = {
  grossCost: number;
  fixedMargin: number;
  marginPercentage: number;
  total: number;
};

export type DefaultValues = {
  grossCost: number;
  fixedMargin: number;
  marginPercentage: number;
  total: number;
};

export type WeekdayPrices = {
  mon: number;
  tue: number;
  wed: number;
  thu: number;
  fri: number;
  sat: number;
  sun: number;
};

export type PricingRow = {
  id: string;
  roomConfigId: string;
  occupancyTypeId: string;
  mealPlanId: string | null; // null for extra beds and cots
  seasonalPeriodId?: string; // null/undefined for base pricing
  prices: WeekdayPrices;
  // Default cost and margin fields
  defaultValues: DefaultValues;
  // Weekday-specific cost and margin fields
  weekdayValues: {
    mon: WeekdayValues;
    tue: WeekdayValues;
    wed: WeekdayValues;
    thu: WeekdayValues;
    fri: WeekdayValues;
    sat: WeekdayValues;
    sun: WeekdayValues;
  };
  modified: boolean;
  appliedToAllDays?: boolean; // Flag to track if Apply button has been used
};

export type RoomPricingData = {
  room_config_id: string;
  room_config: RoomConfig;
  weekday_rules: any[];
  seasonal_prices: any[];
};

export type ComprehensivePricingTableProps = {
  hotelId: string;
  roomConfigs: RoomConfig[];
  occupancyConfigs: OccupancyConfig[];
  mealPlans: MealPlan[];
  seasonalPeriods: SeasonalPeriod[];
  setSeasonalPeriods: React.Dispatch<React.SetStateAction<SeasonalPeriod[]>>;
  initialPrices?: Record<string, any>; // Legacy support
  roomPricingData?: RoomPricingData[]; // New comprehensive API data
  onSave?: (data: any) => void | Promise<void>;
  canEdit?: boolean;
  canCreate?: boolean;
  canDelete?: boolean;
  hideBackButton?: boolean;
  readOnlyMode?: boolean; // New prop for read-only variant
  currentCurrency?: string; // Current selected currency
  onCurrencyChange?: (currency: string) => void; // Currency change handler
  onRefetch?: () => void; // Function to refetch data from API
};

// Additional utility types that might be useful
export type Weekday = 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat' | 'sun';

export type WeekdayInfo = {
  id: Weekday;
  name: string;
};
