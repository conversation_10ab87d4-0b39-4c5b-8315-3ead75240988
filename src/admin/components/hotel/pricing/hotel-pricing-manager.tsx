import React, { useState, useEffect, useMemo } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  toast,
  Tabs,
  IconButton,
} from "@camped-ai/ui";
import { ArrowLeft } from "lucide-react";
import ComprehensivePricingTable from "./comprehensive-pricing-table";
import { useAdminHotelComprehensivePricing } from "../../../hooks/hotel/use-admin-hotel-comprehensive-pricing";
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
import OccupancyConfigsManager from "./occupancy-configs-manager";
import MealPlansManager from "./meal-plans-manager";
import SeasonsManager from "./seasons-manager.tsx";
import HotelPricingTableSkeleton from "../../shared/hotel-pricing-table-skeleton.tsx";
import type { RoomConfig } from "./types";

type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

type HotelPricingManagerProps = {
  hotelId: string;
  roomConfigs: RoomConfig[];
  onBack?: () => void;
  hotelName: string;
  canEdit?: boolean;
  canCreate?: boolean;
  canDelete?: boolean;
  hideBackButton?: boolean;
};

const HotelPricingManager: React.FC<HotelPricingManagerProps> = ({
  hotelId,
  roomConfigs: propRoomConfigs,
  hotelName,
  onBack,
  canEdit = false,
  canCreate = false,
  canDelete = false,
  hideBackButton = false,
}) => {
  const [activeTab, setActiveTab] = useState("pricing");
  const [currentCurrency, setCurrentCurrency] = useState<string>("");

  // Fetch currencies to get the default currency
  const { defaultCurrency } = useAdminCurrencies();

  // Set default currency when currencies are loaded
  useEffect(() => {
    if (defaultCurrency && !currentCurrency) {
      setCurrentCurrency(defaultCurrency.currency_code);
    }
  }, [defaultCurrency, currentCurrency]);

  // Reset tab if it was set to a removed tab
  useEffect(() => {
    if (activeTab === "channels" || activeTab === "unified") {
      setActiveTab("pricing");
    }
  }, [activeTab]);

  // Use the comprehensive pricing hook with currency parameter
  const {
    data: comprehensiveData,
    isLoading,
    refetch,
  } = useAdminHotelComprehensivePricing(hotelId, currentCurrency);

  // Memoize room configs to prevent unnecessary re-renders
  const roomConfigs = useMemo(() => {
    return comprehensiveData?.roomConfigs || propRoomConfigs || [];
  }, [comprehensiveData?.roomConfigs, propRoomConfigs]);

  // Memoize other data to prevent unnecessary re-renders
  const occupancyConfigs = useMemo(() => {
    return comprehensiveData?.occupancyConfigs || [];
  }, [comprehensiveData?.occupancyConfigs]);

  const mealPlans = useMemo(() => {
    return comprehensiveData?.mealPlans || [];
  }, [comprehensiveData?.mealPlans]);

  const pricingData = useMemo(() => {
    return comprehensiveData?.pricingData || {};
  }, [comprehensiveData?.pricingData]);

  const [seasonalPeriods, setSeasonalPeriods] = useState<SeasonalPeriod[]>([]);

  // Update seasonal periods when comprehensive data changes
  useEffect(() => {
    if (comprehensiveData?.seasonalPeriods) {
      setSeasonalPeriods(comprehensiveData.seasonalPeriods);
    }
  }, [comprehensiveData?.seasonalPeriods]);

  const handleSavePricing = async (data: any) => {
    // If the data contains seasonal periods, update the state
    if (data && data.seasonal_periods) {
      setSeasonalPeriods(data.seasonal_periods);
    }

    // Refetch the data from the server to ensure UI shows the latest saved values
    try {
      await refetch();
      toast.success("Success", {
        description: "Pricing saved successfully",
      });
    } catch (error) {
      console.error("Error refreshing pricing data:", error);
      // Still show success since the save operation itself succeeded
      toast.success("Success", {
        description: "Pricing saved successfully",
      });
    }
  };

  if (isLoading || !currentCurrency) {
    return <HotelPricingTableSkeleton />;
  }

  return (
    <>
      {!hideBackButton && (
        <div className="">
          <div className="flex items-center gap-4 mb-2">
            <IconButton onClick={onBack}>
              <ArrowLeft className="w-4 h-4" />
            </IconButton>
            <Heading level="h1">{hotelName}</Heading>
          </div>
        </div>
      )}

      {roomConfigs.length === 0 ? (
        <div className="bg-muted p-8 rounded-lg border border-border text-center">
          <Text className="text-muted-foreground mb-4">
            No room configurations found. Please add room configurations first.
          </Text>
          {canCreate && (
            <Button
              variant="primary"
              onClick={() =>
                (window.location.href = `/hotel-management/hotels/${hotelId}/room-configs/new`)
              }
            >
              Add Room Configuration
            </Button>
          )}
        </div>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <Tabs.List className=" mb-4">
            <Tabs.Trigger value="pricing" className="px-4 py-2">
              Pricing
            </Tabs.Trigger>
            <Tabs.Trigger value="seasons" className="px-4 py-2">
              Seasons
            </Tabs.Trigger>
            <Tabs.Trigger value="occupancy" className="px-4 py-2">
              Occupancy Types
            </Tabs.Trigger>
            <Tabs.Trigger value="mealplans" className="px-4 py-2">
              Meal Plans
            </Tabs.Trigger>
            {/* <Tabs.Trigger value="channel-overrides" className="px-4 py-2">Channel Overrides</Tabs.Trigger> */}
          </Tabs.List>

          <Tabs.Content value="occupancy">
            <OccupancyConfigsManager
              hotelId={hotelId}
              canEdit={canEdit}
              canCreate={canCreate}
              canDelete={canDelete}
            />
          </Tabs.Content>

          <Tabs.Content value="mealplans">
            <MealPlansManager
              hotelId={hotelId}
              canEdit={canEdit}
              canCreate={canCreate}
              canDelete={canDelete}
            />
          </Tabs.Content>

          <Tabs.Content value="seasons">
            <SeasonsManager
              hotelId={hotelId}
              seasonalPeriods={seasonalPeriods}
              setSeasonalPeriods={setSeasonalPeriods}
              canEdit={canEdit}
              canCreate={canCreate}
              canDelete={canDelete}
            />
          </Tabs.Content>

          <Tabs.Content value="pricing">
            <ComprehensivePricingTable
              hotelId={hotelId}
              roomConfigs={roomConfigs}
              occupancyConfigs={occupancyConfigs}
              mealPlans={mealPlans}
              seasonalPeriods={seasonalPeriods}
              setSeasonalPeriods={setSeasonalPeriods}
              initialPrices={pricingData}
              roomPricingData={comprehensiveData?.room_pricing_data || []}
              onSave={handleSavePricing}
              canEdit={canEdit}
              canCreate={canCreate}
              canDelete={canDelete}
              currentCurrency={currentCurrency}
              onCurrencyChange={setCurrentCurrency}
              onRefetch={refetch}
            />
          </Tabs.Content>

          {/* <Tabs.Content value="channel-overrides">
            <SalesChannelPriceOverrides
              hotelId={hotelId}
              roomConfigs={roomConfigs}
              occupancyConfigs={occupancyConfigs}
              mealPlans={mealPlans}
              seasonalPeriods={seasonalPeriods}
              onSave={handleSavePricing}
            />
          </Tabs.Content> */}
        </Tabs>
      )}
    </>
  );
};

export default HotelPricingManager;
