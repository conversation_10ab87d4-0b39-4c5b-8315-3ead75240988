import React, { useState } from "react";
import { Dropdown<PERSON>en<PERSON>, But<PERSON> } from "@camped-ai/ui";
import { ConditionalTooltip } from "../../conditional-tooltip";
import { Booking, Room, RoomInventoryStatus } from "../types";
import {
  statusColors,
  getStatusDisplayName,
  getStatusIcon,
} from "./statusUtils";
import { formatDate, getBookingDuration } from "./timeUtils";
import {
  User,
  Calendar,
  Hash,
  MoreVertical,
  Eye,
  Edit,
  ArrowRight,
  Wrench,
  CheckCircle,
  // TODO: Re-enable split booking functionality in the future
  // Scissors,
} from "lucide-react";

interface BookingBlockProps {
  booking: Booking;
  left: number;
  width: number;
  height: number;
  room: Room;
  isHighlighted?: boolean;
  isSelected?: boolean;
  onViewBooking?: (bookingId: string) => void;
  onUpdateStatus?: (booking: Booking) => void;
  onMoveToUnallocated?: (booking: Booking, room: Room) => void;
  // TODO: Re-enable split booking functionality in the future
  // onSplitBooking?: (booking: Booking, room: Room) => void;
  onClick?: (booking: Booking) => void;
  onMarkAsMaintenance?: (booking: Booking) => void;
  onMarkAsAvailable?: (booking: Booking) => void;
}

const BookingBlock: React.FC<BookingBlockProps> = ({
  booking,
  left,
  width,
  height,
  room,
  isHighlighted = false,
  isSelected = false,
  onViewBooking,
  onUpdateStatus,
  onMoveToUnallocated,
  // TODO: Re-enable split booking functionality in the future
  // onSplitBooking,
  onClick,
  onMarkAsMaintenance,
  onMarkAsAvailable,
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const statusColor = statusColors[booking.status];
  const showLabel = width > 80; // Only show labels if block is wide enough

  // TODO: Re-enable split booking functionality in the future
  // Check if booking can be split (multi-day booking)
  // const canSplit = () => {
  //   const fromDate = new Date(booking.checkIn);
  //   const toDate = new Date(booking.checkOut);
  //   const duration = Math.ceil(
  //     (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
  //   );
  //   return duration > 1;
  // };

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    // Add drag logic here
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick?.(booking);
  };

  const duration = getBookingDuration(booking.checkIn, booking.checkOut);

  // Determine what to display based on booking type
  const isGuestBooking =
    booking.status === RoomInventoryStatus.BOOKED ||
    booking.status === RoomInventoryStatus.RESERVED;

  const displayName =
    isGuestBooking && booking.guestName
      ? booking.guestName
      : getStatusDisplayName(booking.status);

  // Tooltip content
  const tooltipContent = (
    <div className="max-w-xs space-y-1">
      <div className="flex items-center gap-2">
        <span>{getStatusIcon(booking.status)}</span>
        <span className="font-medium text-sm">
          {getStatusDisplayName(booking.status)}
        </span>
      </div>

      {isGuestBooking && booking.guestName && (
        <div className="flex items-center gap-2">
          <User size={12} />
          <span className="text-sm truncate">{booking.guestName}</span>
        </div>
      )}

      <div className="text-xs">
        <div className="flex items-center gap-1">
          <Calendar size={12} />
          <span>Check-in: {formatDate(booking.checkIn)}</span>
        </div>
        <div className="flex items-center gap-1 mt-1">
          <Calendar size={12} />
          <span>Check-out: {formatDate(booking.checkOut)}</span>
        </div>
        <div className="text-muted-foreground mt-1">
          Duration: {duration} {Number(duration) > 1 ? "s" : ""}
        </div>
        <div className="text-muted-foreground mt-1 overflow-hidden">
          {booking?.notes}
        </div>
      </div>

      {booking.confirmationNumber && (
        <div className="flex items-center gap-2">
          <Hash size={12} />
          <span className="text-xs font-mono">
            {booking.confirmationNumber}
          </span>
        </div>
      )}
    </div>
  );

  return (
    <ConditionalTooltip content={tooltipContent} showTooltip={!isDropdownOpen}>
      <div
        className={`absolute cursor-pointer booking-block-hover booking-block-clickable rounded-lg border-2 shadow-sm ${statusColor} ${
          isDragging ? "z-50 shadow-lg" : "z-10"
        } ${isHighlighted ? "ring-2 ring-primary/30 ring-opacity-50" : ""} ${
          isSelected ? "selected-booking" : ""
        }`}
        style={{
          left: `${left}px`,
          width: `${Math.max(width, 60)}px`,
          height: `${height}px`,
          top: "4px",
        }}
        onMouseDown={handleMouseDown}
        onClick={handleClick}
      >
        {/* Content with proper alignment */}
        <div className="p-3 h-full flex flex-col justify-center overflow-hidden relative">
          {showLabel ? (
            <>
              <div className="text-sm font-semibold truncate mb-1 leading-tight">
                {displayName}
              </div>
              <div className="text-xs opacity-90 truncate leading-tight">
                {formatDate(booking.checkIn)} - {formatDate(booking.checkOut)}
              </div>
            </>
          ) : (
            <div className="text-center flex items-center justify-center h-full">
              <span className="text-lg">{getStatusIcon(booking.status)}</span>
            </div>
          )}
        </div>

        {/* 3-dots menu - show on hover, but only show view option if booking has ID */}
        {onViewBooking && (
          <div className="absolute top-1 right-1 z-20">
            <DropdownMenu onOpenChange={setIsDropdownOpen}>
              <DropdownMenu.Trigger asChild>
                <Button
                  variant="secondary"
                  size="small"
                  className="h-6 w-6 p-0 booking-menu-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    console.log("3-dots button clicked", {
                      booking,
                      order_id: booking.order_id,
                    });
                  }}
                >
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content align="end" className="w-48">
                {/* View Booking Details - Only show if booking has order_id */}
                {booking.order_id ? (
                  <DropdownMenu.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      if (booking.order_id) {
                        console.log(
                          "View booking clicked",
                          booking.order_id
                        );
                        onViewBooking(booking.order_id);
                      }
                    }}
                    className="flex items-center gap-2"
                  >
                    <Eye className="h-4 w-4" />
                    View Booking Details
                  </DropdownMenu.Item>
                ) : (
                  <DropdownMenu.Item
                    disabled
                    className="flex items-center gap-2"
                  >
                    <Eye className="h-4 w-4 opacity-50" />
                    <span className="text-muted-foreground">
                      No booking details
                    </span>
                  </DropdownMenu.Item>
                )}

                {/* All other menu items - always available regardless of order_id */}
                    <DropdownMenu.Item
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        console.log("Update status clicked", booking);
                        onUpdateStatus?.(booking);
                      }}
                      className="flex items-center gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Update Status
                    </DropdownMenu.Item>
                    {onMoveToUnallocated && (
                      <>
                        <DropdownMenu.Separator />
                        <DropdownMenu.Item
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            console.log("Move to unallocated clicked", booking);
                            onMoveToUnallocated(booking, room);
                          }}
                          className="flex items-center gap-2"
                        >
                          <ArrowRight className="h-4 w-4" />
                          Move to Unallocated
                        </DropdownMenu.Item>
                      </>
                    )}

                    {/* TODO: Re-enable split booking functionality in the future */}
                    {/* Split Booking Option - Only show for multi-day bookings */}
                    {/* {onSplitBooking && canSplit() && (
                      <DropdownMenu.Item
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          console.log("Split booking clicked", booking);
                          onSplitBooking(booking, room);
                        }}
                        className="flex items-center gap-2"
                      >
                        <Scissors className="h-4 w-4" />
                        Split Booking
                      </DropdownMenu.Item>
                    )} */}

                    {/* Dynamic Status-Based Options */}

                    {/* Mark as Maintenance - Only show for available status */}
                    {booking.status === RoomInventoryStatus.AVAILABLE &&
                      onMarkAsMaintenance && (
                        <DropdownMenu.Item
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            console.log("Mark as maintenance clicked", booking);
                            onMarkAsMaintenance(booking);
                          }}
                          className="flex items-center gap-2"
                        >
                          <Wrench className="h-4 w-4" />
                          Mark as Maintenance
                        </DropdownMenu.Item>
                      )}

                    {/* Mark as Available - Only show for maintenance status */}
                    {booking.status === RoomInventoryStatus.MAINTENANCE &&
                      onMarkAsAvailable && (
                        <DropdownMenu.Item
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            console.log("Mark as available clicked", booking);
                            onMarkAsAvailable(booking);
                          }}
                          className="flex items-center gap-2"
                        >
                          <CheckCircle className="h-4 w-4" />
                          Mark as Available
                        </DropdownMenu.Item>
                      )}
              </DropdownMenu.Content>
            </DropdownMenu>
          </div>
        )}

        {/* Resize handles with proper positioning */}
        <div className="absolute left-0 top-0 w-2 h-full cursor-ew-resize hover:bg-background/30 rounded-l-lg" />
        <div className="absolute right-0 top-0 w-2 h-full cursor-ew-resize hover:bg-background/30 rounded-r-lg" />
      </div>
    </ConditionalTooltip>
  );
};

export default BookingBlock;
