import React, { useState, useEffect } from "react";
import {
  FocusModal,
  Heading,
  Text,
  Button,
  Label,
  toast,
} from "@camped-ai/ui";
import { Download } from "lucide-react";

interface ExportModalProps {
  open: boolean;
  onClose: () => void;
  hotelId?: string;
}

const ExportModal: React.FC<ExportModalProps> = ({ open, onClose, hotelId }) => {
  // State for selected fields
  const [selectedFields, setSelectedFields] = useState<Record<string, boolean>>({
    id: true,
    title: true,
    room_number: true,
    hotel_id: true,
    status: true,
    floor: true,
    room_config_id: true,
    created_at: true,
    updated_at: true,
  });

  // State for filters
  const [filters, setFilters] = useState({
    status: "all", // "all", "active", "draft", etc.
    hotel_id: hotelId || "all", // Default to current hotel or "all"
  });

  // State for available hotels
  const [hotels, setHotels] = useState<Array<{id: string, name: string}>>([]);
  const [isLoadingHotels, setIsLoadingHotels] = useState(false);

  // State for file format
  const [fileFormat, setFileFormat] = useState<"csv" | "xlsx">("xlsx");

  // Fetch hotels when modal opens
  useEffect(() => {
    if (open) {
      fetchHotels();
    }
  }, [open]);

  // Fetch hotels for the dropdown
  const fetchHotels = async () => {
    setIsLoadingHotels(true);
    try {
      const response = await fetch('/admin/hotel-management/hotels', {
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch hotels');
      }
      
      const { hotels: hotelsList } = await response.json();
      
      // Format hotels for the dropdown
      const formattedHotels = hotelsList.map((hotel: any) => ({
        id: hotel.id,
        name: hotel.name
      }));
      
      setHotels(formattedHotels);
    } catch (error) {
      console.error('Error fetching hotels:', error);
    } finally {
      setIsLoadingHotels(false);
    }
  };

  // Handle field selection
  const handleFieldChange = (field: string, checked: boolean) => {
    setSelectedFields((prev) => ({
      ...prev,
      [field]: checked,
    }));
  };

  // Handle select all fields
  const handleSelectAll = () => {
    const allFields = { ...selectedFields };
    const allSelected = Object.values(allFields).every((value) => value);

    Object.keys(allFields).forEach((key) => {
      allFields[key] = !allSelected;
    });

    setSelectedFields(allFields);
  };

  // Handle filter changes
  const handleFilterChange = (
    filterName: string,
    value: string | boolean
  ) => {
    setFilters((prev) => ({
      ...prev,
      [filterName]: value,
    }));
  };

  // Handle export
  const handleExport = async () => {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      
      // Add fields
      const fields = Object.entries(selectedFields)
        .filter(([_, selected]) => selected)
        .map(([field]) => field);
      
      queryParams.append("fields", fields.join(","));
      
      // Add filters
      if (filters.status !== "all") {
        queryParams.append("status", filters.status);
      }
      
      if (filters.hotel_id !== "all") {
        queryParams.append("hotel_id", filters.hotel_id);
      }
      
      // Add format
      queryParams.append("format", fileFormat);

      // Use the export endpoint
      const exportUrl = `/admin/rooms/export?${queryParams.toString()}`;
      console.log('Export URL:', exportUrl);
      
      // Open in a new tab for download
      window.open(exportUrl, '_blank');

      console.log('Export request successful');
      
      // Close the modal
      onClose();
    } catch (error) {
      console.error("Error exporting rooms:", error);

      // Show error toast
      toast.error("Export Error", {
        description: (error as Error).message || "Failed to export rooms",
      });
    }
  };

  return (
    <FocusModal open={open} onOpenChange={onClose}>
      <FocusModal.Content className="flex flex-col h-full max-h-[98vh] bg-gray-50 dark:bg-gray-900">
        <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center w-full py-4 px-6">
            <div className="flex items-center gap-3">
              <Heading level="h2" className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Export Rooms
              </Heading>
            </div>

          {/* Progress Indicator */}
          <div className="px-6 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Step 1 */}
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                    1
                  </div>
                  <span className="ml-2 text-sm font-medium text-blue-600 dark:text-blue-400">Configure</span>
                </div>
                <div className="w-8 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                {/* Step 2 */}
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400 rounded-full flex items-center justify-center text-sm font-semibold">
                    2
                  </div>
                  <span className="ml-2 text-sm font-medium text-gray-500 dark:text-gray-400">Export</span>
                </div>
              </div>
            </div>
          </div>
          </div>

        </FocusModal.Header>
        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Fields Section */}
            <div className="space-y-4 border rounded-lg p-4">
              <div className="flex justify-between items-center">
                <Heading level="h3" className="text-lg font-medium">
                  Select Fields to Export
                </Heading>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={handleSelectAll}
                >
                  {Object.values(selectedFields).every((v) => v)
                    ? "Deselect All"
                    : "Select All"}
                </Button>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-[300px] overflow-y-auto pr-2">
                {Object.keys(selectedFields).map((field) => (
                  <div key={field} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id={`field-${field}`}
                      checked={selectedFields[field]}
                      onChange={(e) => handleFieldChange(field, e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded"
                    />
                    <Label htmlFor={`field-${field}`} className="cursor-pointer">
                      {field
                        .replace(/_/g, " ")
                        .replace(/\\b\\w/g, (l) => l.toUpperCase())}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-6">
              {/* Filters Section */}
              <div className="space-y-4 border rounded-lg p-4">
                <Heading level="h3" className="text-lg font-medium">
                  Filter Rooms
                </Heading>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <select
                      id="status"
                      value={filters.status}
                      onChange={(e) => handleFilterChange("status", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">All Statuses</option>
                      <option value="active">Active</option>
                      <option value="draft">Draft</option>
                      <option value="archived">Archived</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="hotel_id">Hotel</Label>
                    {isLoadingHotels ? (
                      <div className="flex items-center justify-center py-2">
                        <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full mr-2"></div>
                        <span className="text-sm text-gray-500">Loading hotels...</span>
                      </div>
                    ) : (
                      <select
                        id="hotel_id"
                        value={filters.hotel_id}
                        onChange={(e) => handleFilterChange("hotel_id", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="all">All Hotels</option>
                        {hotels.map((hotel) => (
                          <option key={hotel.id} value={hotel.id}>
                            {hotel.name}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                </div>
              </div>

              {/* Format Section */}
              <div className="space-y-4 border rounded-lg p-4">
                <Heading level="h3" className="text-lg font-medium">
                  Export Format
                </Heading>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <input
                      type="radio"
                      id="format-xlsx"
                      name="fileFormat"
                      value="xlsx"
                      checked={fileFormat === "xlsx"}
                      onChange={() => setFileFormat("xlsx")}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="format-xlsx" className="cursor-pointer">
                      Excel (.xlsx)
                    </Label>
                  </div>

                  <div className="flex items-center gap-2">
                    <input
                      type="radio"
                      id="format-csv"
                      name="fileFormat"
                      value="csv"
                      checked={fileFormat === "csv"}
                      onChange={() => setFileFormat("csv")}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="format-csv" className="cursor-pointer">
                      CSV (.csv)
                    </Label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>
        </FocusModal.Body>
        <div className="flex-shrink-0 py-4 px-6 bg-white border-t border-gray-200 flex gap-2 justify-end">
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleExport}
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Export
          </Button>
        </div>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default ExportModal;
