import React, { useState, useEffect } from "react";
import {
  Table,
  Badge,
  Button,
  Select,
  Input,
  DatePicker,
  Heading,
  Text,
  Toaster,
  toast,
  Dropdown<PERSON>enu,
  IconButton,
} from "@camped-ai/ui";
import { format } from "date-fns";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  Copy,
  Filter,
  ChevronDown,
  ChevronUp,
  MoreHorizontal,
  Eye,
  Edit,
  Trash,
  Calendar
} from "lucide-react";
import { Buildings } from "@camped-ai/icons";
import Spinner from "../shared/spinner";
import { useRbac } from "../../hooks/use-rbac";

// Type definitions
interface BookingMetadata {
  room_id?: string;
  room_name?: string;
  room_configuration_name?: string;
  room_type?: string;
  hotel_id?: string;
  hotel_name?: string;
  guest_name?: string;
  guest_email?: string;
  check_in_date?: string;
  check_out_date?: string;
  nights?: number;
  created_at?: string;
  payment_status?: string;
  currency_code?: string;
  total_amount?: number;
  add_on_total_amount?: number;
}

interface Booking {
  order_id: string;
  status: string;
  total: number;
  currency_code: string;
  created_at: string;
  guest_name?: string;
  guest_email?: string;
  room_type?: string;
  payment_status?: string;
  total_amount?: number;
  check_in_date?: string;
  check_out_date?: string;
  metadata?: BookingMetadata;
}

interface Hotel {
  id: string;
  name: string;
}

// Status badge colors
const statusColors = {
  pending: "yellow",
  confirmed: "green",
  checked_in: "blue",
  checked_out: "purple",
  canceled: "gray", // Changed from red to gray (American spelling)
  no_show: "gray",
};

interface BookingListProps {
  hotelId?: string | null;
}

const BookingList = ({ hotelId = null }: BookingListProps) => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { hasPermission } = useRbac();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [isLoading, setIsLoading] = useState(false); // Start with false to allow initial fetch
  const [totalCount, setTotalCount] = useState(0);
  const [showFilters, setShowFilters] = useState(false);

  // Sorting state
  const [sorting, setSorting] = useState<{
    sort_by: "order_id" | "guest_name" | "hotel_name" | "status" | "payment_status" | "total_amount" | "created_at";
    sort_order: "asc" | "desc";
  }>({ sort_by: "created_at", sort_order: "desc" });

  // Sorting helper functions
  const handleSort = (column: "order_id" | "guest_name" | "hotel_name" | "status" | "payment_status" | "total_amount" | "created_at") => {
    let newSortOrder: "asc" | "desc" = "asc";

    // If clicking the same column, toggle the order
    if (sorting.sort_by === column) {
      newSortOrder = sorting.sort_order === "asc" ? "desc" : "asc";
    } else {
      // If clicking a different column, use default order for that column
      if (column === "created_at" || column === "total_amount") {
        newSortOrder = "desc"; // Date and amount columns default to newest/highest first
      } else {
        newSortOrder = "asc"; // Other columns default to alphabetical order (A-Z)
      }
    }

    setSorting({
      sort_by: column,
      sort_order: newSortOrder,
    });
  };

  const getSortIcon = (column: "order_id" | "guest_name" | "hotel_name" | "status" | "payment_status" | "total_amount" | "created_at") => {
    if (sorting.sort_by !== column) {
      return <ChevronDown className="h-4 w-4 text-ui-fg-muted opacity-50" />;
    }

    return sorting.sort_order === "asc" ? (
      <ChevronUp className="h-4 w-4 text-ui-fg-base" />
    ) : (
      <ChevronDown className="h-4 w-4 text-ui-fg-base" />
    );
  };

  const SortableHeader: React.FC<{
    column: "order_id" | "guest_name" | "hotel_name" | "status" | "payment_status" | "total_amount" | "created_at";
    children: React.ReactNode;
    className?: string;
  }> = ({ column, children, className }) => (
    <Table.HeaderCell
      className={`${className} cursor-pointer hover:bg-ui-bg-subtle transition-colors select-none`}
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-1">
        {children}
        {getSortIcon(column)}
      </div>
    </Table.HeaderCell>
  );

  // Get pagination from URL parameters
  const currentPage = parseInt(searchParams.get("page") || "0");
  const pageSize = parseInt(searchParams.get("pageSize") || "10");

  // Calculate pagination values
  const pageCount = Math.ceil(totalCount / pageSize);
  const canNextPage = currentPage < pageCount - 1;
  const canPreviousPage = currentPage > 0;

  // Initialize filter states from URL parameters or defaults
  const [hotelFilter, setHotelFilter] = useState(
    searchParams.get("hotel") || hotelId || "all"
  );
  const [customerNameFilter, setCustomerNameFilter] = useState(
    searchParams.get("customerName") || ""
  );
  const [customerEmailFilter, setCustomerEmailFilter] = useState(
    searchParams.get("customerEmail") || ""
  );
  const [fromDateFilter, setFromDateFilter] = useState(() => {
    const fromDate = searchParams.get("fromDate");
    return fromDate ? new Date(fromDate) : null;
  });
  const [toDateFilter, setToDateFilter] = useState(() => {
    const toDate = searchParams.get("toDate");
    return toDate ? new Date(toDate) : null;
  });

  // Initialize hotelFilter from hotelId prop on component mount
  useEffect(() => {
    if (hotelId) {
      setHotelFilter(hotelId);
    }
  }, []);

  // Update hotelFilter when hotelId prop changes
  useEffect(() => {
    if (hotelId) {
      setHotelFilter(hotelId);
    }
  }, [hotelId]);

  // Fetch hotels for the filter dropdown
  const fetchHotels = async () => {
    try {
      const response = await fetch(`/admin/hotel-management/hotels?limit=100`);

      if (!response.ok) {
        throw new Error("Failed to fetch hotels");
      }

      const data = await response.json();
      setHotels(data.hotels || []);
    } catch (error) {
      console.error("Error fetching hotels:", error);
      toast.error("Failed to fetch hotels");
    }
  };

  // Fetch bookings
  const fetchBookings = async () => {
    try {
      // Prevent multiple simultaneous API calls
      if (isLoading) {
        return;
      }

      setIsLoading(true);

      // Get current filter values from URL or state
      const currentFilters = getCurrentFiltersFromURL();

      // Build query parameters
      const params = new URLSearchParams();

      // Always use hotel filter, prioritizing URL then state then prop
      const effectiveHotelFilter = currentFilters.hotel;
      if (effectiveHotelFilter && effectiveHotelFilter !== "all") {
        params.append("hotel_id", effectiveHotelFilter);
      } else if (hotelId) {
        // Fallback to hotelId prop if no filter is set
        params.append("hotel_id", hotelId);
      }

      // Payment status filter removed

      if (currentFilters.customerName) {
        params.append("guest_name", currentFilters.customerName);
      }

      if (currentFilters.customerEmail) {
        params.append("guest_email", currentFilters.customerEmail);
      }

      if (currentFilters.fromDate) {
        params.append(
          "from_date",
          format(currentFilters.fromDate, "yyyy-MM-dd")
        );
      }

      if (currentFilters.toDate) {
        params.append("to_date", format(currentFilters.toDate, "yyyy-MM-dd"));
      }

      // Pagination - use URL parameters directly or calculate from page
      const effectiveLimit = searchParams.get("limit") || pageSize.toString();
      const effectiveOffset =
        searchParams.get("offset") || (currentPage * pageSize).toString();

      params.append("limit", effectiveLimit);
      params.append("offset", effectiveOffset);

      // Fetch data
      const url = `/admin/hotel-management/bookings?${params.toString()}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error("Failed to fetch bookings");
      }

      const data = await response.json();

      // Backend now handles pagination properly
      setBookings(data.bookings || []);
      setTotalCount(data.count || 0);
    } catch (error) {
      console.error("Error fetching bookings:", error);
      toast.error("Failed to fetch bookings");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch hotels on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  // Initialize default URL parameters if missing
  useEffect(() => {
    const hasPageParam = searchParams.has("page");
    const hasPageSizeParam = searchParams.has("pageSize");
    const hasLimitParam = searchParams.has("limit");
    const hasOffsetParam = searchParams.has("offset");

    // If any pagination parameters are missing, set all defaults
    if (
      !hasPageParam ||
      !hasPageSizeParam ||
      !hasLimitParam ||
      !hasOffsetParam
    ) {
      const newSearchParams = new URLSearchParams(searchParams);

      if (!hasPageParam) newSearchParams.set("page", "0");
      if (!hasPageSizeParam) newSearchParams.set("pageSize", "10");
      if (!hasLimitParam) newSearchParams.set("limit", "10");
      if (!hasOffsetParam) newSearchParams.set("offset", "0");

      setSearchParams(newSearchParams, { replace: true });
    }
  }, []); // Only run once on mount

  // Read current values from URL for API calls (no state updates to avoid loops)
  const getCurrentFiltersFromURL = () => {
    return {
      hotel: searchParams.get("hotel") || hotelFilter,
      customerName: searchParams.get("customerName") || customerNameFilter,
      customerEmail: searchParams.get("customerEmail") || customerEmailFilter,
      fromDate: searchParams.get("fromDate")
        ? new Date(searchParams.get("fromDate")!)
        : fromDateFilter,
      toDate: searchParams.get("toDate")
        ? new Date(searchParams.get("toDate")!)
        : toDateFilter,
    };
  };

  // Single useEffect for all data fetching - only depend on URL params and hotelId prop
  useEffect(() => {
    // Check if we have the required pagination parameters
    const hasRequiredParams =
      searchParams.has("page") && searchParams.has("limit");

    if (hasRequiredParams) {
      fetchBookings();
    }
  }, [searchParams, hotelId]);

  // Update URL parameters for pagination
  const updatePage = (newPage: number) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("page", newPage.toString());
    newSearchParams.set("pageSize", pageSize.toString());
    newSearchParams.set("limit", pageSize.toString());
    newSearchParams.set("offset", (newPage * pageSize).toString());
    setSearchParams(newSearchParams);
  };

  // Update page size and reset to first page
  const updatePageSize = (newPageSize: number) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("page", "0"); // Reset to first page
    newSearchParams.set("pageSize", newPageSize.toString());
    newSearchParams.set("limit", newPageSize.toString());
    newSearchParams.set("offset", "0"); // Reset offset
    setSearchParams(newSearchParams);
  };

  // Quick date select handlers
  const handleQuickDateSelect = (period: string) => {
    const today = new Date();
    let fromDate: Date | null = null;
    let toDate: Date | null = null;

    switch (period) {
      case "today":
        fromDate = new Date(today);
        toDate = new Date(today);
        break;
      case "thisWeek":
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        fromDate = startOfWeek;
        toDate = endOfWeek;
        break;
      case "thisMonth":
        fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
        toDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        break;
      case "last30Days":
        fromDate = new Date(today);
        fromDate.setDate(today.getDate() - 30);
        toDate = new Date(today);
        break;
    }

    setFromDateFilter(fromDate);
    setToDateFilter(toDate);

    // Auto-apply filters when quick date is selected
    setTimeout(() => {
      handleApplyFilters();
    }, 100);
  };

  // Apply filters
  const handleApplyFilters = () => {
    const newSearchParams = new URLSearchParams(searchParams);

    // Update all filter parameters in URL
    newSearchParams.set("hotel", hotelFilter);
    newSearchParams.set("customerName", customerNameFilter);
    newSearchParams.set("customerEmail", customerEmailFilter);

    if (fromDateFilter) {
      newSearchParams.set("fromDate", format(fromDateFilter, "yyyy-MM-dd"));
    } else {
      newSearchParams.delete("fromDate");
    }

    if (toDateFilter) {
      newSearchParams.set("toDate", format(toDateFilter, "yyyy-MM-dd"));
    } else {
      newSearchParams.delete("toDate");
    }

    // Reset pagination
    newSearchParams.set("page", "0");
    newSearchParams.set("pageSize", pageSize.toString());
    newSearchParams.set("limit", pageSize.toString());
    newSearchParams.set("offset", "0");

    setSearchParams(newSearchParams);
  };

  // Reset filters
  const handleResetFilters = () => {
    // If hotelId is provided from URL, keep that filter
    setHotelFilter(hotelId || "all");
    setCustomerNameFilter("");
    setCustomerEmailFilter("");
    setFromDateFilter(null);
    setToDateFilter(null);

    const newSearchParams = new URLSearchParams(searchParams);

    // Reset all filter parameters in URL
    newSearchParams.set("hotel", hotelId || "all");
    newSearchParams.delete("customerName");
    newSearchParams.delete("customerEmail");
    newSearchParams.delete("fromDate");
    newSearchParams.delete("toDate");

    // Reset pagination
    newSearchParams.set("page", "0");
    newSearchParams.set("pageSize", pageSize.toString());
    newSearchParams.set("limit", pageSize.toString());
    newSearchParams.set("offset", "0");

    setSearchParams(newSearchParams);
  };

  // View booking details
  const handleViewBooking = (bookingId: string) => {
    navigate(`/hotel-management/bookings/${bookingId}`);
  };

  // Create new booking
  const handleCreateBooking = () => {
    navigate("/hotel-management/bookings/create");
  };

  // Copy booking ID to clipboard
  const handleCopyBookingId = (bookingId: string) => {
    navigator.clipboard.writeText(bookingId);
    toast.success("Copied", {
      description: "Booking ID copied to clipboard",
    });
  };

  // Edit booking
  const handleEditBooking = (bookingId: string) => {
    navigate(`/hotel-management/bookings/${bookingId}/edit`);
  };

  // Delete booking
  const handleDeleteBooking = (bookingId: string) => {
    // TODO: Implement delete functionality
    toast.error("Delete functionality not implemented yet");
  };

  // Calculate duration in nights
  const calculateDuration = (checkInDate: string, checkOutDate: string) => {
    try {
      const checkIn = new Date(checkInDate);
      const checkOut = new Date(checkOutDate);

      if (isNaN(checkIn.getTime()) || isNaN(checkOut.getTime())) {
        return 0;
      }

      const diffTime = checkOut.getTime() - checkIn.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return Math.max(0, diffDays);
    } catch (error) {
      console.error("Error calculating duration:", error);
      return 0;
    }
  };

  // Format booking dates range
  const formatBookingDates = (checkInDate: string, checkOutDate: string) => {
    try {
      const checkIn = new Date(checkInDate);
      const checkOut = new Date(checkOutDate);

      if (isNaN(checkIn.getTime()) || isNaN(checkOut.getTime())) {
        return "Invalid dates";
      }

      return `${format(checkIn, "MMM dd")} → ${format(checkOut, "MMM dd")}`;
    } catch (error) {
      console.error("Error formatting booking dates:", error);
      return "Invalid dates";
    }
  };

  // Helper render functions
  const renderActionDropdown = (booking: Booking) => (
    <div onClick={(e) => e.stopPropagation()}>
      <DropdownMenu>
        <DropdownMenu.Trigger asChild>
          <IconButton size="small">
            <MoreHorizontal className="h-4 w-4" />
          </IconButton>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content align="end">
          <DropdownMenu.Item
            onClick={(e) => {
              e.stopPropagation();
              handleViewBooking(booking.order_id);
            }}
          >
            <Eye className="h-4 w-4 mr-2" />
            View
          </DropdownMenu.Item>
          {hasPermission("bookings:edit") && (
            <DropdownMenu.Item
              onClick={(e) => {
                e.stopPropagation();
                handleEditBooking(booking.order_id);
              }}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </DropdownMenu.Item>
          )}
          {hasPermission("bookings:delete") && (
            <DropdownMenu.Item
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteBooking(booking.order_id);
              }}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenu.Item>
          )}
        </DropdownMenu.Content>
      </DropdownMenu>
    </div>
  );

  const renderLoadingSkeleton = () =>
    Array.from({ length: 5 }).map((_, index) => (
      <Table.Row key={`skeleton-${index}`}>
        {/* Booking ID skeleton */}
        <Table.Cell className="w-32">
          <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-20" />
        </Table.Cell>
        {/* Customer skeleton */}
        <Table.Cell className="w-48">
          <div className="space-y-1">
            <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-32" />
            <div className="h-3 bg-ui-bg-subtle rounded animate-pulse w-24" />
          </div>
        </Table.Cell>
        {/* Hotel skeleton */}
        <Table.Cell className="w-40">
          <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-28" />
        </Table.Cell>
        {/* Room Type skeleton */}
        <Table.Cell className="w-32">
          <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-20" />
        </Table.Cell>
        {/* Duration skeleton */}
        <Table.Cell className="w-24">
          <div className="h-6 bg-ui-bg-subtle rounded animate-pulse w-16" />
        </Table.Cell>
        {/* Booking Dates skeleton */}
        <Table.Cell className="w-36">
          <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-24" />
        </Table.Cell>
        {/* Status skeleton */}
        <Table.Cell className="w-24">
          <div className="h-6 bg-ui-bg-subtle rounded animate-pulse w-16" />
        </Table.Cell>
        {/* Payment Status skeleton */}
        <Table.Cell className="w-32">
          <div className="h-6 bg-ui-bg-subtle rounded animate-pulse w-20" />
        </Table.Cell>
        {/* Total skeleton */}
        <Table.Cell className="w-24">
          <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-16" />
        </Table.Cell>
        {/* Actions skeleton */}
        <Table.Cell className="w-16">
          <div className="h-8 w-8 bg-ui-bg-subtle rounded animate-pulse" />
        </Table.Cell>
      </Table.Row>
    ));

  const renderEmptyState = () => (
    <div className="py-16 text-center">
      <div className="max-w-sm mx-auto">
        <Calendar className="h-16 w-16 text-ui-fg-muted mx-auto mb-4" />
        <Text size="large" weight="plus" className="text-ui-fg-base">
          No bookings found
        </Text>
        <Text className="text-ui-fg-subtle mt-2">
          Try adjusting your filters or get started by creating your first booking
        </Text>
        {hasPermission("bookings:create") && (
          <Button
            size="small"
            className="mt-6"
            onClick={handleCreateBooking}
          >
            Create your first booking
          </Button>
        )}
      </div>
    </div>
  );

  return (
    <div className="">
      <Toaster />
      <div className="flex justify-between items-center mb-6">
        <Heading>Bookings</Heading>
        <div className="flex gap-2">
          <Button
            variant="secondary"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
            <ChevronDown
              className={`h-4 w-4 transition-transform duration-200 ease-in-out ${
                showFilters ? "rotate-180" : "rotate-0"
              }`}
            />
          </Button>
          {hasPermission("carts:view") && (
            <Button
              variant="secondary"
              onClick={() => navigate("/hotel-management/carts")}
            >
              View Pending Carts
            </Button>
          )}
          {hasPermission("bookings:create") && (
            <Button onClick={handleCreateBooking}>Create Booking</Button>
          )}
        </div>
      </div>

      {/* Collapsible Filters */}
      <div
        className={`overflow-hidden transition-all duration-300 ease-in-out mb-6 ${
          showFilters ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
        }`}
      >
        <div className="bg-card p-6 rounded-md shadow-sm border border-border transform transition-transform duration-300 ease-in-out">
          <div className="space-y-4">
            {/* Hotel - Full Width */}
            <div>
              <Text className="text-xs font-medium text-muted-foreground mb-1">
                HOTEL
              </Text>
              <Select
                value={hotelFilter}
                onValueChange={setHotelFilter}
                disabled={!!hotelId}
              >
                <Select.Trigger className={hotelId ? "bg-muted" : ""}>
                  <Select.Value placeholder="All Hotels" />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="all">All Hotels</Select.Item>
                  {hotels.map((hotel) => (
                    <Select.Item key={hotel.id} value={hotel.id}>
                      {hotel.name}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>

            {/* Customer Name and Email - Same Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Text className="text-xs font-medium text-muted-foreground mb-1">
                  CUSTOMER NAME
                </Text>
                <Input
                  placeholder="Search by name..."
                  value={customerNameFilter}
                  onChange={(e) => setCustomerNameFilter(e.target.value)}
                />
              </div>
              <div>
                <Text className="text-xs font-medium text-muted-foreground mb-1">
                  CUSTOMER EMAIL
                </Text>
                <Input
                  placeholder="Search by email..."
                  value={customerEmailFilter}
                  onChange={(e) => setCustomerEmailFilter(e.target.value)}
                />
              </div>
            </div>

            {/* Check-in and Check-out - Same Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Text className="text-xs font-medium text-muted-foreground mb-1">
                  CHECK-IN DATE
                </Text>
                <DatePicker
                  value={fromDateFilter}
                  onChange={setFromDateFilter}
                />
              </div>
              <div>
                <Text className="text-xs font-medium text-muted-foreground mb-1">
                  CHECK-OUT DATE
                </Text>
                <DatePicker value={toDateFilter} onChange={setToDateFilter} />
              </div>
            </div>

            {/* Actions Row - Left aligned buttons, Right aligned counts */}
            <div className="flex justify-between items-center pt-4">
              <div className="flex gap-2">
                <Button onClick={handleApplyFilters}>Apply Filters</Button>
                <Button variant="secondary" onClick={handleResetFilters}>
                  Reset All
                </Button>
              </div>

              <div className="text-xs text-muted-foreground space-y-1 text-right">
                <div>
                  Total Results:{" "}
                  <span className="font-medium">{totalCount}</span>
                </div>
                <div>
                  Showing:{" "}
                  <span className="font-medium">{bookings.length}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bookings Table */}
      <div className="mx-0 mt-6">
        {bookings.length === 0 && !isLoading ? (
          renderEmptyState()
        ) : (
          <Table>
            <Table.Header>
              <Table.Row>
                <SortableHeader column="order_id" className="w-32">
                  Booking ID
                </SortableHeader>
                <SortableHeader column="guest_name" className="w-48">
                  Customer
                </SortableHeader>
                <SortableHeader column="hotel_name" className="w-40">
                  Hotel
                </SortableHeader>
                <Table.HeaderCell className="w-32">Room Type</Table.HeaderCell>
                <Table.HeaderCell className="w-24">Duration</Table.HeaderCell>
                <Table.HeaderCell className="w-36">Booking Dates</Table.HeaderCell>
                <SortableHeader column="status" className="w-24">
                  Status
                </SortableHeader>
                <SortableHeader column="payment_status" className="w-32">
                  Payment Status
                </SortableHeader>
                <SortableHeader column="total_amount" className="w-24">
                  Total
                </SortableHeader>
                <Table.HeaderCell className="text-right w-16">
                  Actions
                </Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {isLoading
                ? renderLoadingSkeleton()
                : bookings.map((booking) => {
                  return (
                    <Table.Row
                      key={booking.order_id}
                      className="cursor-pointer hover:bg-ui-bg-subtle transition-colors"
                      onClick={() => handleViewBooking(booking.order_id)}
                    >
                      {/* Booking ID */}
                      <Table.Cell className="w-32">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-ui-fg-base truncate max-w-[80px]">
                            {booking.order_id}
                          </span>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCopyBookingId(booking.order_id);
                            }}
                            className="p-1 hover:bg-ui-bg-subtle rounded transition-colors"
                            title="Copy booking ID"
                          >
                            <Copy className="h-3 w-3 text-ui-fg-muted" />
                          </button>
                        </div>
                      </Table.Cell>
                      {/* Customer */}
                      <Table.Cell className="w-48">
                        <div className="min-w-0 max-w-48">
                          <div
                            className="font-medium text-ui-fg-base truncate text-sm"
                            title={booking.metadata?.guest_name || booking.guest_name || "Guest"}
                          >
                            {booking.metadata?.guest_name || booking.guest_name || "Guest"}
                          </div>
                          {(booking.guest_email || booking.metadata?.guest_email) && (
                            <div className="flex items-center text-sm text-ui-fg-subtle mt-0.5">
                              <span className="truncate">
                                {booking.guest_email || booking.metadata?.guest_email}
                              </span>
                            </div>
                          )}
                        </div>
                      </Table.Cell>
                      {/* Hotel */}
                      <Table.Cell className="w-40">
                        <Text className="text-sm text-ui-fg-base">
                          {booking.metadata?.hotel_name ||
                            (booking.metadata?.hotel_id
                              ? hotels.find(
                                  (h) => h.id === booking.metadata?.hotel_id
                                )?.name
                              : "Not specified")}
                        </Text>
                      </Table.Cell>

                      {/* Room Type */}
                      <Table.Cell className="w-32">
                        <Text className="text-sm text-ui-fg-base">
                          {booking.metadata?.room_type ||
                            booking.room_type ||
                            "Standard"}
                        </Text>
                      </Table.Cell>
                      {/* Duration */}
                      <Table.Cell className="w-24">
                        {(() => {
                          const checkIn =
                            booking.metadata?.check_in_date ||
                            booking.check_in_date;
                          const checkOut =
                            booking.metadata?.check_out_date ||
                            booking.check_out_date;
                          const nights = calculateDuration(checkIn, checkOut);
                          return nights > 0 ? (
                            <Badge
                              color="blue"
                              className="rounded-full text-xs px-3 py-1.5 font-medium"
                            >
                              {nights} {nights === 1 ? "Night" : "Nights"}
                            </Badge>
                          ) : (
                            <Text className="text-ui-fg-subtle text-sm">
                              -
                            </Text>
                          );
                        })()}
                      </Table.Cell>
                      {/* Booking Dates */}
                      <Table.Cell className="w-36">
                        <Text className="text-sm text-ui-fg-base">
                          {formatBookingDates(
                            booking.metadata?.check_in_date ||
                              booking.check_in_date,
                            booking.metadata?.check_out_date ||
                              booking.check_out_date
                          )}
                        </Text>
                      </Table.Cell>
                      {/* Status */}
                      <Table.Cell className="w-24">
                        {booking.status === "pending" ? (
                          booking.metadata?.room_id ? (
                            <Badge
                              color="green"
                              className="rounded-full text-xs px-3 py-1.5 font-medium"
                            >
                              Booked
                            </Badge>
                          ) : (
                            <Badge
                              color="orange"
                              className="rounded-full text-xs px-3 py-1.5 font-medium"
                            >
                              Reserved
                            </Badge>
                          )
                        ) : (
                          <Badge
                            color={statusColors[booking.status] || "grey"}
                            className="rounded-full text-xs px-3 py-1.5 font-medium"
                          >
                            {booking.status}
                          </Badge>
                        )}
                      </Table.Cell>
                      {/* Payment Status */}
                      <Table.Cell className="w-32">
                        {(() => {
                          const paymentStatus =
                            booking.metadata?.payment_status ||
                            booking.payment_status ||
                            "pending";
                          const paymentStatusColors: Record<string, any> = {
                            pending: "orange",
                            paid: "green",
                            failed: "red",
                            refunded: "grey",
                            partially_paid: "blue",
                            cancelled: "grey",
                          };

                          return (
                            <Badge
                              color={
                                paymentStatusColors[paymentStatus] || "grey"
                              }
                              className="rounded-full text-xs px-3 py-1.5 font-medium"
                            >
                              {paymentStatus
                                .replace(/_/g, " ")
                                .replace(/\b\w/g, (l: string) =>
                                  l.toUpperCase()
                                )}
                            </Badge>
                          );
                        })()}
                      </Table.Cell>
                      {/* Total */}
                      <Table.Cell className="w-24">
                        <Text className="text-sm text-ui-fg-base font-medium">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency:
                              booking.metadata?.currency_code ||
                              booking.currency_code ||
                              "USD",
                          }).format(
                            (booking.metadata?.total_amount ||
                              booking.total_amount ||
                              0) +
                              (booking.metadata?.add_on_total_amount || 0) / 100
                          )}
                        </Text>
                      </Table.Cell>

                      {/* Actions */}
                      <Table.Cell className="text-right w-16">
                        <div onClick={(e) => e.stopPropagation()}>
                          {renderActionDropdown(booking)}
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  );
                })}
            </Table.Body>
          </Table>
        )}
      </div>

      {/* Pagination Controls */}
      {!isLoading && bookings.length > 0 && (
        <div className="mt-4 flex items-center justify-between">
          {/* Page Size Selector */}
          <div className="flex items-center gap-2">
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => {
                const newPageSize = parseInt(value);
                updatePageSize(newPageSize);
              }}
            >
              <Select.Trigger className="w-20">
                <Select.Value />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="10">10</Select.Item>
                <Select.Item value="25">25</Select.Item>
                <Select.Item value="50">50</Select.Item>
                <Select.Item value="100">100</Select.Item>
              </Select.Content>
            </Select>
          </div>

          {/* Pagination */}
          <Table.Pagination
            count={totalCount}
            pageSize={pageSize}
            pageIndex={currentPage}
            pageCount={pageCount}
            canPreviousPage={canPreviousPage}
            canNextPage={canNextPage}
            previousPage={() => {
              updatePage(currentPage - 1);
            }}
            nextPage={() => {
              updatePage(currentPage + 1);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default BookingList;
