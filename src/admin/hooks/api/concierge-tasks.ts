import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";

// Types
export interface ConciergeTask {
  id: string;
  title: string;
  description?: string;
  status: "pending" | "in_progress" | "review" | "completed" | "cancelled";
  priority: "low" | "medium" | "high" | "urgent";
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  created_by?: string;
  updated_by?: string;
  due_date?: string;
  is_deleted: boolean;
  deleted_at?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface ConciergeTasksResponse {
  tasks: ConciergeTask[];
  count: number;
  limit: number;
  offset: number;
}

export interface ConciergeTaskFilters {
  limit?: number;
  offset?: number;
  q?: string;
  status?: string;
  priority?: string;
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  created_by?: string;
  due_date_gte?: string;
  due_date_lte?: string;
  created_at_gte?: string;
  created_at_lte?: string;
  order?: string;
  sort_order?: "asc" | "desc";
}

export interface CreateConciergeTaskData {
  title: string;
  description?: string;
  status?: "pending" | "in_progress" | "review" | "completed" | "cancelled";
  priority?: "low" | "medium" | "high" | "urgent";
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  due_date?: string;
  metadata?: Record<string, any>;
}

export interface UpdateConciergeTaskData {
  title?: string;
  description?: string;
  status?: "pending" | "in_progress" | "review" | "completed" | "cancelled";
  priority?: "low" | "medium" | "high" | "urgent";
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  due_date?: string;
  metadata?: Record<string, any>;
}

// API functions
const fetchConciergeTasks = async (filters: ConciergeTaskFilters = {}): Promise<ConciergeTasksResponse> => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      params.append(key, String(value));
    }
  });
  
  const response = await fetch(`/admin/concierge-management/tasks?${params.toString()}`, {
    credentials: "include",
  });
  
  if (!response.ok) {
    throw new Error("Failed to fetch concierge tasks");
  }
  
  return response.json();
};

const fetchBookingTasks = async (bookingId: string, filters: ConciergeTaskFilters = {}): Promise<ConciergeTasksResponse> => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      params.append(key, String(value));
    }
  });
  
  const response = await fetch(`/admin/concierge-management/bookings/${bookingId}/tasks?${params.toString()}`, {
    credentials: "include",
  });
  
  if (!response.ok) {
    throw new Error("Failed to fetch booking tasks");
  }
  
  return response.json();
};

const fetchConciergeTask = async (taskId: string): Promise<{ task: ConciergeTask }> => {
  const response = await fetch(`/admin/concierge-management/tasks/${taskId}`, {
    credentials: "include",
  });
  
  if (!response.ok) {
    throw new Error("Failed to fetch concierge task");
  }
  
  return response.json();
};

const createConciergeTask = async (data: CreateConciergeTaskData): Promise<{ task: ConciergeTask }> => {
  const response = await fetch("/admin/concierge-management/tasks", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to create concierge task");
  }
  
  return response.json();
};

const createBookingTask = async (bookingId: string, data: CreateConciergeTaskData): Promise<{ task: ConciergeTask }> => {
  const response = await fetch(`/admin/concierge-management/bookings/${bookingId}/tasks`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to create booking task");
  }
  
  return response.json();
};

const updateConciergeTask = async (taskId: string, data: UpdateConciergeTaskData): Promise<{ task: ConciergeTask }> => {
  const response = await fetch(`/admin/concierge-management/tasks/${taskId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to update concierge task");
  }
  
  return response.json();
};

const deleteConciergeTask = async (taskId: string): Promise<void> => {
  const response = await fetch(`/admin/concierge-management/tasks/${taskId}`, {
    method: "DELETE",
    credentials: "include",
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to delete concierge task");
  }
};

const assignConciergeTask = async (taskId: string, assignedTo: string): Promise<{ task: ConciergeTask }> => {
  const response = await fetch(`/admin/concierge-management/tasks/${taskId}/assign`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    body: JSON.stringify({ assigned_to: assignedTo }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to assign concierge task");
  }
  
  return response.json();
};

const completeConciergeTask = async (taskId: string): Promise<{ task: ConciergeTask }> => {
  const response = await fetch(`/admin/concierge-management/tasks/${taskId}/complete`, {
    method: "POST",
    credentials: "include",
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to complete concierge task");
  }
  
  return response.json();
};

// React Query hooks
export const useConciergeTasks = (filters: ConciergeTaskFilters = {}) => {
  return useQuery({
    queryKey: ["concierge-tasks", filters],
    queryFn: () => fetchConciergeTasks(filters),
    staleTime: 30000, // 30 seconds
  });
};

export const useBookingTasks = (bookingId: string, filters: ConciergeTaskFilters = {}) => {
  return useQuery({
    queryKey: ["booking-tasks", bookingId, filters],
    queryFn: () => fetchBookingTasks(bookingId, filters),
    staleTime: 30000, // 30 seconds
    enabled: !!bookingId,
  });
};

export const useConciergeTask = (taskId: string) => {
  return useQuery({
    queryKey: ["concierge-task", taskId],
    queryFn: () => fetchConciergeTask(taskId),
    enabled: !!taskId,
  });
};

export const useCreateConciergeTask = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: createConciergeTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["concierge-tasks"] });
      toast.success("Task created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useCreateBookingTask = (bookingId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateConciergeTaskData) => createBookingTask(bookingId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["booking-tasks", bookingId] });
      queryClient.invalidateQueries({ queryKey: ["concierge-tasks"] });
      toast.success("Task created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useUpdateConciergeTask = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ taskId, data }: { taskId: string; data: UpdateConciergeTaskData }) => 
      updateConciergeTask(taskId, data),
    onSuccess: (_, { taskId }) => {
      queryClient.invalidateQueries({ queryKey: ["concierge-task", taskId] });
      queryClient.invalidateQueries({ queryKey: ["concierge-tasks"] });
      queryClient.invalidateQueries({ queryKey: ["booking-tasks"] });
      toast.success("Task updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useDeleteConciergeTask = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: deleteConciergeTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["concierge-tasks"] });
      queryClient.invalidateQueries({ queryKey: ["booking-tasks"] });
      toast.success("Task deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useAssignConciergeTask = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ taskId, assignedTo }: { taskId: string; assignedTo: string }) => 
      assignConciergeTask(taskId, assignedTo),
    onSuccess: (_, { taskId }) => {
      queryClient.invalidateQueries({ queryKey: ["concierge-task", taskId] });
      queryClient.invalidateQueries({ queryKey: ["concierge-tasks"] });
      queryClient.invalidateQueries({ queryKey: ["booking-tasks"] });
      toast.success("Task assigned successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useCompleteConciergeTask = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: completeConciergeTask,
    onSuccess: (_, taskId) => {
      queryClient.invalidateQueries({ queryKey: ["concierge-task", taskId] });
      queryClient.invalidateQueries({ queryKey: ["concierge-tasks"] });
      queryClient.invalidateQueries({ queryKey: ["booking-tasks"] });
      toast.success("Task completed successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};
