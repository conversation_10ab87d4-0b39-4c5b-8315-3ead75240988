import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";
import { useState } from "react";
import * as XLSX from "xlsx";

// Types
export interface ProductService {
  id: string;
  name: string;
  type: "Product" | "Service";
  description?: string;
  base_cost?: number; // Base cost in CHF
  custom_fields?: Record<string, any>; // JSON object for dynamic field values
  status: "active" | "inactive";
  category_id: string;
  unit_type_id: string;
  // Service Level and Location
  service_level?: "hotel" | "destination";
  hotel_id?: string; // JSON array of hotel IDs for hotel-level services
  destination_id?: string; // JSON array of destination IDs for destination-level services

  // Price Tracking Fields for Flagging System
  highest_price?: number | null; // Highest price seen from supplier offerings
  highest_price_currency?: string | null; // Currency of the highest price
  price_flag_active?: boolean; // Whether a price flag is currently active
  price_flag_created_at?: string | null; // When the current price flag was created (ISO string)
  price_flag_supplier_offering_id?: string | null; // ID of supplier offering that triggered the flag
  category?: {
    id: string;
    name: string;
    description?: string;
    is_active: boolean;
    dynamic_field_schema?: Array<{
      label: string;
      key: string;
      type: string;
      options?: string[];
      required: boolean;
      used_in_filtering?: boolean;
    }>;
  };
  unit_type?: {
    id: string;
    name: string;
    description?: string;
    is_active: boolean;
  };
  tags?: Array<{
    id: string;
    name: string;
    color?: string;
  }>;
  suppliers?: Array<{
    id: string;
    supplier_id: string;
    cost: number;
    availability: string;
    season?: string;
    supplier?: {
      id: string;
      name: string;
      type: string;
      status: string;
    };
  }>;
  created_at: string;
  updated_at: string;
}

export interface CreateProductServiceInput {
  name?: string; // Optional - will be auto-generated if not provided
  type: "Product" | "Service";
  description?: string;
  base_cost?: number; // Base cost in CHF
  custom_fields?: Record<string, any>; // JSON object for dynamic field values
  status?: "active" | "inactive";
  category_id: string;
  unit_type_id: string;
  tag_ids?: string[];
  service_level?: "hotel" | "destination";
  hotel_id?: string; // JSON array of hotel IDs for hotel-level services
  destination_id?: string; // JSON array of destination IDs for destination-level services
}

export interface UpdateProductServiceInput {
  name?: string;
  type?: "Product" | "Service";
  description?: string;
  base_cost?: number; // Base cost in CHF
  custom_fields?: Record<string, any>; // JSON object for dynamic field values
  status?: "active" | "inactive";
  category_id?: string;
  unit_type_id?: string;
  tag_ids?: string[];
  service_level?: "hotel" | "destination";
  hotel_id?: string; // JSON array of hotel IDs for hotel-level services
  destination_id?: string; // JSON array of destination IDs for destination-level services

  // Price Tracking Fields for Flagging System
  highest_price?: number | null; // Highest price seen from supplier offerings
  highest_price_currency?: string | null; // Currency of the highest price
  price_flag_active?: boolean; // Whether a price flag is currently active
  price_flag_created_at?: Date | null; // When the current price flag was created
  price_flag_supplier_offering_id?: string | null; // ID of supplier offering that triggered the flag
}

export interface ProductServiceFilters {
  name?: string;
  type?: "Product" | "Service";
  status?: "active" | "inactive";
  category_id?: string;
  unit_type_id?: string;
  tag_ids?: string[];
  limit?: number;
  offset?: number;
  sort_by?: "name" | "type" | "category" | "base_cost" | "status" | "updated_at" | "created_at";
  sort_order?: "asc" | "desc";
}

export interface ProductServiceListResponse {
  product_services: ProductService[];
  count: number;
  limit: number;
  offset: number;
}

// Query keys
const QUERY_KEYS = {
  all: ["supplier-products-services"] as const,
  lists: () => [...QUERY_KEYS.all, "list"] as const,
  list: (filters: ProductServiceFilters) =>
    [...QUERY_KEYS.lists(), filters] as const,
  details: () => [...QUERY_KEYS.all, "detail"] as const,
  detail: (id: string) => [...QUERY_KEYS.details(), id] as const,
};

// Hook to fetch products/services list
export const useProductsServices = (filters: ProductServiceFilters = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.list(filters),
    queryFn: async (): Promise<ProductServiceListResponse> => {
      const params = new URLSearchParams();

      if (filters.name) params.append("name", filters.name);
      if (filters.type) params.append("type", filters.type);
      if (filters.status) params.append("status", filters.status);
      if (filters.category_id)
        params.append("category_id", filters.category_id);
      if (filters.unit_type_id)
        params.append("unit_type_id", filters.unit_type_id);
      if (filters.tag_ids) {
        if (Array.isArray(filters.tag_ids)) {
          params.append("tag_ids", filters.tag_ids.join(","));
        } else {
          params.append("tag_ids", filters.tag_ids);
        }
      }
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      if (filters.sort_by) params.append("sort_by", filters.sort_by);
      if (filters.sort_order) params.append("sort_order", filters.sort_order);

      const url = `/admin/supplier-management/products-services${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      const response = await fetch(url, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch products/services");
      }

      return response.json();
    },
  });
};

// Hook to fetch a single product/service
export const useProductService = (id: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.detail(id),
    queryFn: async (): Promise<{ product_service: ProductService }> => {
      const response = await fetch(
        `/admin/supplier-management/products-services/${id}`,
        {
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch product/service");
      }

      return response.json();
    },
    enabled: !!id,
  });
};

// Hook to create a product/service
export const useCreateProductService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: CreateProductServiceInput
    ): Promise<{ product_service: ProductService }> => {
      const response = await fetch(
        "/admin/supplier-management/products-services",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        const error = new Error(
          errorData.message || "Failed to create product/service"
        );
        // Preserve error type for better error handling
        (error as any).type = errorData.type;
        throw error;
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Product/Service created successfully");
    },
    onError: (error: Error) => {
      // Handle specific error types
      const errorType = (error as any).type;
      if (
        errorType === "duplicate_error" ||
        error.message.includes("already exists")
      ) {
        toast.error("Product already exists");
      } else {
        toast.error(error.message);
      }
    },
  });
};

// Hook to update a product/service
export const useUpdateProductService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateProductServiceInput;
    }): Promise<{ product_service: ProductService }> => {
      const response = await fetch(
        `/admin/supplier-management/products-services/${id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        const error = new Error(
          errorData.message || "Failed to update product/service"
        );
        // Preserve error type for better error handling
        (error as any).type = errorData.type;
        throw error;
      }

      return response.json();
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.detail(id) });
      toast.success("Product/Service updated successfully");
    },
    onError: (error: Error) => {
      // Handle specific error types
      const errorType = (error as any).type;
      if (
        errorType === "duplicate_error" ||
        error.message.includes("already exists")
      ) {
        toast.error("Product already exists");
      } else {
        toast.error(error.message);
      }
    },
  });
};

// Hook to delete a product/service
export const useDeleteProductService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      id: string
    ): Promise<{ deleted: boolean; id: string }> => {
      const response = await fetch(
        `/admin/supplier-management/products-services/${id}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        const error = new Error(
          errorData.message || "Failed to delete product/service"
        );
        // Preserve error type and status for better error handling
        (error as any).type = errorData.type;
        (error as any).status = response.status;
        throw error;
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Product/Service deleted successfully");
    },
    onError: (error: Error) => {
      // Handle specific error types
      const errorType = (error as any).type;
      const status = (error as any).status;

      if (status === 409 && errorType === "reference_constraint_error") {
        // Extract the specific constraint details from the error message
        const message = error.message;
        if (
          message.includes("supplier offering(s)") &&
          message.includes("supplier link(s)")
        ) {
          toast.error(
            "Cannot delete: This product/service is currently being used by suppliers and has active offerings. Please remove all supplier links and offerings first."
          );
        } else if (message.includes("supplier offering(s)")) {
          toast.error(
            "Cannot delete: This product/service has active supplier offerings. Please remove all offerings first."
          );
        } else if (message.includes("supplier link(s)")) {
          toast.error(
            "Cannot delete: This product/service is linked to suppliers. Please remove all supplier links first."
          );
        } else {
          toast.error(
            "Cannot delete: This product/service is currently being used. Please remove all references first."
          );
        }
      } else {
        toast.error(error.message);
      }
    },
  });
};

// Import/Export Types
export interface ImportValidationError {
  row: number;
  field: string;
  message: string;
  value?: any;
}

export interface ImportProgress {
  processed: number;
  total: number;
  percentage: number;
  currentItem?: string;
}

export interface ImportResult {
  success: boolean;
  imported: number;
  updated?: number;
  errors: ImportValidationError[];
  message: string;
}

// Template generation utility for products/services
export const generateProductServiceTemplate = async () => {
  try {
    // Fetch categories, unit types, and tags for template generation
    const [categoriesRes, unitTypesRes, tagsRes] = await Promise.all([
      fetch("/admin/supplier-management/products-services/categories", {
        credentials: "include",
      }),
      fetch("/admin/supplier-management/products-services/unit-types", {
        credentials: "include",
      }),
      fetch("/admin/supplier-management/products-services/tags", {
        credentials: "include",
      }),
    ]);

    const categoriesData = await categoriesRes.json();
    const unitTypesData = await unitTypesRes.json();
    const tagsData = await tagsRes.json();

    const categories = categoriesData.categories || [];
    const unitTypes = unitTypesData.unit_types || [];
    const tags = tagsData.tags || [];

    // Create template data with different category combinations
    const templateData = [];

    // Example 1: Kids Club Service with dynamic fields
    const kidsClubCategory = categories.find(
      (c: any) =>
        c.name.toLowerCase().includes("kids") ||
        c.name.toLowerCase().includes("child")
    );
    if (kidsClubCategory) {
      const kidsClubUnitType = unitTypes.find(
        (ut: any) =>
          ut.name.toLowerCase().includes("person") ||
          ut.name.toLowerCase().includes("child")
      );
      // Initialize template record with basic fields
      const templateRecord1 = {
        type: "Service",
        description: "Supervised indoor activities for children",
        base_cost: 25.0,
        status: "active",
        category_id: kidsClubCategory.id,
        category_name: kidsClubCategory.name, // For reference
        unit_type_id: kidsClubUnitType?.id || unitTypes[0]?.id,
        unit_type_name: kidsClubUnitType?.name || unitTypes[0]?.name, // For reference
        tag_ids: tags
          .slice(0, 2)
          .map((t: any) => t.id)
          .join(","),
        tag_names: tags
          .slice(0, 2)
          .map((t: any) => t.name)
          .join(","), // For reference
      };

      // Add dynamic fields from category schema and collect required field values for name generation
      const requiredFieldValues: any[] = [];
      if (kidsClubCategory.dynamic_field_schema) {
        kidsClubCategory.dynamic_field_schema.forEach((field: any) => {
          let value = "";
          switch (field.type) {
            case "text":
              value = field.key === "age_range" ? "5-12" : "Sample text";
              break;
            case "number":
              value = field.key === "duration_hours" ? "2" : "10";
              break;
            case "dropdown":
              if (field.key === "activity_type") value = "Indoor";
              else value = field.options?.[0] || "Option 1";
              break;
            case "multi-select":
              value =
                field.options?.slice(0, 2).join(",") || "Option 1,Option 2";
              break;
            case "boolean":
              value = "true";
              break;
            case "date":
              value = "2024-01-01";
              break;
            case "number-range":
              value = "5-12";
              break;
            default:
              value = "Sample value";
          }
          (templateRecord1 as any)[`custom_field_${field.key}`] = value;

          // Collect required field values for name generation (excluding cost)
          if (field.required && field.key !== "base_cost") {
            requiredFieldValues.push(value);
          }
        });
      }

      // Note: Name will be auto-generated from category + required fields, so we don't include it in template
      templateData.push(templateRecord1);
    }

    // Example 2: Transportation Service
    const transportCategory = categories.find((c: any) =>
      c.name.toLowerCase().includes("transport")
    );
    if (transportCategory) {
      const transportUnitType = unitTypes.find(
        (ut: any) =>
          ut.name.toLowerCase().includes("trip") ||
          ut.name.toLowerCase().includes("journey")
      );
      // Initialize template record with basic fields
      const templateRecord2 = {
        type: "Service",
        description: "Premium airport transfer service",
        base_cost: 85.0,
        status: "active",
        category_id: transportCategory.id,
        category_name: transportCategory.name,
        unit_type_id: transportUnitType?.id || unitTypes[0]?.id,
        unit_type_name: transportUnitType?.name || unitTypes[0]?.name,
        tag_ids: tags
          .slice(1, 3)
          .map((t: any) => t.id)
          .join(","),
        tag_names: tags
          .slice(1, 3)
          .map((t: any) => t.name)
          .join(","),
      };

      // Add dynamic fields from category schema and collect required field values for name generation
      const requiredFieldValues2: any[] = [];
      if (transportCategory.dynamic_field_schema) {
        transportCategory.dynamic_field_schema.forEach((field: any) => {
          let value = "";
          switch (field.type) {
            case "dropdown":
              if (field.key === "vehicle_type") value = "Sedan";
              else value = field.options?.[0] || "Option 1";
              break;
            case "number":
              if (field.key === "passenger_capacity") value = "4";
              else value = "10";
              break;
            case "text":
              value = "Sample text";
              break;
            case "multi-select":
              value =
                field.options?.slice(0, 2).join(",") || "Option 1,Option 2";
              break;
            case "boolean":
              value = "true";
              break;
            case "date":
              value = "2024-01-01";
              break;
            case "number-range":
              value = "1-8";
              break;
            default:
              value = "Sample value";
          }
          (templateRecord2 as any)[`custom_field_${field.key}`] = value;

          // Collect required field values for name generation (excluding cost)
          if (field.required && field.key !== "base_cost") {
            requiredFieldValues2.push(value);
          }
        });
      }

      // Note: Name will be auto-generated from category + required fields, so we don't include it in template
      templateData.push(templateRecord2);
    }

    // Example 3: Accommodation Product
    const accommodationCategory = categories.find(
      (c: any) =>
        c.name.toLowerCase().includes("accommodation") ||
        c.name.toLowerCase().includes("room")
    );
    if (accommodationCategory) {
      const accommodationUnitType = unitTypes.find(
        (ut: any) =>
          ut.name.toLowerCase().includes("night") ||
          ut.name.toLowerCase().includes("room")
      );
      // Initialize template record with basic fields
      const templateRecord3 = {
        type: "Product",
        description: "Luxury room with panoramic sea views",
        base_cost: 180.0,
        status: "active",
        category_id: accommodationCategory.id,
        category_name: accommodationCategory.name,
        unit_type_id: accommodationUnitType?.id || unitTypes[0]?.id,
        unit_type_name: accommodationUnitType?.name || unitTypes[0]?.name,
        tag_ids: tags
          .slice(2, 4)
          .map((t: any) => t.id)
          .join(","),
        tag_names: tags
          .slice(2, 4)
          .map((t: any) => t.name)
          .join(","),
      };

      // Add dynamic fields from category schema and collect required field values for name generation
      const requiredFieldValues3: any[] = [];
      if (accommodationCategory.dynamic_field_schema) {
        accommodationCategory.dynamic_field_schema.forEach((field: any) => {
          let value = "";
          switch (field.type) {
            case "dropdown":
              if (field.key === "room_type") value = "Suite";
              else value = field.options?.[0] || "Option 1";
              break;
            case "multi-select":
              if (field.key === "amenities") value = "WiFi,Sea View,Mini Bar";
              else
                value =
                  field.options?.slice(0, 2).join(",") || "Option 1,Option 2";
              break;
            case "boolean":
              if (field.key === "pet_friendly") value = "false";
              else value = "true";
              break;
            case "number":
              if (field.key === "max_capacity") value = "2";
              else value = "10";
              break;
            case "text":
              value = "Sample text";
              break;
            case "date":
              value = "2024-01-01";
              break;
            case "number-range":
              value = "1-4";
              break;
            default:
              value = "Sample value";
          }
          (templateRecord3 as any)[`custom_field_${field.key}`] = value;

          // Collect required field values for name generation (excluding cost)
          if (field.required && field.key !== "base_cost") {
            requiredFieldValues3.push(value);
          }
        });
      }

      // Note: Name will be auto-generated from category + required fields, so we don't include it in template
      templateData.push(templateRecord3);
    }

    // If no specific categories found, create generic examples
    if (templateData.length === 0) {
      templateData.push({
        // Note: name will be auto-generated from category + required fields
        type: "Product",
        description: "Sample product description",
        base_cost: 50.0,
        status: "active",
        category_id: categories[0]?.id || "psc_01234567890123456789",
        category_name: categories[0]?.name || "Sample Category",
        unit_type_id: unitTypes[0]?.id || "psut_01234567890123456789",
        unit_type_name: unitTypes[0]?.name || "Per Unit",
        tag_ids:
          tags
            .slice(0, 2)
            .map((t: any) => t.id)
            .join(",") || "pst_01234567890123456789,pst_01234567890123456790",
        tag_names:
          tags
            .slice(0, 2)
            .map((t: any) => t.name)
            .join(",") || "Tag 1,Tag 2",
      });

      templateData.push({
        // Note: name will be auto-generated from category + required fields
        type: "Service",
        description: "Sample service description",
        base_cost: 75.0,
        status: "active",
        category_id:
          categories[1]?.id || categories[0]?.id || "psc_01234567890123456789",
        category_name:
          categories[1]?.name || categories[0]?.name || "Sample Category",
        unit_type_id:
          unitTypes[1]?.id || unitTypes[0]?.id || "psut_01234567890123456789",
        unit_type_name: unitTypes[1]?.name || unitTypes[0]?.name || "Per Unit",
        tag_ids:
          tags
            .slice(1, 3)
            .map((t: any) => t.id)
            .join(",") || "pst_01234567890123456790,pst_01234567890123456791",
        tag_names:
          tags
            .slice(1, 3)
            .map((t: any) => t.name)
            .join(",") || "Tag 2,Tag 3",
      });
    }

    return templateData;
  } catch (error) {
    // Return basic template if API calls fail
    return [
      {
        name: "Sample Product",
        type: "Product",
        description: "Sample product description",
        base_cost: 50.0,
        status: "active",
        category_id: "psc_01234567890123456789",
        category_name: "Sample Category",
        unit_type_id: "psut_01234567890123456789",
        unit_type_name: "Per Unit",
        tag_ids: "pst_01234567890123456789,pst_01234567890123456790",
        tag_names: "Tag 1,Tag 2",
      },
    ];
  }
};

// CSV export utility
export const exportToCSV = (data: any[], filename: string) => {
  const csvContent = convertToCSV(data);
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${filename}_${new Date().toISOString().split("T")[0]}.csv`
  );
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Excel export utility
export const exportToExcel = (data: any[], filename: string) => {
  const worksheet = XLSX.utils.json_to_sheet(data);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Products_Services");

  // Auto-size columns
  const colWidths = Object.keys(data[0] || {}).map((key) => ({
    wch: Math.max(key.length, 15),
  }));
  worksheet["!cols"] = colWidths;

  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${filename}_${new Date().toISOString().split("T")[0]}.xlsx`
  );
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Helper function to convert data to CSV
const convertToCSV = (data: any[]): string => {
  if (!data.length) return "";

  const headers = Object.keys(data[0]);
  const csvRows = [
    headers.join(","),
    ...data.map((row) =>
      headers
        .map((header) => {
          const value = row[header];
          // Handle JSON strings and special characters
          if (
            typeof value === "string" &&
            (value.includes(",") || value.includes('"') || value.includes("\n"))
          ) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value || "";
        })
        .join(",")
    ),
  ];

  return csvRows.join("\n");
};

// Validation function for import data
const validateImportData = async (
  data: any[]
): Promise<ImportValidationError[]> => {
  const errors: ImportValidationError[] = [];

  try {
    // Fetch reference data for validation
    const [categoriesRes, unitTypesRes] = await Promise.all([
      fetch("/admin/supplier-management/products-services/categories", {
        credentials: "include",
      }),
      fetch("/admin/supplier-management/products-services/unit-types", {
        credentials: "include",
      }),
    ]);

    const categoriesData = await categoriesRes.json();
    const unitTypesData = await unitTypesRes.json();

    const categories = categoriesData.categories || [];
    const unitTypes = unitTypesData.unit_types || [];

    data.forEach((row, index) => {
      const rowNumber = index + 2; // +2 because Excel rows start at 1 and we have headers

      // Note: Name validation removed since names are auto-generated from category + required fields

      // Type validation
      if (!row.type || !["Product", "Service"].includes(row.type)) {
        errors.push({
          row: rowNumber,
          field: "type",
          message: 'Type must be either "Product" or "Service"',
          value: row.type,
        });
      }

      // Category validation
      if (!row.category_id) {
        errors.push({
          row: rowNumber,
          field: "category_id",
          message: "Category ID is required",
          value: row.category_id,
        });
      } else {
        const categoryExists = categories.some((c: any) => c.id === row.category_id);
        if (!categoryExists) {
          errors.push({
            row: rowNumber,
            field: "category_id",
            message: "Category ID does not exist",
            value: row.category_id,
          });
        }
      }

      // Unit type validation
      if (!row.unit_type_id) {
        errors.push({
          row: rowNumber,
          field: "unit_type_id",
          message: "Unit Type ID is required",
          value: row.unit_type_id,
        });
      } else {
        const unitTypeExists = unitTypes.some(
          (ut: any) => ut.id === row.unit_type_id
        );
        if (!unitTypeExists) {
          errors.push({
            row: rowNumber,
            field: "unit_type_id",
            message: "Unit Type ID does not exist",
            value: row.unit_type_id,
          });
        }
      }

      // Status validation
      if (row.status && !["active", "inactive"].includes(row.status)) {
        errors.push({
          row: rowNumber,
          field: "status",
          message: 'Status must be either "active" or "inactive"',
          value: row.status,
        });
      }

      // Base cost validation
      if (
        row.base_cost !== undefined &&
        row.base_cost !== null &&
        row.base_cost !== ""
      ) {
        const cost = parseFloat(row.base_cost);
        if (isNaN(cost) || cost < 0) {
          errors.push({
            row: rowNumber,
            field: "base_cost",
            message: "Base cost must be a valid positive number",
            value: row.base_cost,
          });
        }
      }



      // Dynamic field validation based on category
      const category = categories.find((c: any) => c.id === row.category_id);
      if (category && category.dynamic_field_schema) {
        // Define base fields that should never be treated as custom fields
        const baseFields = new Set([
          'type', 'description', 'status', 'base_cost', 'category_id', 'category_name',
          'unit_type_id', 'unit_type_name', 'name'
        ]);

        category.dynamic_field_schema.forEach((field: any) => {
          // Skip validation if field is not used in products
          if (field.used_in_product === false) {
            console.log(`Skipping field ${field.key} - not used in products`);
            return;
          }

          // Skip if this field key conflicts with a base field
          if (baseFields.has(field.key)) {
            console.log(`Skipping field ${field.key} - conflicts with base field`);
            return;
          }

          // Handle number-range fields differently (they have _from and _to columns)
          if (field.type === 'number-range') {
            const fromFieldKey = `custom_field_${field.key}_from`;
            const toFieldKey = `custom_field_${field.key}_to`;
            const directFromKey = `${field.key}_from`;
            const directToKey = `${field.key}_to`;

            let fromValue = row[fromFieldKey] || row[directFromKey]; // Check both formats
            let toValue = row[toFieldKey] || row[directToKey]; // Check both formats

            // Check custom_fields object as fallback
            if (
              (!fromValue || !toValue) &&
              row.custom_fields &&
              typeof row.custom_fields === "object"
            ) {
              const combinedValue = row.custom_fields[field.key];
              if (combinedValue && typeof combinedValue === 'string' && combinedValue.includes('-')) {
                const [from, to] = combinedValue.split('-');
                fromValue = fromValue || from?.trim();
                toValue = toValue || to?.trim();
              }
            }

            // Required field validation for number-range
            if (field.required) {
              if (!fromValue || fromValue.toString().trim() === "") {
                errors.push({
                  row: rowNumber,
                  field: fromFieldKey,
                  message: `${field.label} (from) is required`,
                  value: fromValue,
                });
              }
              if (!toValue || toValue.toString().trim() === "") {
                errors.push({
                  row: rowNumber,
                  field: toFieldKey,
                  message: `${field.label} (to) is required`,
                  value: toValue,
                });
              }
            }
          } else {
            // Handle regular fields
            const prefixedFieldKey = `custom_field_${field.key}`;
            const directFieldKey = field.key;

            // Check both prefixed field key, direct field key, and custom_fields object
            let fieldValue = row[prefixedFieldKey] || row[directFieldKey];
            if (
              !fieldValue &&
              row.custom_fields &&
              typeof row.custom_fields === "object"
            ) {
              fieldValue = row.custom_fields[field.key];
            }

            // Determine which field name to use in error reporting
            const actualFieldKey = row[prefixedFieldKey] !== undefined ? prefixedFieldKey : directFieldKey;

            // Required field validation
            if (
              field.required &&
              (!fieldValue || fieldValue.toString().trim() === "")
            ) {
              errors.push({
                row: rowNumber,
                field: actualFieldKey,
                message: `${field.label} is required`,
                value: fieldValue,
              });
            }

            // Type-specific validation for regular fields
            if (fieldValue && fieldValue.toString().trim() !== "") {
            switch (field.type) {
              case "number":
                if (isNaN(parseFloat(fieldValue))) {
                  errors.push({
                    row: rowNumber,
                    field: actualFieldKey,
                    message: `${field.label} must be a valid number`,
                    value: fieldValue,
                  });
                }
                break;
              case "dropdown":
                if (field.options && !field.options.includes(fieldValue)) {
                  errors.push({
                    row: rowNumber,
                    field: actualFieldKey,
                    message: `${
                      field.label
                    } must be one of: ${field.options.join(", ")}`,
                    value: fieldValue,
                  });
                }
                break;
              case "multi-select":
                if (field.options) {
                  // Ensure fieldValue is a string before splitting
                  const fieldValueStr = String(fieldValue || '');
                  const selectedValues = fieldValueStr
                    .split(",")
                    .map((v) => v.trim())
                    .filter(v => v.length > 0); // Remove empty values
                  const invalidValues = selectedValues.filter(
                    (v) => !field.options.includes(v)
                  );
                  if (invalidValues.length > 0) {
                    errors.push({
                      row: rowNumber,
                      field: actualFieldKey,
                      message: `${
                        field.label
                      } contains invalid options: ${invalidValues.join(
                        ", "
                      )}. Valid options: ${field.options.join(", ")}`,
                      value: fieldValue,
                    });
                  }
                }
                break;
              case "boolean":
                if (
                  !["true", "false", "1", "0", "yes", "no"].includes(
                    fieldValue.toString().toLowerCase()
                  )
                ) {
                  errors.push({
                    row: rowNumber,
                    field: actualFieldKey,
                    message: `${field.label} must be a boolean value (true/false, 1/0, yes/no)`,
                    value: fieldValue,
                  });
                }
                break;
              case "date":
                if (isNaN(Date.parse(fieldValue))) {
                  errors.push({
                    row: rowNumber,
                    field: actualFieldKey,
                    message: `${field.label} must be a valid date`,
                    value: fieldValue,
                  });
                }
                break;
              case "number-range":
                const rangePattern = /^\d+(-\d+)?$/;
                if (!rangePattern.test(fieldValue.toString())) {
                  errors.push({
                    row: rowNumber,
                    field: actualFieldKey,
                    message: `${field.label} must be in format "min-max" or single number`,
                    value: fieldValue,
                  });
                }
                break;
            }
          }
          }
        });
      }
    });
  } catch (error) {
    errors.push({
      row: 0,
      field: 'general',
      message: `Failed to validate data: ${error instanceof Error ? error.message : 'Unknown error'}`,
      value: null
    });
  }

  return errors;
};

// Custom hook for product/service import/export operations
export const useProductServiceImportExport = () => {
  const [importProgress, setImportProgress] = useState<ImportProgress>({
    processed: 0,
    total: 0,
    percentage: 0,
  });
  const queryClient = useQueryClient();

  // File parsing function
  const parseImportFile = async (
    file: File,
    selectedCategoryId?: string
  ): Promise<{ data: any[]; errors: ImportValidationError[] }> => {
    try {
      let data: any[] = [];

      if (file.name.endsWith(".csv") || file.type === "text/csv") {
        // Parse CSV
        const text = await file.text();

        const lines = text.split("\n").filter((line) => line.trim());
        if (lines.length < 2)
          throw new Error(
            "CSV file must have at least a header row and one data row"
          );

        // Better CSV parsing to handle quoted values
        const parseCSVLine = (line: string): string[] => {
          const result: string[] = [];
          let current = "";
          let inQuotes = false;

          for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === '"') {
              inQuotes = !inQuotes;
            } else if (char === "," && !inQuotes) {
              result.push(current.trim());
              current = "";
            } else {
              current += char;
            }
          }
          result.push(current.trim());
          return result;
        };

        const headers = parseCSVLine(lines[0]).map((h) => h.replace(/"/g, ""));

        data = lines.slice(1).map((line) => {
          const values = parseCSVLine(line);
          const row: any = {};
          headers.forEach((header, headerIndex) => {
            row[header] = values[headerIndex] || "";
          });
          return row;
        });
      } else if (
        file.name.endsWith(".xlsx") ||
        file.name.endsWith(".xls") ||
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel"
      ) {
        // Parse Excel
        const buffer = await file.arrayBuffer();
        const workbook = XLSX.read(buffer, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        data = XLSX.utils.sheet_to_json(worksheet, { defval: "" });
      } else {
        throw new Error(
          `Unsupported file format: ${file.type}. Please use CSV or Excel files.`
        );
      }

      // Fetch categories, unit types, hotels/destinations for field processing
      const [categoriesResponse, unitTypesResponse, hotelsResponse, destinationsResponse] = await Promise.all([
        fetch('/admin/supplier-management/products-services/categories?is_active=true&limit=100', {
          credentials: 'include'
        }).then(async res => {
          if (res.ok) {
            const data = await res.json();
            return data;
          } else {
            return { categories: [] };
          }
        }).catch(() => {
          return { categories: [] };
        }),
        fetch('/admin/supplier-management/products-services/unit-types?is_active=true&limit=100', {
          credentials: 'include'
        }).then(async res => {
          if (res.ok) {
            const data = await res.json();
            return data;
          } else {
            return { unit_types: [] };
          }
        }).catch(() => {
          return { unit_types: [] };
        }),

        fetch('/admin/hotel-management/hotels/list-basic?limit=100', {
          credentials: 'include'
        }).then(res => res.ok ? res.json() : { hotels: [] }).catch(() => ({ hotels: [] })),
        fetch('/admin/hotel-management/destinations/list-basic?limit=100', {
          credentials: 'include'
        }).then(res => res.ok ? res.json() : { destinations: [] }).catch(() => ({ destinations: [] }))
      ]);

      const categories = categoriesResponse?.categories || [];
      const unitTypes = unitTypesResponse?.unit_types || [];
      const hotels = hotelsResponse?.hotels || [];
      const destinations = destinationsResponse?.destinations || [];

      // Create lookup maps for efficient processing
      const categoryMap = new Map(categories.map((cat: any) => [cat.id, cat]));
      const categoryNameToIdMap = new Map(categories.map((cat: any) => [cat.name?.toLowerCase(), cat.id]));
      const unitTypeNameToIdMap = new Map(unitTypes.map((ut: any) => [ut.name?.toLowerCase(), ut.id]));
      const hotelNameToIdMap = new Map(hotels.map((hotel: any) => [hotel.name?.toLowerCase(), hotel.id]));
      const destinationNameToIdMap = new Map(destinations.map((dest: any) => [dest.name?.toLowerCase(), dest.id]));

      // Determine category ID with multiple fallback methods
      let categoryId: string | undefined;

      // Method 1: Use selectedCategoryId if provided
      if (selectedCategoryId) {
        const category = categories.find((cat: any) => cat.id === selectedCategoryId);
        if (category) {
          categoryId = selectedCategoryId;
        }
      }

      // Method 2: Extract category from filename if not already set
      if (!categoryId && file.name.includes('_')) {
        const filenameParts = file.name.split('_');

        // Look for category name in filename (e.g., products_services_template_Accommodation_2025-07-14.xlsx)
        if (filenameParts.length >= 4) {
          const categoryName = filenameParts[3]; // Accommodation

          const category = categories.find((cat: any) =>
            cat.name.toLowerCase() === categoryName.toLowerCase()
          );
          if (category) {
            categoryId = category.id;
          }
        }
      }

      // Method 3: Use first category as fallback
      if (!categoryId && categories.length > 0) {
        categoryId = categories[0].id;
      }

      // Transform data
      const transformedData = data.map((row) => {
        const transformed: any = {
          // Note: name will be auto-generated from category + required fields, so we don't process it from import
          type: row.type,
          description: row.description || undefined,
          // Convert string booleans to actual booleans for status
          status:
            row.status === undefined
              ? "active"
              : ["active", "inactive"].includes(row.status)
              ? row.status
              : "active",
          // Parse base_cost as number
          base_cost: row.base_cost ? parseFloat(row.base_cost) : undefined,
        };

        // Convert category_name to category_id
        if (row.category_name) {
          const categoryId = categoryNameToIdMap.get(row.category_name.toLowerCase());
          if (categoryId) {
            transformed.category_id = categoryId;
          }
        } else if (row.category_id) {
          // Fallback to direct category_id if provided
          transformed.category_id = row.category_id;
        } else if (categoryId) {
          // Use the detected/selected category ID
          transformed.category_id = categoryId;
        }

        // Convert unit_type_name to unit_type_id
        if (row.unit_type_name) {
          const unitTypeId = unitTypeNameToIdMap.get(row.unit_type_name.toLowerCase());
          if (unitTypeId) {
            transformed.unit_type_id = unitTypeId;
          }
        } else if (row.unit_type_id) {
          // Fallback to direct unit_type_id if provided
          transformed.unit_type_id = row.unit_type_id;
        }



        // Extract custom fields from columns (both 'custom_field_' prefixed and direct field names)
        const customFields: Record<string, any> = {};
        const numberRangeFields: Record<string, { from?: number; to?: number }> = {};

        // Get the category to know which fields are custom fields
        const category = categoryMap.get(transformed.category_id);
        const categoryCustomFields = new Set<string>();
        if (category && (category as any)?.dynamic_field_schema) {
          (category as any).dynamic_field_schema.forEach((field: any) => {
            // Include ALL custom fields from the schema, regardless of used_in_product flag
            // This allows importing data for any field defined in the category schema
            const fieldKey = field.key;
            categoryCustomFields.add(fieldKey);
            // For number-range fields, also add the from/to variants
            if (field.type === 'number-range') {
              categoryCustomFields.add(`${fieldKey}_from`);
              categoryCustomFields.add(`${fieldKey}_to`);
            }
          });
        }

        // Define base fields that should never be treated as custom fields
        const baseFields = new Set([
          'type', 'description', 'status', 'base_cost', 'category_id', 'category_name',
          'unit_type_id', 'unit_type_name', 'name'
        ]);

        Object.keys(row).forEach((key) => {
          let fieldKey: string;
          let value = row[key];

          // Skip base fields - they should never be treated as custom fields
          if (baseFields.has(key)) {
            return;
          }

          // Check if it's a prefixed custom field or a direct custom field
          if (key.startsWith("custom_field_")) {
            fieldKey = key.replace("custom_field_", "");
          } else if (categoryCustomFields.has(key)) {
            // Direct custom field name (from Excel template)
            fieldKey = key;
          } else {
            // Not a custom field, skip
            return;
          }

          // Handle empty values - still process them for validation but mark as empty
          if (value === undefined || value === null || value === "") {
            customFields[fieldKey] = ""; // Include empty fields for validation
            return;
          }

          // Handle number-range fields (ending with _from or _to)
          if (fieldKey.endsWith('_from') || fieldKey.endsWith('_to')) {
            const baseFieldKey = fieldKey.replace(/_from$|_to$/, '');
            const rangeType = fieldKey.endsWith('_from') ? 'from' : 'to';

            if (!numberRangeFields[baseFieldKey]) {
              numberRangeFields[baseFieldKey] = {};
            }

            const numValue = parseFloat(String(value));
            if (!isNaN(numValue) && isFinite(numValue)) {
              numberRangeFields[baseFieldKey][rangeType] = numValue;
            }
            return; // Don't add to customFields yet, will be combined later
          }

          // Convert boolean strings
          if (
            ["true", "false", "1", "0", "yes", "no"].includes(
              String(value).toLowerCase()
            )
          ) {
            value = ["true", "1", "yes"].includes(
              String(value).toLowerCase()
            );
          }
          // Convert numeric strings
          else if (
            !isNaN(parseFloat(String(value))) &&
            isFinite(parseFloat(String(value)))
          ) {
            // Only convert if it's a pure number, not a range like "5-12"
            if (!/[-–]/.test(String(value))) {
              value = parseFloat(String(value));
            }
          }
          // Handle comma-separated values for multi-select fields
          // Note: We'll process these further after we get the category schema
          else if (typeof value === 'string' && value.includes(',')) {
            // Keep as string for now, will be processed based on field type
            value = value.trim();
          }

          customFields[fieldKey] = value;
        });

        // Second pass: combine number-range from/to values
        Object.keys(numberRangeFields).forEach(fieldKey => {
          const range = numberRangeFields[fieldKey];
          if (range.from !== undefined && range.to !== undefined) {
            customFields[fieldKey] = `${range.from}-${range.to}`;
          }
        });

        // Process custom fields based on category schema
        if (transformed.category_id) {
          const category = categoryMap.get(transformed.category_id);
          if (category && (category as any)?.dynamic_field_schema) {
            const processedCustomFields: Record<string, any> = {};

            // Process all custom fields from the schema, not just the ones with values
            (category as any).dynamic_field_schema.forEach((field: any) => {
              const fieldKey = field.key;
              const fieldType = field.type;
              const value = customFields[fieldKey];

              // Include ALL fields from the schema for import processing
              // This allows importing any field defined in the category, regardless of used_in_product flag
              // Handle empty values
              if (value === undefined || value === null || value === '') {
                processedCustomFields[fieldKey] = null; // Use null for empty values
                return;
              }

              switch (fieldType) {
                case 'multi-select':
                  // Handle comma-separated multi-select values
                  if (typeof value === 'string' && value.includes(',')) {
                    processedCustomFields[fieldKey] = value.split(',').map(v => v.trim()).filter(v => v);
                  } else {
                    processedCustomFields[fieldKey] = Array.isArray(value) ? value : [value];
                  }
                  break;

                case 'hotels':
                  // Convert hotel names to IDs
                  if (typeof value === 'string') {
                    const hotelNames = value.includes(',') ?
                      value.split(',').map(v => v.trim()).filter(v => v) : [value.trim()];
                    const hotelIds = hotelNames
                      .map(name => hotelNameToIdMap.get(name.toLowerCase()))
                      .filter(id => id);
                    processedCustomFields[fieldKey] = hotelIds;
                  }
                  break;

                case 'destinations':
                  // Convert destination names to IDs
                  if (typeof value === 'string') {
                    const destNames = value.includes(',') ?
                      value.split(',').map(v => v.trim()).filter(v => v) : [value.trim()];
                    const destIds = destNames
                      .map(name => destinationNameToIdMap.get(name.toLowerCase()))
                      .filter(id => id);
                    processedCustomFields[fieldKey] = destIds;
                  }
                  break;

                default:
                  // Keep other field types as-is
                  processedCustomFields[fieldKey] = value;
                  break;
              }
            });

            // Always assign custom_fields, even if empty (for validation)
            transformed.custom_fields = processedCustomFields;
          } else {
            // Fallback: use original custom fields if no schema found
            transformed.custom_fields = customFields;
          }
        } else {
          // No category found, but still include custom fields for validation
          transformed.custom_fields = customFields;
        }

        // Validate required fields before returning
        const missingFields = [];
        if (!transformed.type) missingFields.push('type');
        if (!transformed.category_id) missingFields.push('category_id');
        if (!transformed.unit_type_id) missingFields.push('unit_type_id');

        return transformed;
      });

      // Validate data
      const errors = await validateImportData(transformedData);

      return { data: transformedData, errors };
    } catch (error) {
      throw new Error(
        `Failed to parse file: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  };

  // Import mutation
  const importMutation = useMutation({
    mutationFn: async (productServices: any[]): Promise<ImportResult> => {
      try {
        setImportProgress({
          processed: 0,
          total: productServices.length,
          percentage: 0,
        });


        const response = await fetch(
          "/admin/supplier-management/products-services/import",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
            body: JSON.stringify({ product_services: productServices }),
          }
        );

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || "Import failed");
        }

        const result = await response.json();
        setImportProgress({
          processed: productServices.length,
          total: productServices.length,
          percentage: 100,
        });

        return result;
      } catch (error) {
        throw new Error(
          `Import failed: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      // Remove success toast - success will be shown in modal UI
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  return {
    parseImportFile,
    importProductServices: importMutation.mutateAsync,
    isImporting: importMutation.isPending,
    importProgress,
    generateTemplate: generateProductServiceTemplate,
    validateData: validateImportData,
    exportToCSV,
    exportToExcel,
  };
};
