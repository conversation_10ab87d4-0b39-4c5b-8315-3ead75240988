import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAdminClient } from "../use-admin-client";
import { sdk } from "../../lib/sdk";
import { toast } from "@camped-ai/ui";

export interface VendorProduct {
  id: string;
  vendor_id: string;
  name: string;
  description?: string;
  sku?: string;
  category: string;
  unit_of_measure: string;
  current_stock: number;
  minimum_stock: number;
  maximum_stock?: number;
  specifications?: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateVendorProductData {
  vendor_id: string;
  name: string;
  description?: string;
  sku?: string;
  category: string;
  unit_of_measure: string;
  current_stock: number;
  minimum_stock: number;
  maximum_stock?: number;
  specifications?: Record<string, any>;
  is_active?: boolean;
}

export interface UpdateVendorProductData extends Partial<CreateVendorProductData> {
  id: string;
}

export interface VendorProductFilters {
  vendor_id?: string;
  category?: string;
  is_active?: boolean;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface VendorProductsResponse {
  products: VendorProduct[];
  count: number;
  offset: number;
  limit: number;
}

export interface VendorProductResponse {
  product: VendorProduct;
}

// Query Keys
export const vendorProductKeys = {
  all: ["vendor-products"] as const,
  lists: () => [...vendorProductKeys.all, "list"] as const,
  list: (filters: VendorProductFilters) => [...vendorProductKeys.lists(), filters] as const,
  details: () => [...vendorProductKeys.all, "detail"] as const,
  detail: (id: string) => [...vendorProductKeys.details(), id] as const,
  byVendor: (vendorId: string) => [...vendorProductKeys.all, "vendor", vendorId] as const,
};

// Hooks
export const useVendorProducts = (filters: VendorProductFilters = {}) => {
  return useQuery({
    queryKey: vendorProductKeys.list(filters),
    queryFn: async (): Promise<VendorProductsResponse> => {
      const params = new URLSearchParams();
      
      if (filters.vendor_id) params.append("vendor_id", filters.vendor_id);
      if (filters.category) params.append("category", filters.category);
      if (filters.is_active !== undefined) params.append("is_active", filters.is_active.toString());
      if (filters.search) params.append("search", filters.search);
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      
      const endpoint = filters.vendor_id
        ? `/admin/vendor_management/vendors/${filters.vendor_id}/products?${params.toString()}`
        : `/admin/vendor_management/products?${params.toString()}`;

      try {
        const result = (await sdk.client.fetch(endpoint)) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
  });
};

export const useVendorProduct = (id: string) => {
  return useQuery({
    queryKey: vendorProductKeys.detail(id),
    queryFn: async (): Promise<VendorProductResponse> => {
      try {
        const result = (await sdk.client.fetch(`/admin/vendor_management/products/${id}`)) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!id,
  });
};

export const useCreateVendorProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateVendorProductData): Promise<VendorProductResponse> => {
      const endpoint = `/admin/vendor_management/vendors/${data.vendor_id}/products`;

      const response = await sdk.client.fetch(endpoint, {
        method: "POST",
        body: data,
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to create product: ${response.statusText}`);
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: vendorProductKeys.lists() });
      queryClient.invalidateQueries({ queryKey: vendorProductKeys.byVendor(data.product.vendor_id) });
      toast.success(`Product "${data.product.name}" created successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to create product: ${error.message}`);
    },
  });
};

export const useUpdateVendorProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateVendorProductData): Promise<VendorProductResponse> => {
      const response = await sdk.client.fetch(`/admin/vendor_management/products/${data.id}`, {
        method: "PUT",
        body: data,
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to update product: ${response.statusText}`);
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: vendorProductKeys.lists() });
      queryClient.invalidateQueries({ queryKey: vendorProductKeys.detail(data.product.id) });
      queryClient.invalidateQueries({ queryKey: vendorProductKeys.byVendor(data.product.vendor_id) });
      toast.success(`Product "${data.product.name}" updated successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update product: ${error.message}`);
    },
  });
};

export const useDeleteVendorProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<{ results: any }> => {
      const response = await sdk.client.fetch(`/admin/vendor_management/products/${id}`, {
        method: "DELETE",
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to delete product: ${response.statusText}`);
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: vendorProductKeys.lists() });
      toast.success("Product deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete product: ${error.message}`);
    },
  });
};

// Stock management
export const useUpdateProductStock = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      current_stock,
      minimum_stock,
      maximum_stock
    }: {
      id: string;
      current_stock: number;
      minimum_stock?: number;
      maximum_stock?: number;
    }): Promise<VendorProductResponse> => {
      const response = await sdk.client.fetch(`/admin/vendor_management/products/${id}`, {
        method: "PUT",
        body: {
          id,
          current_stock,
          minimum_stock,
          maximum_stock,
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to update stock: ${response.statusText}`);
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: vendorProductKeys.lists() });
      queryClient.invalidateQueries({ queryKey: vendorProductKeys.detail(data.product.id) });
      queryClient.invalidateQueries({ queryKey: vendorProductKeys.byVendor(data.product.vendor_id) });
      toast.success("Stock updated successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to update stock: ${error.message}`);
    },
  });
};
