import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";
import { supplierKeys } from "./use-suppliers";
import { useBusinessTypes } from "../supplier-products-services/use-business-types";
import * as XLSX from "xlsx";

export interface ImportValidationError {
  row: number;
  field: string;
  message: string;
  value?: any;
}

export interface ImportProgress {
  processed: number;
  total: number;
  percentage: number;
  currentItem?: string;
}

export interface ImportResult {
  success: boolean;
  imported: number;
  errors: ImportValidationError[];
  message: string;
}

// Template generation utility
export const generateSupplierTemplate = (
  firstBusinessType: string = "housekeeping",
  secondBusinessType: string = "maintenance"
) => {
  const templateData = [
    {
      // Required fields
      name: "Example Supplier Ltd",
      supplier_type: "Company", // Company or Individual
      business_type: firstBusinessType, // Dynamic business type from API
      address: "123 Main Street\nZurich, Zurich 8001\nSwitzerland",

      // Optional fields
      handle: "example-supplier", // Auto-generated if not provided
      description: "Professional cleaning and housekeeping services",
      status: "active", // active, inactive, pending, suspended
      verification_status: "unverified", // verified, unverified, in_review
      business_registration_number: "CHE-123.456.789",
      tax_id: "CHE-123.456.789 MWST",
      website: "https://www.example.com",
      rating: "4.5", // 0-5 rating
      total_orders: "25",
      completed_orders: "23",
    },
    {
      // Sample data row 2 with minimal required fields
      name: "Mountain Maintenance GmbH",
      supplier_type: "company",
      business_type: secondBusinessType,
      address: "Bergstrasse 45\nInterlaken, Bern 3800\nSwitzerland",

      // Optional fields can be empty
      handle: "",
      description: "",
      status: "",
      verification_status: "",
      business_registration_number: "",
      tax_id: "",
      website: "",
      rating: "",
      total_orders: "",
      completed_orders: "",
    },
  ];

  return templateData;
};

// Validation utility
export const validateImportData = (
  data: any[],
  validBusinessTypes: string[] = []
): ImportValidationError[] => {
  const errors: ImportValidationError[] = [];
  const requiredFields = [
    "name",
    "supplier_type"
    // Note: address is optional and should not cause validation errors
    // Note: contacts are managed separately through the Contacts sheet
    // Note: business_type has been removed from the simplified data model
  ];

  const validSupplierTypes = ["company", "individual"];

  data.forEach((row, index) => {
    const rowNumber = index + 2; // +2 because Excel/CSV starts at 1 and we have headers

    // Check required fields
    requiredFields.forEach((field) => {
      if (!row[field] || row[field].toString().trim() === "") {
        errors.push({
          row: rowNumber,
          field,
          message: `${field.replace("_", " ")} is required`,
          value: row[field],
        });
      }
    });

    // Validate supplier type
    if (
      row.supplier_type &&
      !validSupplierTypes.includes(row.supplier_type.toLowerCase())
    ) {
      errors.push({
        row: rowNumber,
        field: "supplier_type",
        message: `Supplier type must be one of: ${validSupplierTypes.join(
          ", "
        )}`,
        value: row.supplier_type,
      });
    }

    // Business type validation removed - field no longer exists in simplified data model

    // Note: Email validation is now handled in the Contacts sheet

    // Validate name uniqueness (basic check - server will do final validation)
    const duplicateIndex = data.findIndex(
      (otherRow, otherIndex) =>
        otherIndex !== index &&
        otherRow.name &&
        otherRow.name.toLowerCase().trim() === row.name?.toLowerCase().trim()
    );

    if (duplicateIndex !== -1) {
      errors.push({
        row: rowNumber,
        field: "name",
        message: `Duplicate supplier name found in row ${duplicateIndex + 2}`,
        value: row.name,
      });
    }
  });

  return errors;
};

// Export utilities
export const exportToCSV = (data: any[], filename: string) => {
  if (data.length === 0) return;

  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(","),
    ...data.map((row) =>
      headers
        .map((header) => {
          const value = row[header]?.toString() || "";
          // Escape quotes and wrap in quotes if contains comma, quote, or newline
          return value.includes(",") ||
            value.includes('"') ||
            value.includes("\n")
            ? `"${value.replace(/"/g, '""')}"`
            : value;
        })
        .join(",")
    ),
  ].join("\n");

  // Add BOM for better Excel compatibility
  const BOM = "\uFEFF";
  const blob = new Blob([BOM + csvContent], {
    type: "text/csv;charset=utf-8;",
  });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${filename}_${new Date().toISOString().split("T")[0]}.csv`
  );
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const exportToExcel = (data: any[], filename: string) => {
  if (data.length === 0) return;

  // Create a new workbook
  const workbook = XLSX.utils.book_new();

  // Convert data to worksheet
  const worksheet = XLSX.utils.json_to_sheet(data);

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Suppliers");

  // Generate Excel file buffer
  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });

  // Create blob and download
  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8;",
  });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${filename}_${new Date().toISOString().split("T")[0]}.xlsx`
  );
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Custom hook for import/export operations
export const useSupplierImportExport = () => {
  const [importProgress, setImportProgress] = useState<ImportProgress>({
    processed: 0,
    total: 0,
    percentage: 0,
  });
  const queryClient = useQueryClient();

  // Fetch business types for template generation and validation
  const { data: businessTypesData } = useBusinessTypes({
    is_active: true,
    limit: 100,
  });

  // File parsing function
  const parseImportFile = async (
    file: File
  ): Promise<{ data: any[]; errors: ImportValidationError[] }> => {
    try {
      let data: any[] = [];

      if (file.type === "text/csv" || file.name.endsWith(".csv")) {
        // Parse CSV
        const text = await file.text();
        const lines = text.split("\n").filter((line) => line.trim());
        if (lines.length < 2) {
          throw new Error(
            "File must contain at least a header row and one data row"
          );
        }

        const headers = lines[0]
          .split(",")
          .map((h) => h.trim().replace(/"/g, ""));
        data = lines.slice(1).map((line) => {
          const values = line.split(",").map((v) => v.trim().replace(/"/g, ""));
          const row: any = {};
          headers.forEach((header, index) => {
            row[header] = values[index] || "";
          });
          return row;
        });
      } else if (
        file.name.endsWith(".xlsx") ||
        file.name.endsWith(".xls") ||
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel"
      ) {
        // Parse Excel files (both .xls and .xlsx)
        const arrayBuffer = await file.arrayBuffer();
        const workbook = XLSX.read(arrayBuffer, { type: "array" });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON with headers
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        if (jsonData.length < 2) {
          throw new Error(
            "File must contain at least a header row and one data row"
          );
        }

        const headers = jsonData[0] as string[];
        data = jsonData.slice(1).map((row: unknown) => {
          const rowArray = row as any[];
          const rowData: any = {};
          headers.forEach((header, index) => {
            rowData[header] = rowArray[index] || "";
          });
          return rowData;
        });
      } else {
        throw new Error(
          "Unsupported file format. Please use CSV or Excel (.xls, .xlsx) files."
        );
      }

      // Fetch business types for validation
      let validBusinessTypes: string[] = [];
      try {
        const response = await fetch(
          "/admin/lookups/entity_name/business_types?limit=100"
        );
        if (response.ok) {
          const businessTypesData = await response.json();
          validBusinessTypes =
            businessTypesData.lookups
              ?.map((lookup: any) => {
                try {
                  const parsed = JSON.parse(lookup.value);
                  return parsed.is_active ? parsed.name : null;
                } catch {
                  return null;
                }
              })
              .filter((name: string | null) => name !== null) || [];
        }
      } catch (error) {
        console.warn("Failed to fetch business types for validation:", error);
      }

      const errors = validateImportData(data, validBusinessTypes);
      return { data, errors };
    } catch (error) {
      throw new Error(
        `Failed to parse file: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  };

  // Import mutation
  const importMutation = useMutation({
    mutationFn: async (suppliers: any[]): Promise<ImportResult> => {
      setImportProgress({
        processed: 0,
        total: suppliers.length,
        percentage: 0,
      });

      try {
        // Transform data before sending to backend
        const transformedSuppliers = suppliers.map((supplier) => ({
          ...supplier,
          // Transform string values to appropriate types
          rating:
            supplier.rating && supplier.rating !== ""
              ? parseFloat(supplier.rating)
              : undefined,
          total_orders:
            supplier.total_orders && supplier.total_orders !== ""
              ? parseInt(supplier.total_orders, 10)
              : 0,
          completed_orders:
            supplier.completed_orders && supplier.completed_orders !== ""
              ? parseInt(supplier.completed_orders, 10)
              : 0,
          // Handle empty status values
          status:
            supplier.status && supplier.status !== ""
              ? supplier.status
              : "pending",
          verification_status:
            supplier.verification_status && supplier.verification_status !== ""
              ? supplier.verification_status
              : "unverified",
        }));

        const response = await fetch(
          "/admin/supplier-management/suppliers/import",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ suppliers: transformedSuppliers }),
            credentials: "include",
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.message ||
              `HTTP ${response.status}: ${response.statusText}`
          );
        }

        const result = await response.json();

        // Update progress to 100%
        setImportProgress({
          processed: suppliers.length,
          total: suppliers.length,
          percentage: 100,
        });

        return {
          success: true,
          imported: result.imported || suppliers.length,
          errors: result.errors || [],
          message:
            result.message ||
            `Successfully imported ${suppliers.length} suppliers`,
        };
      } catch (error) {
        throw new Error(
          `Import failed: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: supplierKeys.lists() });
      toast.success(result.message);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  // Generate template with dynamic business types
  const generateDynamicTemplate = () => {
    const businessTypeOptions =
      businessTypesData?.lookups
        ?.filter((bt) => bt.parsed_value?.is_active)
        ?.map((bt) => bt.parsed_value?.name || bt.value) || [];

    const firstBusinessType = businessTypeOptions[0] || "housekeeping";
    const secondBusinessType = businessTypeOptions[1] || "maintenance";

    return generateSupplierTemplate(firstBusinessType, secondBusinessType);
  };

  return {
    parseImportFile,
    importSuppliers: importMutation.mutateAsync,
    isImporting: importMutation.isPending,
    importProgress,
    generateTemplate: generateDynamicTemplate,
    generateStaticTemplate: generateSupplierTemplate, // Keep static version for backward compatibility
    validateData: validateImportData,
    exportToCSV,
    exportToExcel,
    businessTypes:
      businessTypesData?.lookups?.filter((bt) => bt.parsed_value?.is_active) ||
      [],
  };
};
