import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import React, { useState, useMemo } from "react";
import { Container, Heading, Text, Button, Toaster } from "@camped-ai/ui";
import { ArrowLeft, Tags } from "lucide-react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import ReadOnlyPricingTable from "../../../../../components/hotel/pricing/read-only-pricing-table";
import { useAdminHotelComprehensivePricing } from "../../../../../hooks/hotel/use-admin-hotel-comprehensive-pricing";
import { useAdminCurrencies } from "../../../../../hooks/use-admin-currencies";
import { useRbac } from "../../../../../hooks/use-rbac";
import { useHotels } from "../../../../../hooks/supplier-products-services/use-hotels";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";

const HotelPricingPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [currentCurrency, setCurrentCurrency] = useState<string>("");

  // Fetch currencies to get the default currency
  const { defaultCurrency } = useAdminCurrencies();

  // Set default currency when currencies are loaded
  React.useEffect(() => {
    if (defaultCurrency && !currentCurrency) {
      setCurrentCurrency(defaultCurrency.currency_code);
    }
  }, [defaultCurrency, currentCurrency]);

  // Use the hotels hook to get hotel details
  const { data: hotelsData, isLoading: isLoadingHotels } = useHotels({
    limit: 100,
    is_active: true,
  });

  // Find the hotel by slug
  const hotel = useMemo(() => {
    if (!hotelsData?.hotels || !slug) return null;
    return hotelsData.hotels.find((h: any) => h.id === slug || h.slug === slug);
  }, [hotelsData?.hotels, slug]);

  // Use the comprehensive pricing hook for pricing data with currency
  const {
    data: comprehensiveData,
    isLoading: isLoadingPricing,
    isError,
  } = useAdminHotelComprehensivePricing(hotel?.id || "", currentCurrency);

  // Helper function to get available meal plans for an occupancy type
  const getAvailableMealPlans = (occupancyTypeId: string) => {
    const occupancyConfigs = comprehensiveData?.occupancyConfigs || [];
    const mealPlans = comprehensiveData?.mealPlans || [];

    const occupancy = occupancyConfigs.find(
      (oc: any) => oc.id === occupancyTypeId
    );
    if (!occupancy) return mealPlans;

    // Legacy logic for special accommodations (backward compatibility)
    const isExtraBed =
      (occupancy as any).type === "EXTRA_BED" ||
      occupancy.name?.toLowerCase().includes("extra bed");
    const isCot =
      (occupancy as any).type === "COT" ||
      occupancy.name?.toLowerCase().includes("cot");

    if (isExtraBed || isCot) {
      return [{ id: null, name: "N/A" }];
    }

    // Filter meal plans based on their applicable_occupancy_types metadata
    const availableMealPlans = mealPlans.filter((mealPlan: any) => {
      const applicableTypes = mealPlan.metadata?.applicable_occupancy_types;

      // If no applicable types specified, meal plan is available for all occupancy types
      if (!applicableTypes || applicableTypes.length === 0) {
        return true;
      }

      // Check if this occupancy type is in the applicable types
      return applicableTypes.includes(occupancyTypeId);
    });

    // If no meal plans are available, show N/A
    if (availableMealPlans.length === 0) {
      return [{ id: null, name: "N/A" }];
    }

    return availableMealPlans;
  };

  // Transform comprehensive data into pricing rows for the read-only table
  // This generates ALL possible combinations, not just ones with existing pricing data
  const transformToPricingRows = () => {
    if (!comprehensiveData) return [];

    const pricingRows: any[] = [];
    const roomConfigs = comprehensiveData.roomConfigs || [];
    const occupancyConfigs = comprehensiveData.occupancyConfigs || [];
    const seasonalPeriods = comprehensiveData.seasonalPeriods || [];

    // Helper function to find existing pricing data for a combination
    const findExistingPricing = (
      roomConfigId: string,
      occupancyTypeId: string,
      mealPlanId: string | null,
      seasonalPeriodId?: string
    ) => {
      const roomData = comprehensiveData.room_pricing_data?.find(
        (rd: any) => rd.room_config_id === roomConfigId
      );

      if (!roomData) return null;

      if (seasonalPeriodId) {
        // Look for seasonal pricing
        const seasonalPrice = roomData.seasonal_prices?.find(
          (sp: any) => sp.id === seasonalPeriodId
        );
        if (!seasonalPrice) return null;

        return seasonalPrice.weekday_rules?.find(
          (rule: any) =>
            rule.occupancy_type_id === occupancyTypeId &&
            (mealPlanId === null
              ? !rule.meal_plan_id || rule.meal_plan_id === null
              : rule.meal_plan_id === mealPlanId)
        );
      } else {
        // Look for base pricing
        return roomData.weekday_rules?.find(
          (rule: any) =>
            rule.occupancy_type_id === occupancyTypeId &&
            (mealPlanId === null
              ? !rule.meal_plan_id || rule.meal_plan_id === null
              : rule.meal_plan_id === mealPlanId)
        );
      }
    };

    // Generate base pricing rows (no seasonal period)
    roomConfigs.forEach((roomConfig: any) => {
      occupancyConfigs.forEach((occupancy: any) => {
        // Get available meal plans for this occupancy type
        const mealPlansToProcess = getAvailableMealPlans(occupancy.id);

        mealPlansToProcess.forEach((mealPlan: any) => {
          // Find existing pricing data for this combination
          const existingRule = findExistingPricing(
            roomConfig.id,
            occupancy.id,
            mealPlan.id
          );

          pricingRows.push({
            id: `${roomConfig.id}-${occupancy.id}-${mealPlan.id || "no-meal"}`,
            roomConfigId: roomConfig.id,
            occupancyTypeId: occupancy.id,
            mealPlanId: mealPlan.id,
            prices: {
              mon: existingRule?.weekday_prices?.mon || 0,
              tue: existingRule?.weekday_prices?.tue || 0,
              wed: existingRule?.weekday_prices?.wed || 0,
              thu: existingRule?.weekday_prices?.thu || 0,
              fri: existingRule?.weekday_prices?.fri || 0,
              sat: existingRule?.weekday_prices?.sat || 0,
              sun: existingRule?.weekday_prices?.sun || 0,
            },
          });
        });
      });
    });

    // Generate seasonal pricing rows
    seasonalPeriods.forEach((seasonalPeriod: any) => {
      roomConfigs.forEach((roomConfig: any) => {
        occupancyConfigs.forEach((occupancy: any) => {
          // Get available meal plans for this occupancy type
          const mealPlansToProcess = getAvailableMealPlans(occupancy.id);

          mealPlansToProcess.forEach((mealPlan: any) => {
            // Find existing pricing data for this combination
            const existingRule = findExistingPricing(
              roomConfig.id,
              occupancy.id,
              mealPlan.id,
              seasonalPeriod.id
            );

            pricingRows.push({
              id: `${roomConfig.id}-${occupancy.id}-${
                mealPlan.id || "no-meal"
              }-${seasonalPeriod.id}`,
              roomConfigId: roomConfig.id,
              occupancyTypeId: occupancy.id,
              mealPlanId: mealPlan.id,
              seasonalPeriodId: seasonalPeriod.id,
              prices: {
                mon: existingRule?.weekday_prices?.mon || 0,
                tue: existingRule?.weekday_prices?.tue || 0,
                wed: existingRule?.weekday_prices?.wed || 0,
                thu: existingRule?.weekday_prices?.thu || 0,
                fri: existingRule?.weekday_prices?.fri || 0,
                sat: existingRule?.weekday_prices?.sat || 0,
                sun: existingRule?.weekday_prices?.sun || 0,
              },
            });
          });
        });
      });
    });

    return pricingRows;
  };

  const isLoading = isLoadingHotels || isLoadingPricing;

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Check permissions first
  if (!hasPermission("pricing:view")) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            You don't have permission to view pricing information
          </Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  if (!hotel) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">Hotel not found</Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  // Handle error state
  if (isError || !hotel) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            Failed to load hotel pricing data
          </Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Hotel
          </Button>
        </div>
      </Container>
    );
  }

  // Handle missing pricing data
  if (!comprehensiveData) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            No pricing data available for this hotel
          </Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Hotel
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <Container className="py-6">
        {/* Header with back button */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <Button
              variant="secondary"
              size="small"
              onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Hotel
            </Button>
            <div>
              <Heading level="h1" className="text-2xl font-bold">
                {hotel.name} - Pricing Overview
              </Heading>
              <Text className="text-muted-foreground">
                View current pricing for all room types and seasonal periods
              </Text>
            </div>
          </div>
        </div>

        {/* Read-only pricing table */}
        <ReadOnlyPricingTable
          hotelId={hotel.id}
          roomConfigs={comprehensiveData.roomConfigs || []}
          occupancyConfigs={comprehensiveData.occupancyConfigs || []}
          mealPlans={comprehensiveData.mealPlans || []}
          seasonalPeriods={comprehensiveData.seasonalPeriods || []}
          pricingRows={transformToPricingRows()}
          currencyCode={currentCurrency}
          isLoading={false}
        />
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Hotel Pricing",
  icon: Tags,
});

export default HotelPricingPage;
