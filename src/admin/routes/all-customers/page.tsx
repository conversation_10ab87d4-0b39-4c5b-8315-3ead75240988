import { useState, useEffect, useCallback } from "react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Users } from "@camped-ai/icons";
import { Container, Heading, Text, Button, Input, Select } from "@camped-ai/ui";
import { RefreshCw, ChevronLeft, ChevronRight, UserPlus } from "lucide-react";
import { useSearchParams, useNavigate, useLocation } from "react-router-dom";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";
import AddCustomerDrawer from "../../components/customer/AddCustomerDrawer";
import CustomerListView from "./components/CustomerListView";
import {
  useCustomersList,
  type Customer,
} from "../../hooks/customer-management/use-customers-list";

const AllCustomersPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();

  const [searchInput, setSearchInput] = useState("");
  const [showAddCustomerDrawer, setShowAddCustomerDrawer] = useState(false);

  // Get state from URL params
  const search = searchParams.get("search") || "";
  const currentPage = parseInt(searchParams.get("page") || "1", 10);
  const limit = parseInt(searchParams.get("limit") || "10", 10);
  const sortBy =
    (searchParams.get("sort_by") as
      | "name"
      | "email"
      | "created_at"
      | "updated_at") || "created_at";
  const sortOrder =
    (searchParams.get("sort_order") as "asc" | "desc") || "desc";

  // Use the new hook for data fetching
  const {
    data: customersData,
    isLoading: loading,
    error: queryError,
    refetch: refetchCustomers,
  } = useCustomersList({
    limit,
    offset: (currentPage - 1) * limit,
    search: search.trim() || undefined,
    sort_by: sortBy,
    sort_order: sortOrder,
  });

  const customers = customersData?.customers || [];
  const totalCount = customersData?.count || 0;
  const error = queryError ? (queryError as Error).message : null;

  // Update URL params using router navigate
  const updateUrlParams = useCallback(
    (newParams: {
      page?: number;
      search?: string;
      limit?: number;
      sort_by?: "name" | "email" | "created_at" | "updated_at";
      sort_order?: "asc" | "desc";
    }) => {
      const params = new URLSearchParams(searchParams);

      if (newParams.page !== undefined) {
        if (newParams.page === 1) {
          params.delete("page");
        } else {
          params.set("page", newParams.page.toString());
        }
      }

      if (newParams.search !== undefined) {
        if (newParams.search.trim() === "") {
          params.delete("search");
          // Always reset to page 1 when clearing search
          params.delete("page");
        } else {
          params.set("search", newParams.search.trim());
          // Always reset to page 1 when searching
          params.delete("page");
        }
      }

      if (newParams.limit !== undefined) {
        if (newParams.limit === 10) {
          params.delete("limit");
        } else {
          params.set("limit", newParams.limit.toString());
        }
      }

      if (newParams.sort_by !== undefined) {
        if (newParams.sort_by === "created_at") {
          params.delete("sort_by");
        } else {
          params.set("sort_by", newParams.sort_by);
        }
      }

      if (newParams.sort_order !== undefined) {
        if (newParams.sort_order === "desc") {
          params.delete("sort_order");
        } else {
          params.set("sort_order", newParams.sort_order);
        }
      }

      // Use navigate to update URL with replace to avoid adding to history
      const queryString = params.toString();
      const newUrl = queryString
        ? `${location.pathname}?${queryString}`
        : location.pathname;
      navigate(newUrl, { replace: true });
    },
    [searchParams, navigate, location.pathname]
  );

  // Handle sorting changes
  const handleSortChange = (sorting: {
    sort_by: "name" | "email" | "created_at" | "updated_at";
    sort_order: "asc" | "desc";
  }) => {
    updateUrlParams({
      sort_by: sorting.sort_by,
      sort_order: sorting.sort_order,
      page: 1, // Reset to first page when sorting changes
    });
  };

  // Initialize search input with URL search value
  useEffect(() => {
    setSearchInput(search);
  }, [search]);

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchInput !== search) {
        updateUrlParams({ search: searchInput });
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(timer);
  }, [searchInput, search, updateUrlParams]);

  const handleSearchInputChange = (value: string) => {
    setSearchInput(value);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    updateUrlParams({ page });
  };

  // Handle refresh
  const handleRefresh = () => {
    refetchCustomers();
  };

  // Handle successful customer addition
  const handleCustomerAdded = () => {
    refetchCustomers(); // Refresh the customers list
  };

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / limit);
  const hasNextPage = currentPage < totalPages;
  const hasPrevPage = currentPage > 1;

  return (
    <>
      <PermissionBasedSidebarHider />

      <Container className="p-0 divide-y">
        <div className="flex flex-row items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">All Customers</Heading>
            <Text className="text-ui-fg-subtle">
              Manage and view all customer profiles
            </Text>
          </div>
          <div className="flex items-center gap-3 mt-5">
            <Button
              variant="primary"
              onClick={() => setShowAddCustomerDrawer(true)}
            >
              <UserPlus className="mr-2 h-4 w-4" />
              Add Customer
            </Button>
            <Button
              variant="secondary"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw
                className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="px-6 pt-4">
          <div className="relative">
            <Input
              type="text"
              placeholder="Search by name or email..."
              value={searchInput}
              onChange={(e) => handleSearchInputChange(e.target.value)}
            />
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="px-6 pt-4">
            <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
              <Text className="text-destructive">Error: {error}</Text>
            </div>
          </div>
        )}

        {/* Customers Table */}
        <CustomerListView
          customers={customers}
          isLoading={loading}
          sorting={{ sort_by: sortBy, sort_order: sortOrder }}
          onSortChange={handleSortChange}
          onCreateClick={() => setShowAddCustomerDrawer(true)}
        />

        {/* Pagination controls */}
        {!loading && !error && totalCount > 0 && (
          <div className="flex items-center justify-between p-4 border-t border-ui-border-base bg-ui-bg-base">
            {/* Total count on the left */}
            <div className="flex items-center">
              <Text className="text-sm text-ui-fg-base font-medium">
                Total Customers: {totalCount}
              </Text>
            </div>

            {/* Page numbers in the center */}
            <div className="flex items-center gap-1">
              {/* Previous arrow */}
              <Button
                variant="secondary"
                size="small"
                disabled={!hasPrevPage}
                onClick={() => handlePageChange(currentPage - 1)}
                className={`w-8 h-8 p-0 flex items-center justify-center border border-ui-border-base ${
                  !hasPrevPage
                    ? "bg-ui-bg-disabled text-ui-fg-disabled cursor-not-allowed"
                    : "bg-ui-bg-base text-ui-fg-base hover:bg-ui-bg-subtle"
                }`}
              >
               <ChevronLeft className="w-4 h-4" />
              </Button>

              {/* Page numbers */}
              {(() => {
                const pages = [];
                const maxVisiblePages = 5;
                let startPage = Math.max(
                  1,
                  currentPage - Math.floor(maxVisiblePages / 2)
                );
                let endPage = Math.min(
                  totalPages,
                  startPage + maxVisiblePages - 1
                );

                // Adjust start page if we're near the end
                if (endPage - startPage + 1 < maxVisiblePages) {
                  startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                for (let i = startPage; i <= endPage; i++) {
                  pages.push(
                    <Button
                      key={i}
                      variant={
                        i === currentPage ? "primary" : "secondary"
                      }
                      size="small"
                      onClick={() => handlePageChange(i)}
                      className={`w-8 h-8 p-0 flex items-center justify-center border ${
                        i === currentPage
                          ? "bg-ui-bg-interactive text-ui-fg-on-color border-ui-bg-interactive"
                          : "bg-ui-bg-base text-ui-fg-base border-ui-border-base hover:bg-ui-bg-subtle"
                      }`}
                    >
                      {i}
                    </Button>
                  );
                }
                return pages;
              })()}

              {/* Next arrow */}
              <Button
                variant="secondary"
                size="small"
                disabled={!hasNextPage}
                onClick={() => handlePageChange(currentPage + 1)}
                className={`w-8 h-8 p-0 flex items-center justify-center border border-ui-border-base ${
                  !hasNextPage
                    ? "bg-ui-bg-disabled text-ui-fg-disabled cursor-not-allowed"
                    : "bg-ui-bg-base text-ui-fg-base hover:bg-ui-bg-subtle"
                }`}
              >
                             <ChevronRight className="w-4 h-4" />

              </Button>
            </div>

            {/* Show per page dropdown on the right */}
            <div className="flex items-center gap-2">
              <Text className="text-sm text-ui-fg-base">Show per Page:</Text>
              <Select
                value={limit.toString()}
                onValueChange={(value) => updateUrlParams({ limit: parseInt(value), page: 1 })}
              >
                <Select.Trigger className="w-[60px] h-8 border-ui-border-base bg-ui-bg-base text-ui-fg-base hover:bg-ui-bg-subtle">
                  <Select.Value />
                </Select.Trigger>
                <Select.Content className="bg-ui-bg-base border-ui-border-base">
                  <Select.Item
                    value="5"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    5
                  </Select.Item>
                  <Select.Item
                    value="10"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    10
                  </Select.Item>
                  <Select.Item
                    value="25"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    25
                  </Select.Item>
                  <Select.Item
                    value="50"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    50
                  </Select.Item>
                  <Select.Item
                    value="100"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    100
                  </Select.Item>
                </Select.Content>
              </Select>
            </div>
          </div>
        )}
      </Container>

      {/* Add Customer Drawer */}
      <AddCustomerDrawer
        isOpen={showAddCustomerDrawer}
        onClose={() => setShowAddCustomerDrawer(false)}
        onSuccess={handleCustomerAdded}
      />
    </>
  );
};

export const config = defineRouteConfig({
  label: "All Customers",
  icon: Users,
});

export default AllCustomersPage;
