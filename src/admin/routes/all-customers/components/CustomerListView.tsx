import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Edit, Eye, MoreHorizontal, Trash, UserX, ChevronUp, ChevronDown } from "lucide-react";
import { Users, PlusMini } from "@camped-ai/icons";
import {
  Text,
  Button,
  DropdownMenu,
  IconButton,
  Table,
  Prompt,
} from "@camped-ai/ui";
import { useRbac } from "../../../hooks/use-rbac";
import { type Customer } from "../../../hooks/customer-management/use-customers-list";

// Sorting interface
interface SortingOptions {
  sort_by: "name" | "email" | "created_at" | "updated_at";
  sort_order: "asc" | "desc";
}

interface CustomerListViewProps {
  customers: Customer[];
  isLoading: boolean;
  onCustomerClick?: (id: string) => void;
  onEditClick?: (id: string) => void;
  onCreateClick?: () => void;
  onDeleteClick?: (id: string) => void;
  hasCreatePermission?: boolean;
  hasEditPermission?: boolean;
  hasDeletePermission?: boolean;
  // Sorting props
  sorting?: SortingOptions;
  onSortChange?: (sorting: SortingOptions) => void;
}

const CustomerListView: React.FC<CustomerListViewProps> = ({
  customers,
  isLoading,
  onCustomerClick,
  onEditClick,
  onCreateClick,
  onDeleteClick,
  hasCreatePermission = true,
  hasEditPermission = true,
  hasDeletePermission = true,
  sorting = { sort_by: "created_at", sort_order: "desc" },
  onSortChange,
}) => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);

  // Helper functions
  const getDisplayName = (customer: Customer): string => {
    if (customer.first_name && customer.last_name) {
      return `${customer.first_name} ${customer.last_name}`;
    }
    if (customer.first_name) {
      return customer.first_name;
    }
    if (customer.last_name) {
      return customer.last_name;
    }
    return "No Name";
  };



  // Sorting helper functions
  const handleSort = (column: "name" | "email" | "created_at" | "updated_at") => {
    if (!onSortChange) return;

    let newSortOrder: "asc" | "desc" = "asc";

    // If clicking the same column, toggle the order
    if (sorting.sort_by === column) {
      newSortOrder = sorting.sort_order === "asc" ? "desc" : "asc";
    } else {
      // If clicking a different column, use default order for that column
      if (column === "created_at" || column === "updated_at") {
        newSortOrder = "desc"; // Date columns default to newest first
      } else {
        newSortOrder = "asc"; // Name and email default to alphabetical order (A-Z)
      }
    }

    onSortChange({
      sort_by: column,
      sort_order: newSortOrder,
    });
  };

  const getSortIcon = (column: "name" | "email" | "created_at" | "updated_at") => {
    if (sorting.sort_by !== column) {
      return <ChevronDown className="h-4 w-4 text-ui-fg-muted opacity-50" />;
    }

    return sorting.sort_order === "asc" ? (
      <ChevronUp className="h-4 w-4 text-ui-fg-base" />
    ) : (
      <ChevronDown className="h-4 w-4 text-ui-fg-base" />
    );
  };

  const SortableHeader: React.FC<{
    column: "name" | "email" | "created_at" | "updated_at";
    children: React.ReactNode;
    className?: string;
  }> = ({ column, children, className }) => (
    <Table.HeaderCell
      className={`${className} cursor-pointer hover:bg-ui-bg-subtle transition-colors select-none`}
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-1">
        {children}
        {getSortIcon(column)}
      </div>
    </Table.HeaderCell>
  );

  const handleDeleteClick = (customer: Customer, event: React.MouseEvent) => {
    event.stopPropagation();
    setCustomerToDelete(customer);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!customerToDelete) return;

    try {
      if (onDeleteClick) {
        onDeleteClick(customerToDelete.id);
      }
    } catch (error) {
      console.error("Error deleting customer:", error);
    } finally {
      setDeleteConfirmOpen(false);
      setCustomerToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setCustomerToDelete(null);
  };

  const renderLoadingSkeleton = () =>
    Array.from({ length: 5 }).map((_, index) => (
      <Table.Row key={`skeleton-${index}`}>
        {/* Customer Name skeleton */}
        <Table.Cell className="w-64">
          <div className="space-y-1">
            <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-32" />
            <div className="h-3 bg-ui-bg-subtle rounded animate-pulse w-24" />
          </div>
        </Table.Cell>
        {/* Phone skeleton */}
        <Table.Cell className="w-32">
          <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-20" />
        </Table.Cell>
        {/* Actions skeleton */}
        <Table.Cell className="w-24">
          <div className="h-8 w-8 bg-ui-bg-subtle rounded animate-pulse" />
        </Table.Cell>
      </Table.Row>
    ));

  const renderEmptyState = () => (
    <div className="py-16 text-center">
      <div className="max-w-sm mx-auto">
        <Users className="h-16 w-16 text-ui-fg-muted mx-auto mb-4" />
        <Text size="large" weight="plus" className="text-ui-fg-base">
          No customers found
        </Text>
        <Text className="text-ui-fg-subtle mt-2">
          Try adjusting your filters or get started by adding your first
          customer
        </Text>
        {(hasCreatePermission ||
          hasPermission("customer_management:create")) && (
            <Button
              size="small"
              className="mt-6"
              onClick={() => {
                if (onCreateClick) {
                  onCreateClick();
                }
              }}
            >
              <PlusMini />
              Add your first customer
            </Button>
          )}
      </div>
    </div>
  );

  const renderActionDropdown = (customer: Customer) => (
    <div onClick={(e) => e.stopPropagation()}>
      <DropdownMenu>
        <DropdownMenu.Trigger asChild>
          <IconButton size="small">
            <MoreHorizontal className="h-4 w-4" />
          </IconButton>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content align="end">
          <DropdownMenu.Item
            onClick={(e) => {
              e.stopPropagation();
              if (onCustomerClick) {
                onCustomerClick(customer.id);
              } else {
                navigate(`/all-customers/${customer.id}`);
              }
            }}
          >
            <Eye className="h-4 w-4 mr-2" />
            View
          </DropdownMenu.Item>
          {(hasEditPermission || hasPermission("customer_management:edit")) && (
            <DropdownMenu.Item
              onClick={(e) => {
                e.stopPropagation();
                if (onEditClick) {
                  onEditClick(customer.id);
                } else {
                  navigate(`/all-customers/${customer.id}?edit=true`);
                }
              }}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </DropdownMenu.Item>
          )}
          {(hasDeletePermission ||
            hasPermission("customer_management:delete")) && (
              <DropdownMenu.Item
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteClick(customer, e);
                }}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenu.Item>
            )}
        </DropdownMenu.Content>
      </DropdownMenu>
    </div>
  );

  return (
    <>
      <div className="mx-0 mt-4">
        {customers.length === 0 && !isLoading ? (
          renderEmptyState()
        ) : (
          <Table>
            <Table.Header>
              <Table.Row>
                <SortableHeader column="name" className="w-64">
                  Customer Name
                </SortableHeader>
                <Table.HeaderCell className="w-32">Phone</Table.HeaderCell>
                <Table.HeaderCell className="text-right w-24">
                  Actions
                </Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {isLoading
                ? renderLoadingSkeleton()
                : customers.map((customer) => {
                  return (
                    <Table.Row
                      key={customer.id}
                      className="cursor-pointer hover:bg-ui-bg-subtle transition-colors"
                      onClick={() => {
                        if (onCustomerClick) {
                          onCustomerClick(customer.id);
                        } else {
                          navigate(`/all-customers/${customer.id}`);
                        }
                      }}
                    >
                      {/* Customer Name */}
                      <Table.Cell className="w-64">
                        <div className="flex items-center space-x-3">
                         
                          {/* Customer Info */}
                          <div className="min-w-0 max-w-64">
                            <div
                              className="font-medium text-ui-fg-base truncate text-sm"
                              title={getDisplayName(customer)}
                            >
                              {getDisplayName(customer)}
                            </div>
                            <div className="flex items-center text-sm text-ui-fg-subtle mt-0.5">
                              <span className="truncate">
                                {customer.email}
                              </span>
                            </div>
                            {customer.company_name && (
                              <div className="text-xs text-ui-fg-subtle/70 truncate">
                                {customer.company_name}
                              </div>
                            )}
                          </div>
                        </div>
                      </Table.Cell>

                      {/* Phone */}
                      <Table.Cell>
                        <Text className="text-sm text-ui-fg-subtle">
                          {customer.phone || "—"}
                        </Text>
                      </Table.Cell>

                      {/* Actions */}
                      <Table.Cell className="text-right">
                        <div onClick={(e) => e.stopPropagation()}>
                          {renderActionDropdown(customer)}
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  );
                })}
            </Table.Body>
          </Table>
        )}
      </div>

      {/* Delete Confirmation Prompt */}
      <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Customer</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{getDisplayName(customerToDelete || {} as Customer)}"? This action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelDelete}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={handleConfirmDelete}
            >
              Delete
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export default CustomerListView;
