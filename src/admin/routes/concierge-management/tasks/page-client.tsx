import React, { useState, useMemo } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";
import {
  Badge,
  Button,
  Container,
  Heading,
  Text,
  toast,
  DropdownMenu,
} from "@camped-ai/ui";
import { Edit, Eye, MoreHorizontal, Trash, Plus, Calendar } from "lucide-react";
import { useReactTable, getCoreRowModel, createColumnHelper, ColumnDef } from "@tanstack/react-table";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";
import { TaskFormModal } from "../../../components/concierge/task-form-modal";
import type { TaskScreenData } from "./loader";
import "../../../styles/task-modal.css";

// Status badge colors
const statusColors = {
  pending: "orange",
  in_progress: "blue",
  review: "orange",
  completed: "green",
  cancelled: "grey",
} as const;

const priorityColors = {
  low: "grey",
  medium: "blue",
  high: "orange",
  urgent: "red",
} as const;

interface TasksPageClientProps {
  tasks: TaskScreenData[];
  isLoading: boolean;
  totalCount: number;
  pageSize: number;
}

const TasksPageClient: React.FC<TasksPageClientProps> = ({
  tasks,
  isLoading,
  totalCount,
  pageSize,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { hasPermission } = useRbac();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<TaskScreenData | null>(null);

  // Column helper for type safety
  const columnHelper = createColumnHelper<TaskScreenData>();

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "grey";
  };

  const getPriorityBadgeVariant = (priority: string) => {
    return priorityColors[priority as keyof typeof priorityColors] || "grey";
  };

  const handleCopyTaskId = (taskId: string) => {
    navigator.clipboard.writeText(taskId);
    toast.success("Task ID copied to clipboard");
  };

  // Define columns
  const columns = useMemo<ColumnDef<TaskScreenData, any>[]>(() => [
    columnHelper.display({
      id: "task",
      header: "Task",
      cell: ({ row }) => {
        const task = row.original;
        return (
          <div className="flex items-center gap-x-3 w-[200px] truncate">
            <div className="flex h-8 w-8 items-center justify-center rounded bg-ui-bg-subtle">
              <Calendar className="h-4 w-4 text-ui-fg-subtle" />
            </div>
            <div>
              <Text className="txt-compact-medium-plus" weight="plus">
                {task.title}
              </Text>
              <div className="flex items-center gap-x-1">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopyTaskId(task.id);
                  }}
                  className="text-xs text-ui-fg-muted hover:text-ui-fg-subtle"
                >
                  {task.id.slice(0, 8)}...
                </button>
              </div>
            </div>
          </div>
        );
      },
    }),
    columnHelper.accessor("description", {
      header: "Description",
      cell: ({ row }) => (
        <div className="w-[200px] truncate">
          <Text className="text-ui-fg-subtle">
            {row.original.description || "No description"}
          </Text>
        </div>
      ),
    }),
    columnHelper.accessor("status", {
      header: "Status",
      cell: ({ row }) => (
        <Badge color={getStatusBadgeVariant(row.original.status)}  size="xsmall">
          {row.original.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </Badge>
      ),
    }),
    columnHelper.accessor("priority", {
      header: "Priority",
      cell: ({ row }) => (
        <Badge color={getPriorityBadgeVariant(row.original.priority)} size="xsmall">
          {row.original.priority.charAt(0).toUpperCase() + row.original.priority.slice(1)}
        </Badge>
      ),
    }),
    columnHelper.accessor("entity_type", {
      header: "Entity Type",
      cell: ({ row }) => (
        <Text>
          {row.original.entity_type
            ? row.original.entity_type.charAt(0).toUpperCase() + row.original.entity_type.slice(1)
            : "General"}
        </Text>
      ),
    }),
    columnHelper.accessor("assigned_to", {
      header: "Assigned To",
      cell: ({ row }) => (
        <Text className={row.original.assigned_to ? "" : "text-ui-fg-subtle"}>
          {row.original.assigned_to || "Unassigned"}
        </Text>
      ),
    }),
    columnHelper.accessor("due_date", {
      header: "Due Date",
      cell: ({ row }) => (
        <Text className={row.original.due_date ? "" : "text-ui-fg-subtle"}>
          {row.original.due_date
            ? format(new Date(row.original.due_date), "MMM dd, yyyy")
            : "No due date"}
        </Text>
      ),
    }),
    columnHelper.accessor("created_at", {
      header: "Created",
      cell: ({ row }) => (
        <Text>
          {format(new Date(row.original.created_at), "MMM dd, yyyy")}
        </Text>
      ),
    }),
    columnHelper.display({
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const task = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="secondary" size="small">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item
                onClick={() => navigate(`/concierge-management/tasks/${task.id}`)}
              >
                <Eye className="h-4 w-4 mr-2" />
                View
              </DropdownMenu.Item>
              {hasPermission("tasks:edit") && (
                <DropdownMenu.Item
                  onClick={() => setEditingTask(task)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenu.Item>
              )}
              {hasPermission("tasks:delete") && (
                <DropdownMenu.Item
                  onClick={() => {
                    console.log("Delete task:", task.id);
                  }}
                  className="text-red-600"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenu.Item>
              )}
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    }),
  ], [hasPermission, navigate]);

  // Define filters
  const filters: Filter[] = useMemo(() => [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { label: "Pending", value: "pending" },
        { label: "In Progress", value: "in_progress" },
        { label: "Review", value: "review" },
        { label: "Completed", value: "completed" },
        { label: "Cancelled", value: "cancelled" },
      ],
    },
    {
      key: "priority",
      label: "Priority",
      type: "select",
      options: [
        { label: "Low", value: "low" },
        { label: "Medium", value: "medium" },
        { label: "High", value: "high" },
        { label: "Urgent", value: "urgent" },
      ],
    },
    {
      key: "entity_type",
      label: "Entity Type",
      type: "select",
      options: [
        { label: "Booking", value: "booking" },
        { label: "Deal", value: "deal" },
        { label: "Guest", value: "guest" },
        { label: "Itinerary", value: "itinerary" },
      ],
    },
  ], []);

  // Define sortable columns
  const orderBy = useMemo(() => [
    { key: "title" as keyof TaskScreenData, label: "Title" },
    { key: "status" as keyof TaskScreenData, label: "Status" },
    { key: "priority" as keyof TaskScreenData, label: "Priority" },
    { key: "due_date" as keyof TaskScreenData, label: "Due Date" },
    { key: "created_at" as keyof TaskScreenData, label: "Created At" },
  ], []);

  // Get current page from URL
  const searchParams = new URLSearchParams(location.search);
  const currentPage = parseInt(searchParams.get("page") || "1");

  // Create table instance
  const table = useReactTable({
    data: tasks,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil((totalCount || tasks.length) / pageSize),
    state: {
      pagination: {
        pageIndex: currentPage - 1,
        pageSize,
      },
    },
  });

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">Tasks</Heading>
        </div>
        <div className="flex items-center gap-x-2">
          <Button size="small" variant="secondary" onClick={() => setIsCreateModalOpen(true)}>
            {t("actions.create")}
          </Button>
        </div>
      </div>

      {/* DataTable */}
      <DataTable
        table={table}
        columns={columns}
        pageSize={pageSize}
        count={totalCount || tasks.length}
        isLoading={isLoading}
        filters={filters}
        orderBy={orderBy}
        search="autofocus"
        pagination
        navigateTo={(row) => `/concierge-management/tasks/${row.original.id}`}
        queryObject={Object.fromEntries(searchParams)}
        noRecords={{
          title: "No tasks found",
          message: "Get started by creating your first task",
        }}
      />

      {/* Create Task Modal */}
      <TaskFormModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      />

      {/* Edit Task Modal */}
      <TaskFormModal
        isOpen={!!editingTask}
        onClose={() => setEditingTask(null)}
        task={editingTask || undefined}
      />
    </Container>
  );
};

export default TasksPageClient;
