import React from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Label,
  toast,
} from "@camped-ai/ui";
import { ArrowLeft } from "@camped-ai/icons";
import { useTranslation } from "react-i18next";
import { useCreateConciergeTask } from "../../../../hooks/api/concierge-tasks";

// Form validation schema
const taskFormSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title is too long"),
  description: z.string().optional(),
  status: z.enum(["pending", "in_progress", "review", "completed", "cancelled"]).default("pending"),
  priority: z.enum(["low", "medium", "high", "urgent"]).default("medium"),
  entity_type: z.string().optional(),
  entity_id: z.string().optional(),
  assigned_to: z.string().optional(),
  due_date: z.string().optional(),
});

type TaskFormData = z.infer<typeof taskFormSchema>;

const CreateTaskPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const createTaskMutation = useCreateConciergeTask();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<TaskFormData>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      status: "pending",
      priority: "medium",
    },
  });

  const onSubmit = async (data: TaskFormData) => {
    try {
      const taskData = {
        ...data,
        due_date: data.due_date ? new Date(data.due_date).toISOString() : undefined,
      };

      await createTaskMutation.mutateAsync(taskData);
      toast.success("Task created successfully");
      navigate("/concierge-management/tasks");
    } catch (error) {
      console.error("Error creating task:", error);
      toast.error("Failed to create task");
    }
  };

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center gap-x-4">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate("/concierge-management/tasks")}
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <Heading level="h1">Create New Task</Heading>
            <Text className="text-ui-fg-subtle">
              Create a new task for the concierge team
            </Text>
          </div>
        </div>
      </div>

      {/* Form */}
      <div className="px-6 py-6">
        <form onSubmit={handleSubmit(onSubmit)} className="max-w-2xl space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">
              Title <span className="text-red-500">*</span>
            </Label>
            <Input
              id="title"
              {...register("title")}
              placeholder="Enter task title"
              error={errors.title?.message}
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Enter task description (optional)"
              rows={4}
            />
          </div>

          {/* Status and Priority Row */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select {...register("status")}>
                <option value="pending">Pending</option>
                <option value="in_progress">In Progress</option>
                <option value="review">Review</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select {...register("priority")}>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </Select>
            </div>
          </div>

          {/* Entity Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="entity_type">Entity Type</Label>
              <Select {...register("entity_type")}>
                <option value="">General Task</option>
                <option value="booking">Booking</option>
                <option value="deal">Deal</option>
                <option value="guest">Guest</option>
                <option value="itinerary">Itinerary</option>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="entity_id">Entity ID</Label>
              <Input
                id="entity_id"
                {...register("entity_id")}
                placeholder="Enter entity ID (optional)"
              />
            </div>
          </div>

          {/* Assignment and Due Date Row */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="assigned_to">Assigned To</Label>
              <Input
                id="assigned_to"
                {...register("assigned_to")}
                placeholder="Enter user ID (optional)"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="due_date">Due Date</Label>
              <Input
                id="due_date"
                type="date"
                {...register("due_date")}
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-x-2 pt-6 border-t">
            <Button
              type="button"
              variant="secondary"
              onClick={() => navigate("/concierge-management/tasks")}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              loading={isSubmitting}
            >
              Create Task
            </Button>
          </div>
        </form>
      </div>
    </Container>
  );
};

export default CreateTaskPage;
