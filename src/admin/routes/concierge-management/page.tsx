import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import { Calendar, CheckSquare, MapPin, Settings, Mail } from "lucide-react";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../components/rbac/RoleGuard";

const ConciergeManagementPage = () => {
  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="p-6">
        <RoleGuard
          requirePermission="concierge_management:view"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to access Concierge Management.
              </Text>
            </div>
          }
        >
          <div className="space-y-6">
            {/* Header */}
            <div>
              <Heading level="h1" className="text-3xl font-bold">
                Concierge Management
              </Heading>
              <Text className="text-muted-foreground mt-2">
                Manage bookings, tasks, itineraries, and system configuration
              </Text>
            </div>

            {/* Main Navigation Cards */}
            <div className="space-y-6">
              <div>
                <Heading level="h2" className="text-xl font-semibold mb-4">
                  Main Functions
                </Heading>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <NavigationCard
                    title="Bookings"
                    description="View and manage all hotel bookings"
                    href="/concierge-management/bookings"
                    icon={<Calendar className="h-8 w-8" />}
                    color="blue"
                  />
                  <NavigationCard
                    title="Tasks"
                    description="Comprehensive task management system"
                    href="/concierge-management/tasks"
                    icon={<CheckSquare className="h-8 w-8" />}
                    color="green"
                  />
                  <NavigationCard
                    title="Itineraries"
                    description="Manage guest itineraries and schedules"
                    href="/concierge-management/itineraries"
                    icon={<MapPin className="h-8 w-8" />}
                    color="purple"
                  />
                </div>
              </div>

              {/* Configuration Section */}
              <div>
                <Heading level="h2" className="text-xl font-semibold mb-4">
                  Configuration
                </Heading>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <NavigationCard
                    title="Status Configuration"
                    description="Configure statuses for boards and templates"
                    href="/concierge-management/config/statuses"
                    icon={<Settings className="h-8 w-8" />}
                    color="gray"
                  />
                  <NavigationCard
                    title="Email Templates"
                    description="Manage email templates and notifications"
                    href="/concierge-management/config/email-templates"
                    icon={<Mail className="h-8 w-8" />}
                    color="orange"
                  />
                </div>
              </div>
            </div>
          </div>
        </RoleGuard>
      </Container>
    </>
  );
};

interface NavigationCardProps {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
  color: "blue" | "green" | "purple" | "gray" | "orange";
}

const NavigationCard: React.FC<NavigationCardProps> = ({
  title,
  description,
  href,
  icon,
  color,
}) => {
  const colorClasses = {
    blue: "border-blue-200 hover:border-blue-300 hover:bg-blue-50",
    green: "border-green-200 hover:border-green-300 hover:bg-green-50",
    purple: "border-purple-200 hover:border-purple-300 hover:bg-purple-50",
    gray: "border-gray-200 hover:border-gray-300 hover:bg-gray-50",
    orange: "border-orange-200 hover:border-orange-300 hover:bg-orange-50",
  };

  const iconColorClasses = {
    blue: "text-blue-600",
    green: "text-green-600",
    purple: "text-purple-600",
    gray: "text-gray-600",
    orange: "text-orange-600",
  };

  return (
    <a
      href={href}
      className={`block p-6 border-2 rounded-lg transition-all duration-200 hover:shadow-md ${colorClasses[color]}`}
    >
      <div className="flex flex-col items-center text-center space-y-4">
        <div className={`${iconColorClasses[color]}`}>{icon}</div>
        <div>
          <Heading level="h3" className="text-lg font-semibold">
            {title}
          </Heading>
          <Text className="text-sm text-muted-foreground mt-1">
            {description}
          </Text>
        </div>
      </div>
    </a>
  );
};

export const config = defineRouteConfig({
  label: "Concierge Management",
});

export default ConciergeManagementPage;
