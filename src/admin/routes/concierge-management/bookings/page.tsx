import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useLocation, useSearchParams } from "react-router-dom";
import { useMemo } from "react";
import { Container, Heading, Text } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { bookingScreenLoader, type BookingScreenFilters } from "./loader";
import { fetchHotels } from "./api";
import BookingsPageClient from "./page-client";

const ConciergeBookingsPage = () => {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();

  // Get hotel_id from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const hotelId = queryParams.get("hotel_id");

  // Get current page and page size from URL params
  const currentPage = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("limit") || "20");
  const searchTerm = searchParams.get("q") || "";

  // Build filters for the loader
  const filters: BookingScreenFilters = useMemo(() => {
    const baseFilters: BookingScreenFilters = {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      sort_by: searchParams.get("order")?.replace("-", "") || "created_at",
      sort_order: searchParams.get("order")?.startsWith("-") ? "desc" : "asc",
    };

    // Add filters from URL params
    const statusFilter = searchParams.get("status");
    if (statusFilter) {
      baseFilters.status = statusFilter;
    }

    const paymentStatusFilter = searchParams.get("payment_status");
    if (paymentStatusFilter) {
      baseFilters.payment_status = paymentStatusFilter;
    }

    const hotelFilter = searchParams.get("hotel_id") || hotelId;
    if (hotelFilter) {
      baseFilters.hotel_id = hotelFilter;
    }

    return baseFilters;
  }, [searchParams, currentPage, pageSize, searchTerm, hotelId]);

  // Use the booking screen loader to fetch bookings
  const {
    data: bookingData,
    isLoading: bookingsLoading,
    error: bookingsError,
  } = useQuery({
    queryKey: ["booking-screen", filters],
    queryFn: () => bookingScreenLoader(queryClient)(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });

  // Fetch hotels data
  const {
    data: hotelsData,
    isLoading: hotelsLoading,
    error: hotelsError,
  } = useQuery({
    queryKey: ["hotels"],
    queryFn: fetchHotels,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
  });

  // Extract data from queries
  const bookings = bookingData?.bookings || [];
  const hotels = hotelsData?.hotels || [];
  const totalCount = bookingData?.count || 0;
  const isLoading = bookingsLoading || hotelsLoading;

  // Handle errors
  if (bookingsError) {
    console.error("Error fetching bookings:", bookingsError);
  }
  if (hotelsError) {
    console.error("Error fetching hotels:", hotelsError);
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="bookings:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view bookings.
              </Text>
            </div>
          </Container>
        }
      >
        <BookingsPageClient
          bookings={bookings}
          hotels={hotels}
          isLoading={isLoading}
          totalCount={totalCount}
          pageSize={pageSize}
          hotelId={hotelId}
        />
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Concierge Bookings",
});

export default ConciergeBookingsPage;
