import { Pencil<PERSON>quare, <PERSON><PERSON><PERSON> } from "@camped-ai/icons";
import { Container, Heading, Badge, Button } from "@camped-ai/ui";

import { ActionMenu } from "../../../../../components/ActionMenu";
import { SectionRow } from "../../../../../../components/common/section/section-row";
import { useRbac } from "../../../../../hooks/use-rbac";

interface ConciergeBookingConciergeSection {
  booking: any;
}

const ConciergeBookingConciergeSection = ({ booking }: ConciergeBookingConciergeSection) => {
  const { hasPermission } = useRbac();

  const handleAssignConcierge = () => {
    // TODO: Implement concierge assignment
    console.log("Assign concierge:", booking.id);
  };

  const handleSetPriority = () => {
    // TODO: Implement priority setting
    console.log("Set priority:", booking.id);
  };

  // Format assigned concierge with inline action
  const formatAssignedConcierge = () => {
    if (!booking.assigned_concierge) {
      return hasPermission("bookings:update") ? (
        <Button variant="secondary" size="small" onClick={handleAssignConcierge}>
          <PlusMini className="h-3 w-3 mr-1" />
          Assign Concierge
        </Button>
      ) : "Not assigned";
    }
    return booking.assigned_concierge;
  };

  // Format priority level with tags
  const formatPriorityLevel = () => {
    if (!booking.priority_level) {
      return hasPermission("bookings:update") ? (
        <div className="flex gap-1">
          <Button variant="secondary" size="small" onClick={handleSetPriority}>
            Set Priority
          </Button>
        </div>
      ) : "Not set";
    }

    const priorityColors = {
      low: "grey" as const,
      medium: "blue" as const,
      high: "orange" as const,
      urgent: "red" as const
    };

    const level = booking.priority_level.toLowerCase() as keyof typeof priorityColors;
    return (
      <Badge color={priorityColors[level] || "grey"}>
        {booking.priority_level.toUpperCase()}
      </Badge>
    );
  };

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>Concierge Management</Heading>
        {hasPermission("bookings:update") && (
          <ActionMenu
            groups={[
              {
                actions: [
                  {
                    label: "Update Assignment",
                    onClick: handleAssignConcierge,
                    icon: <PencilSquare />,
                  },
                ],
              },
            ]}
          />
        )}
      </div>

      <SectionRow
        title="Assigned Concierge"
        value={formatAssignedConcierge()}
      />
      <SectionRow
        title="Priority Level"
        value={formatPriorityLevel()}
      />
    </Container>
  );
};

export default ConciergeBookingConciergeSection;
