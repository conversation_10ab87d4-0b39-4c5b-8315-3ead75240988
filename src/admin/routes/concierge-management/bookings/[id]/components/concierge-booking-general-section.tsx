import { PencilSquare } from "@camped-ai/icons";
import { Trash } from "lucide-react";
import { Container, Heading, StatusBadge, usePrompt } from "@camped-ai/ui";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";

import { ActionMenu } from "../../../../../components/ActionMenu";
import { SectionRow } from "../../../../../../components/common/section/section-row";
import { useRbac } from "../../../../../hooks/use-rbac";

const bookingStatusColor = (status: string) => {
  switch (status) {
    case "pending":
      return "grey";
    case "confirmed":
      return "green";
    case "checked_in":
      return "blue";
    case "checked_out":
      return "purple";
    case "canceled":
      return "red";
    case "no_show":
      return "red";
    default:
      return "grey";
  }
};

interface ConciergeBookingGeneralSectionProps {
  booking: any;
  hotelDetails: any;
}

const ConciergeBookingGeneralSection = ({
  booking,
  hotelDetails
}: ConciergeBookingGeneralSectionProps) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd MMM yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Format currency
  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount || 0);
  };

  const handleEdit = () => {
    navigate("edit");
  };

  const handleCancel = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: `Are you sure you want to cancel this booking for ${booking.guest_name}?`,
      confirmText: t("actions.cancel"),
      cancelText: t("actions.back"),
    });

    if (!res) {
      return;
    }

    // TODO: Implement booking cancellation
    console.log("Cancel booking:", booking.id);
  };

  return (
    <Container className="divide-y p-0">
      <SectionRow
        title="Booking ID"
        value={booking.id || "-"}
      />
      <SectionRow
        title="Email Address"
        value={booking.guest_email || "-"}
      />
      <SectionRow
        title="Phone Number"
        value={booking.guest_phone || "-"}
      />
      <SectionRow
        title="Total Amount"
        value={formatCurrency(booking.total_amount, booking.currency_code)}
      />
    </Container>
  );
};

export default ConciergeBookingGeneralSection;
