import { Container, <PERSON><PERSON>, <PERSON>, Badge, Button, toast, Tooltip } from "@camped-ai/ui";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { useState } from "react";
import { PlusMini } from "@camped-ai/icons";

import { SectionRow } from "../../../../../../components/common/section/section-row";
import { useRbac } from "../../../../../hooks/use-rbac";
import { ActionMenu } from "../../../../../components/ActionMenu";
import { AddOnManagementPanel } from "../../../../../components/booking/add-on-management";
import { useAddOnManagement } from "../../../../../hooks/concierge-management/use-booking-add-ons";
import { SelectedAddOn } from "../../../../../components/booking/add-on-selection/AddOnSelectionStep";

interface ConciergeBookingItinerarySectionProps {
  booking: any;
  addOns: any[];
  hotelDetails: any;
  onItineraryCreated?: () => void; // Callback to refresh booking data
  onAddOnAdded?: () => void; // Callback to refresh add-ons data
}

const ConciergeBookingItinerarySection = ({
  booking,
  addOns,
  hotelDetails,
  onItineraryCreated,
  onAddOnAdded
}: ConciergeBookingItinerarySectionProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [isCreatingItinerary, setIsCreatingItinerary] = useState(false);
  const [showAddOnPanel, setShowAddOnPanel] = useState(false);

  // Add-on management hook
  const { addSelectedAddOnToBooking, isAdding } = useAddOnManagement(booking?.id);

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM dd");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Format date range
  const formatDateRange = () => {
    if (!booking.check_in_date || !booking.check_out_date) return "Dates not specified";

    const checkIn = formatDate(booking.check_in_date);
    const checkOut = formatDate(booking.check_out_date);
    const year = format(new Date(booking.check_in_date), "yyyy");

    return `${checkIn} – ${checkOut}, ${year}`;
  };

  const truncate = (str: string, start = 12, end = 12) => {
    if (!str || str.length <= start + end) {
      return str;
    }
    return `${str.substring(0, start)}...${str.substring(str.length - end)}`;
  };

  const handleCreateItinerary = async () => {
    if (!booking?.id) {
      toast.error("Error", {
        description: "Booking ID is required to create an itinerary",
      });
      return;
    }

    setIsCreatingItinerary(true);

    try {
      const response = await fetch(
        `/admin/concierge-management/bookings/${booking.id}/itinerary/create`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            created_by: "current_user", // TODO: Get actual user ID from auth context
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create itinerary");
      }

      const data = await response.json();

      toast.success("Success", {
        description: data.message || "Itinerary created successfully",
      });

      // Call the callback to refresh booking data
      if (onItineraryCreated) {
        onItineraryCreated();
      }

      // Navigate to the itinerary builder
      if (data.redirect_url) {
        navigate(data.redirect_url);
      } else {
        navigate(`/concierge-management/itineraries/${data.itinerary.id}/builder`);
      }
    } catch (error: any) {
      console.error("Error creating itinerary:", error);
      toast.error("Error", {
        description: error.message || "Failed to create itinerary",
      });
    } finally {
      setIsCreatingItinerary(false);
    }
  };

  const handleViewItinerary = () => {
    if (booking.itinerary_id) {
      navigate(`/concierge-management/itineraries/${booking.itinerary_id}/builder`);
    }
  };

  const handleAddAddOn = () => {
    setShowAddOnPanel(true);
  };

  const handleAddOnPanelClose = () => {
    setShowAddOnPanel(false);
  };

  const handleAddOnSuccess = async (selectedAddOn: SelectedAddOn) => {
    try {
      await addSelectedAddOnToBooking(selectedAddOn);
      setShowAddOnPanel(false);
      if (onAddOnAdded) {
        onAddOnAdded();
      }
    } catch (error) {
      console.error("Error adding add-on to booking:", error);
      // Error handling is done in the hook
    }
  };

  // Format itinerary status with tooltip
  const formatItineraryStatus = () => {
    if (!booking.itinerary_id) {
      return (
        <div className="flex items-center gap-2">
          <Text className="text-muted-foreground">Not created</Text>
        </div>
      );
    }
    return (
      <div className="flex items-center gap-2">
        <Badge color="green">Active</Badge>
      </div>
    );
  };

  // Format add-ons for display with bullet point style
  const formatAddOns = () => {
    if (!addOns || addOns.length === 0) {
      return (
        <Text className="text-muted-foreground italic">
          No add-ons booked
        </Text>
      );
    }

    return (
      <div className="space-y-3">
        {addOns.map((addon: any, index: number) => (
          <div key={index} className="space-y-1">
            {/* Main service name with bullet */}
            <div className="flex items-start gap-2">
              <Text className="text-muted-foreground mt-1">•</Text>
              <div className="flex-1">
                <Text className="font-medium">{addon.add_on_name}</Text>
                {addon.customer_field_responses &&
                  Object.keys(addon.customer_field_responses).length > 0 && (
                    <div className="mt-1">
                      <Text className="text-sm text-muted-foreground">
                        {Object.entries(addon.customer_field_responses)
                          .map(([, value]) => String(value))
                          .join(', ')}
                      </Text>
                    </div>
                  )}
                <div className="flex items-center gap-2 mt-1">
                  <Text className="text-sm text-muted-foreground">
                    Quantity: {addon.quantity || 1}
                  </Text>
                  <Text className="text-sm font-medium">
                    {new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: addon.currency_code || "USD",
                    }).format(addon.total_price || 0)}
                  </Text>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <>
      {/* Itinerary Section */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading>Itinerary</Heading>
          {booking.itinerary_id ? (
            <Button variant="secondary" onClick={handleViewItinerary}>
              View Itinerary
            </Button>
          ) : (
            hasPermission("itineraries:create") && (
              <Button
                variant="secondary"
                onClick={handleCreateItinerary}
                disabled={isCreatingItinerary}
              >
                {isCreatingItinerary ? "Creating..." : "Create Itinerary"}
              </Button>
            )
          )}
        </div>

        <SectionRow
          title="Itinerary ID"
          value={
            booking.itinerary_id ? (
              <Tooltip content={booking.itinerary_id}>
                <Text as="span">{truncate(booking.itinerary_id)}</Text>
              </Tooltip>
            ) : (
              <Text className="text-muted-foreground italic">
                Not created
              </Text>
            )
          }
        />
        <SectionRow
          title="Status"
          value={formatItineraryStatus()}
        />
      </Container>

      {/* Stay Details Section */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading>Stay Details</Heading>
        </div>

        <SectionRow
          title="Hotel"
          value={booking.hotel_name || hotelDetails?.name || "Not specified"}
        />
        <SectionRow
          title="Room Type"
          value={booking.room_type || "Not specified"}
        />
        <SectionRow
          title="Dates"
          value={formatDateRange()}
        />
        <SectionRow
          title="Guests"
          value={`${booking.number_of_guests || 1} Guest${(booking.number_of_guests || 1) > 1 ? 's' : ''}, ${booking.number_of_rooms || 1} Room${(booking.number_of_rooms || 1) > 1 ? 's' : ''}`}
        />
      </Container>

      {/* Add-ons Section */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading>Add-ons</Heading>
          {hasPermission("bookings:update") && (
            <ActionMenu
              groups={[
                {
                  actions: [
                    {
                      label: "Add Add-on",
                      onClick: handleAddAddOn,
                      icon: <PlusMini />,
                    },
                  ],
                },
              ]}
            />
          )}
        </div>

        <SectionRow
          title="Booked Services"
          value={formatAddOns()}
        />
      </Container>

      {/* Add-on Management Panel */}
      <AddOnManagementPanel
        open={showAddOnPanel}
        onClose={handleAddOnPanelClose}
        onAddOnAdded={handleAddOnSuccess}
        bookingId={booking?.id}
        destinationId={hotelDetails?.destination_id}
        existingAddOnIds={addOns?.map(addon => addon.add_on_id || addon.id) || []}
        checkInDate={booking?.check_in_date}
        checkOutDate={booking?.check_out_date}
        hotelId={hotelDetails?.id}
      />

    </>
  );
};

export default ConciergeBookingItinerarySection;
