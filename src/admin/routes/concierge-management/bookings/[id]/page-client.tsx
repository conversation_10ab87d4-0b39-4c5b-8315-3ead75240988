import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Heading,
  Text,
  Toaster,
  toast,
  Badge,
  Icon<PERSON>utton,
  Container,
} from "@camped-ai/ui";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON>ef<PERSON>, Edit } from "lucide-react";

import { TwoColumnPage } from "../../../../../components/layout/pages";
import { useRbac } from "../../../../hooks/use-rbac";
import Spinner from "../../../../components/shared/spinner";

// Import section components
import ConciergeBookingGeneralSection from "./components/concierge-booking-general-section.tsx";
import ConciergeBookingConciergeSection from "./components/concierge-booking-concierge-section.tsx";
import ConciergeBookingItinerarySection from "./components/concierge-booking-itinerary-section.tsx";
import ConciergeBookingTabsSection from "./components/concierge-booking-tabs-section";

const ConciergeBookingDetailPageClient = () => {
  const { id: bookingId } = useParams();
  const navigate = useNavigate();
    const [searchQuery, setSearchQuery] = useState("")
  const { hasPermission } = useRbac();
  const [booking, setBooking] = useState<any>(null);
  const [hotelDetails, setHotelDetails] = useState<any>(null);
  const [addOns, setAddOns] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch booking details
  const fetchBookingDetails = async () => {
    if (!bookingId) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      const response = await fetch(
        `/admin/concierge-management/bookings/${bookingId}`
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch booking details: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();

      if (!data.booking) {
        throw new Error("No booking data in response");
      }

      setBooking(data.booking);
      setAddOns(data.add_ons || []);

      // Fetch hotel details if we have a hotel ID
      if (data.booking.hotel_id) {
        fetchHotelDetails(data.booking.hotel_id);
      }
    } catch (error: any) {
      console.error("Error fetching booking details:", error);
      toast.error("Error", {
        description: `Failed to fetch booking details: ${error.message}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch hotel details
  const fetchHotelDetails = async (hotelId: string) => {
    try {
      const response = await fetch(`/admin/hotel-management/hotels/${hotelId}`);
      if (response.ok) {
        const data = await response.json();
        if (data.hotel) {
          setHotelDetails(data.hotel);
        }
      }
    } catch (error) {
      console.error("Error fetching hotel details:", error);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchBookingDetails();
  }, [bookingId]);

  // Go back to bookings list
  const handleGoBack = () => {
    navigate("/concierge-management/bookings");
  };

  if (isLoading) {
    return (
      <Container>
        <div className="flex items-center justify-center py-8">
          <Spinner size="medium" />
          <div className="ml-4 text-muted-foreground">
            Loading booking details...
          </div>
        </div>
      </Container>
    );
  }

  if (!booking) {
    return (
      <Container>
        <div className="text-center py-8">
          <Heading>Booking Not Found</Heading>
          <Text className="mt-2">
            The booking you're looking for doesn't exist or has been removed.
          </Text>
          <Button className="mt-4" onClick={handleGoBack}>
            Back to Bookings
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <div className="gap-4">
      <Toaster />
      <Container className="divide-y p-0 mb-3">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Heading level="h1" className="text-2xl">
              {booking.guest_name || "Guest Booking"}
            </Heading>
            <Badge>
              {booking.status?.replace("_", " ") || "Pending"}
            </Badge>
            {booking.priority_level && (
              <Badge>
                {booking.priority_level} Priority
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            {hasPermission("bookings:update") && (
              <div className="flex items-center gap-2">
                <IconButton size="small">
                  <Edit className="h-4 w-4" />
                </IconButton>
                <Button variant="secondary" onClick={handleGoBack} size="small">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Bookings
                </Button>
              </div>
            )}
          </div>
        </div>
      </Container>
      <TwoColumnPage
        widgets={{
          before: [],
          after: [],
          sideBefore: [],
          sideAfter: [],
        }}
        showJSON={false}
        showMetadata={false}
        data={booking}
        hasOutlet={false}
      >
        <TwoColumnPage.Main>
          {/* Main sections */}
          <ConciergeBookingGeneralSection
            booking={booking}
            hotelDetails={hotelDetails}
          />

          {/* Tabs section */}
          <ConciergeBookingTabsSection
            booking={booking}
          />

        </TwoColumnPage.Main>

        {/* <ConciergeBookingTasksSection booking={booking} /> */}

        <TwoColumnPage.Sidebar>
          <ConciergeBookingConciergeSection booking={booking} />
          <ConciergeBookingItinerarySection
            booking={booking}
            addOns={addOns}
            hotelDetails={hotelDetails}
            onItineraryCreated={fetchBookingDetails}
            onAddOnAdded={fetchBookingDetails}
          />
        </TwoColumnPage.Sidebar>
      </TwoColumnPage>
    </div>
  );
};

export default ConciergeBookingDetailPageClient;
