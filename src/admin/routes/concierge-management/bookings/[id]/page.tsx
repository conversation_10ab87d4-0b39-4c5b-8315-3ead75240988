import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Heading, Text } from "@camped-ai/ui";

import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import ConciergeBookingDetail from "./page-client";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";

const ConciergeBookingDetailPage = () => {

  return (
    <>
      <PermissionBasedSidebarHider />
        <RoleGuard
          requirePermission="bookings:view"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view concierge booking details.
              </Text>
            </div>
          }
        >
          <ConciergeBookingDetail />
        </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Concierge Booking Details",
});

export default ConciergeBookingDetailPage;
