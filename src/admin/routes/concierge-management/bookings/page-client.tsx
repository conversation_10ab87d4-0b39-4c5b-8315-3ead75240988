"use client";

import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Trash,
  Calendar,
  Copy,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  DropdownMenu,
} from "@camped-ai/ui";
import { useTranslation } from "react-i18next";
import { useMemo } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useReactTable, getCoreRowModel, createColumnHelper, ColumnDef } from "@tanstack/react-table";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";
import type { BookingScreenData } from "./loader";
import type { Hotel } from "./api";

// Status badge colors



// Status badge colors
const statusColors = {
  pending: "orange",
  confirmed: "green",
  checked_in: "blue",
  checked_out: "purple",
  canceled: "grey",
  no_show: "grey",
} as const;

interface BookingsPageClientProps {
  bookings: BookingScreenData[];
  hotels: Hotel[];
  isLoading: boolean;
  totalCount: number;
  pageSize: number;
  hotelId?: string | null;
}

const BookingsPageClient: React.FC<BookingsPageClientProps> = ({
  bookings,
  hotels,
  isLoading,
  totalCount,
  pageSize,
  hotelId,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { hasPermission } = useRbac();

  // Column helper for type safety
  const columnHelper = createColumnHelper<BookingScreenData>();

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "grey";
  };

  const handleCopyBookingId = (bookingId: string) => {
    navigator.clipboard.writeText(bookingId);
    toast.success("Booking ID copied to clipboard");
  };

  console.log({hotels})

  // Define columns
  const columns = useMemo<ColumnDef<BookingScreenData, any>[]>(() => [
    columnHelper.display({
      id: "booking",
      header: "Booking",
      cell: ({ row }) => {
        const booking = row.original;
        return (
          <div className="flex items-center gap-x-3 w-[160px] truncate">
            <div className="flex h-8 w-8 items-center justify-center rounded bg-ui-bg-subtle">
              <Calendar className="h-4 w-4 text-ui-fg-subtle" />
            </div>
            <div>
              <Text className="txt-compact-medium-plus" weight="plus">
                {booking.order_id || booking.id}
              </Text>
              <div className="flex items-center gap-x-1">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopyBookingId(booking.display_id || booking.id);
                  }}
                  className="text-ui-fg-muted hover:text-ui-fg-subtle"
                >
                  <Copy className="h-3 w-3" />
                </button>
              </div>
            </div>
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "guest",
      header: "Guest",
      cell: ({ row }) => {
        const booking = row.original;
        return (
          <div className="w-[200px] truncate">
            <Text className="txt-compact-medium">
              {booking.metadata?.guest_name || "—"}
            </Text>
            <Text className="txt-compact-small text-ui-fg-subtle ">
              {booking.metadata?.guest_email || booking.email || "—"}
            </Text>
          </div>
        );
      },
    }),
    ...(hotelId ? [] : [
      columnHelper.display({
        id: "hotel",
        header: "Hotel",
        cell: ({ row }) => {
          const booking = row.original;
          const hotelName = hotels.find(h => h.id === booking.hotel_id)?.name;
          return (
            <Text className="txt-compact-medium w-[120px]">
              {hotelName || "—"}
            </Text>
          );
        },
      }),
    ]),
    columnHelper.display({
      id: "room_type",
      header: "Room Type",
      cell: ({ row }) => {
        const booking = row.original;
        return (
          <Text className="txt-compact-medium w-[120px]">
            {booking.room_type || booking.metadata?.room_type || "—"}
          </Text>
        );
      },
    }),
    columnHelper.display({
      id: "dates",
      header: "Dates",
      cell: ({ row }) => {
        const booking = row.original;
        const checkIn = booking.metadata?.check_in_date;
        const checkOut = booking.metadata?.check_out_date;

        if (!checkIn || !checkOut) return <Text className="txt-compact-medium">—</Text>;

        const formatDate = (dateString: string) => {
          try {
            return new Date(dateString).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric'
            });
          } catch {
            return dateString;
          }
        };

        return (
          <div className="w-[100px]">
            <Text className="txt-compact-medium">
              {formatDate(checkIn)} - {formatDate(checkOut)}
            </Text>
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "duration",
      header: "Duration",
      cell: ({ row }) => {
        const booking = row.original;
        const nights = booking.nights;

        if (!nights) {
          // Calculate nights if not available
          const checkIn = booking.metadata?.check_in_date;
          const checkOut = booking.metadata?.check_out_date;

          if (checkIn && checkOut) {
            try {
              const checkInDate = new Date(checkIn);
              const checkOutDate = new Date(checkOut);
              const calculatedNights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));
              return (
                <Text className="txt-compact-medium w-[60px]">
                  {calculatedNights} {calculatedNights === 1 ? 'Night' : 'Nights'}
                </Text>
              );
            } catch {
              return <Text className="txt-compact-medium  w-[60px]">—</Text>;
            }
          }
          return <Text className="txt-compact-medium  w-[60px]">—</Text>;
        }

        return (
          <Text className="txt-compact-medium  w-[60px]">
            {nights} {nights === 1 ? 'Night' : 'Nights'}
          </Text>
        );
      },
    }),
        columnHelper.display({
      id: "status",
      header: "Status",
      cell: ({ row }) => {
        const booking = row.original;
        return (
          <Badge color={getStatusBadgeVariant(booking.status)} size="xsmall">
            <span className="inter-small-semibold">
              {booking.status?.charAt(0).toUpperCase() + booking.status?.slice(1)}
            </span>
          </Badge>
        );
      },
    }),
    columnHelper.display({
      id: "payment_status",
      header: "Payment",
      cell: ({ row }) => {
        const booking = row.original;
        const paymentStatus = booking.payment_status || booking.metadata?.payment_status || "pending";

        const getPaymentColor = (status: string) => {
          switch (status.toLowerCase()) {
            case 'paid':
            case 'captured':
              return 'green';
            case 'pending':
              return 'orange';
            case 'failed':
              return 'red';
            case 'refunded':
              return 'grey';
            default:
              return 'grey';
          }
        };

        return (
          <Badge color={getPaymentColor(paymentStatus)} size="xsmall">
            <span className="inter-small-semibold">
              {paymentStatus.charAt(0).toUpperCase() + paymentStatus.slice(1)}
            </span>
          </Badge>
        );
      },
    }),
    columnHelper.display({
      id: "total",
      header: "Total",
      cell: ({ row }) => {
        const booking = row.original;
        const total = booking.total || booking.metadata?.total_amount || 0;
        const currency = booking.currency_code || booking.metadata?.currency_code || 'USD';

        return (
          <Text className="txt-compact-medium font-medium">
            {new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: currency,
            }).format(total / 100)} {/* Assuming total is in cents */}
          </Text>
        );
      },
    }),
    columnHelper.display({
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const booking = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="transparent" size="small">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item
                onClick={() => navigate(`/concierge-management/bookings/${booking.order_id || booking.display_id || booking.id}`)}
              >
                <Eye className="h-4 w-4 mr-2" />
                View
              </DropdownMenu.Item>
              {hasPermission("bookings:edit") && (
                <DropdownMenu.Item
                  onClick={() => navigate(`/concierge-management/bookings/${booking.order_id || booking.display_id || booking.id}/edit`)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenu.Item>
              )}
              {hasPermission("bookings:delete") && (
                <DropdownMenu.Item
                  onClick={() => {
                    console.log("Delete booking:", booking.order_id || booking.display_id || booking.id);
                  }}
                  className="text-red-600"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenu.Item>
              )}
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    }),
  ], [hotelId, hasPermission, navigate]);

  // Define filters
  const filters: Filter[] = useMemo(() => [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { label: "Pending", value: "pending" },
        { label: "Confirmed", value: "confirmed" },
        { label: "Checked In", value: "checked_in" },
        { label: "Checked Out", value: "checked_out" },
        { label: "Canceled", value: "canceled" },
        { label: "No Show", value: "no_show" },
      ],
    },
    {
      key: "payment_status",
      label: "Payment Status",
      type: "select",
      options: [
        { label: "Pending", value: "pending" },
        { label: "Paid", value: "paid" },
        { label: "Failed", value: "failed" },
        { label: "Refunded", value: "refunded" },
      ],
    },
    ...(hotelId ? [] : [{
      key: "hotel_id",
      label: "Hotel",
      type: "select" as const,
      options: hotels.map(hotel => ({
        label: hotel.name,
        value: hotel.id,
      })),
    }]),
  ], [hotelId, hotels]);

  // Define sortable columns
  const orderBy = useMemo(() => [
    { key: "order_id" as keyof BookingScreenData, label: "Booking ID" },
    { key: "status" as keyof BookingScreenData, label: "Status" },
    { key: "payment_status" as keyof BookingScreenData, label: "Payment Status" },
    { key: "total" as keyof BookingScreenData, label: "Total" },
    { key: "created_at" as keyof BookingScreenData, label: "Created At" },
  ], []);

  // Get current page from URL
  const searchParams = new URLSearchParams(location.search);
  const currentPage = parseInt(searchParams.get("page") || "1");

  console.log({bookings})

  // Create table instance
  const table = useReactTable({
    data: bookings,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil(totalCount / pageSize),
    state: {
      pagination: {
        pageIndex: currentPage - 1,
        pageSize,
      },
    },
  });

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">Concierge Bookings</Heading>
        </div>
        <div className="flex items-center gap-x-2">
          <Button size="small" variant="secondary" asChild>
            <Link to="create">{t("actions.create")}</Link>
          </Button>
        </div>
      </div>

      {/* DataTable */}
      <DataTable
        table={table}
        columns={columns}
        pageSize={pageSize}
        count={totalCount}
        isLoading={isLoading}
        filters={filters}
        orderBy={orderBy}
        search="autofocus"
        pagination
        navigateTo={(row) => `/concierge-management/bookings/${row.original.order_id || row.original.display_id || row.original.id}`}
        queryObject={Object.fromEntries(searchParams)}
        noRecords={{
          title: "No bookings found",
          message: "Get started by creating your first booking",
        }}
      />
      <Toaster />
    </Container>
  );
};

export default BookingsPageClient;
