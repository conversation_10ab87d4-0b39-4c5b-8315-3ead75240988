import { QueryClient } from "@tanstack/react-query"
import { bookingsQueryKeys } from "../../../../hooks/api/bookings"
import { sdk } from "../../../../admin/lib/sdk"
import { queryClient } from "../../../../admin/lib/query-client"

// Types for booking screen data
export interface BookingScreenFilters {
    limit?: number
    offset?: number
    hotel_id?: string
    status?: string
    payment_status?: string
    booking_status?: string
    sort_by?: string
    sort_order?: 'asc' | 'desc'
    customer_id?: string
    page?: number
}

export interface BookingScreenData {
    id: string
    display_id: string
    customer_id: string
    order_id: string
    hotel_id: string
    room_config_id: string
    email: string
    status: string
    payment_status: string
    total: number
    currency_code: string
    created_at: string
    updated_at: string
    metadata?: {
        hotel_id?: string
        hotel_name?: string
        room_config_id?: string
        room_config_name?: string
        room_number?: string
        check_in_date?: string
        check_out_date?: string
        guest_name?: string
        guest_email?: string
        guest_phone?: string
        number_of_guests?: number
        special_requests?: string
        booking_status?: string
        [key: string]: any
    }
    items?: any[]
    payments?: any[]
    shipping_address?: any
    billing_address?: any
    // Additional computed fields for booking screen
    nights?: number
    guest_count?: number
    room_type?: string
    check_in_formatted?: string
    check_out_formatted?: string
}

export interface BookingScreenResponse {
    bookings: BookingScreenData[]
    count: number
    limit: number
    offset: number
    // Additional metadata for booking screen
    summary?: {
        total_bookings: number
        confirmed_bookings: number
        pending_bookings: number
        cancelled_bookings: number
        total_revenue: number
        average_booking_value: number
    }
}

// Default filters for booking screen
const defaultBookingScreenFilters: BookingScreenFilters = {
    limit: 20,
    offset: 0,
    sort_by: 'created_at',
    sort_order: 'desc',
}

// Query function for booking screen data
const bookingScreenQuery = (filters: BookingScreenFilters = {}) => {
    const mergedFilters = { ...defaultBookingScreenFilters, ...filters }

    return {
        queryKey: bookingsQueryKeys.list(mergedFilters),
        queryFn: async (): Promise<BookingScreenResponse> => {
            const params = new URLSearchParams()

            // Add filters to query params
            Object.entries(mergedFilters).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                    params.append(key, value.toString())
                }
            })

            const url = `/admin/hotel-management/bookings${params.toString() ? `?${params.toString()}` : ''}`

            try {
                const response = await sdk.client.fetch(url)

                // Transform the response data for booking screen
                const transformedData = transformBookingDataForScreen(response)

                return transformedData
            } catch (error) {
                console.error('Failed to fetch booking screen data:', error)
                throw error
            }
        }
    }
}

// Transform booking data for screen display
const transformBookingDataForScreen = (response: any): BookingScreenResponse => {
    const bookings = response.bookings || []

    // Transform each booking with additional computed fields
    const transformedBookings: BookingScreenData[] = bookings.map((booking: any) => {
        const metadata = booking.metadata || {}

        // Calculate nights if check-in and check-out dates are available
        let nights = 0
        if (metadata.check_in_date && metadata.check_out_date) {
            const checkIn = new Date(metadata.check_in_date)
            const checkOut = new Date(metadata.check_out_date)
            nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
        }

        // Format dates for display
        const formatDate = (dateString: string) => {
            if (!dateString) return ''
            try {
                return new Date(dateString).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                })
            } catch {
                return dateString
            }
        }

        return {
            ...booking,
            nights,
            guest_count: metadata.number_of_guests || 1,
            room_type: metadata.room_config_name || 'Standard Room',
            check_in_formatted: formatDate(metadata.check_in_date),
            check_out_formatted: formatDate(metadata.check_out_date),
        }
    })

    // Calculate summary statistics
    const summary = calculateBookingSummary(transformedBookings)

    return {
        bookings: transformedBookings,
        count: response.count || 0,
        limit: response.limit || 20,
        offset: response.offset || 0,
        summary,
    }
}

// Calculate booking summary statistics
const calculateBookingSummary = (bookings: BookingScreenData[]) => {
    const total_bookings = bookings.length
    const confirmed_bookings = bookings.filter(b =>
        b.status === 'completed' || b.payment_status === 'captured'
    ).length
    const pending_bookings = bookings.filter(b =>
        b.status === 'pending' || b.payment_status === 'pending'
    ).length
    const cancelled_bookings = bookings.filter(b =>
        b.status === 'canceled' || b.status === 'cancelled'
    ).length

    const total_revenue = bookings
        .filter(b => b.status === 'completed' || b.payment_status === 'captured')
        .reduce((sum, b) => sum + (b.total || 0), 0)

    const average_booking_value = confirmed_bookings > 0 ? total_revenue / confirmed_bookings : 0

    return {
        total_bookings,
        confirmed_bookings,
        pending_bookings,
        cancelled_bookings,
        total_revenue,
        average_booking_value,
    }
}

// Main loader function for booking screen
export const bookingScreenLoader = (client: QueryClient) => {
    return async (filters: BookingScreenFilters = {}) => {
        const query = bookingScreenQuery(filters)

        return (
            queryClient.getQueryData<BookingScreenResponse>(query.queryKey) ??
            (await client.fetchQuery(query))
        )
    }
}

// Loader function for specific hotel bookings
export const hotelBookingsLoader = (client: QueryClient) => {
    return async (hotelId: string, filters: Omit<BookingScreenFilters, 'hotel_id'> = {}) => {
        const mergedFilters = { ...filters, hotel_id: hotelId }
        const query = bookingScreenQuery(mergedFilters)

        return (
            queryClient.getQueryData<BookingScreenResponse>(query.queryKey) ??
            (await client.fetchQuery(query))
        )
    }
}

// Loader function for customer bookings
export const customerBookingsLoader = (client: QueryClient) => {
    return async (customerId: string, filters: Omit<BookingScreenFilters, 'customer_id'> = {}) => {
        const mergedFilters = { ...filters, customer_id: customerId }
        const query = bookingScreenQuery(mergedFilters)

        return (
            queryClient.getQueryData<BookingScreenResponse>(query.queryKey) ??
            (await client.fetchQuery(query))
        )
    }
}

// Loader function for booking screen with real-time updates
export const realtimeBookingScreenLoader = (client: QueryClient) => {
    return async (filters: BookingScreenFilters = {}) => {
        const query = bookingScreenQuery(filters)

        // Force fresh data for real-time scenarios
        return await client.fetchQuery({
            ...query,
            staleTime: 0, // Always fetch fresh data
            gcTime: 1 * 60 * 1000, // Keep in cache for 1 minute only
        })
    }
}

// Export default loader
export default bookingScreenLoader
