import React, { useState, useEffect } from "react";
import {
  Container,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Badge,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { ArrowLeft, Settings, MoreHorizontal, Eye } from "lucide-react";
import { useNavigate } from "react-router-dom";
import DaySidebar from "../../../../../components/concierge/itinerary/day-sidebar";
import EventPanel from "../../../../../components/concierge/itinerary/event-panel";
import Spinner from "../../../../../components/shared/spinner";

interface ItineraryBuilderProps {
  itineraryId?: string;
}

interface ItineraryData {
  id: string;
  booking_id: string;
  title?: string;
  status: "DRAFT" | "FINALIZED";
  created_by?: string;
  created_at: string;
  updated_at: string;
  days: Array<{
    id: string;
    itinerary_id: string;
    date: string;
    title?: string;
    sort_order: number;
    events: Array<{
      id: string;
      day_id: string;
      category: string;
      type?: string;
      title: string;
      notes?: string;
      start_time?: string;
      end_time?: string;
      duration?: string;
      timezone?: string;
      details?: Record<string, any>;
      price?: number;
      currency?: string;
      media?: string[];
      attachments?: string[];
      people?: string[];
    }>;
  }>;
}

const ItineraryBuilder: React.FC<ItineraryBuilderProps> = ({ itineraryId }) => {
  const navigate = useNavigate();
  const [itinerary, setItinerary] = useState<ItineraryData | null>(null);
  const [selectedDayId, setSelectedDayId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);

  // Fetch itinerary data
  const fetchItinerary = async () => {
    if (!itineraryId) return;

    setIsLoading(true);
    try {
      console.log("Fetching itinerary data for ID:", itineraryId);
      const response = await fetch(`/admin/concierge-management/itineraries/${itineraryId}`, {
        credentials: "include",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to fetch itinerary");
      }

      const data = await response.json();
      console.log("Fetched itinerary data:", data);

      // Ensure data structure is valid
      const itinerary = data.itinerary || {};
      const days = Array.isArray(itinerary.days) ? itinerary.days : [];

      const normalizedItinerary = {
        ...itinerary,
        days: days,
      };

      console.log("Normalized itinerary with", days.length, "days");
      setItinerary(normalizedItinerary);

      // Select first day by default if no day is currently selected
      if (days.length > 0 && !selectedDayId) {
        setSelectedDayId(days[0].id);
      }
    } catch (error) {
      console.error("Error fetching itinerary:", error);
      toast.error("Failed to load itinerary");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchItinerary();
  }, [itineraryId]);

  // Handle day selection
  const handleDaySelect = (dayId: string) => {
    setSelectedDayId(dayId);
  };

  // Handle day updates
  const handleDayUpdate = () => {
    fetchItinerary(); // Refresh data
  };

  // Handle event updates
  const handleEventUpdate = () => {
    fetchItinerary(); // Refresh data
  };

  // Get selected day data
  const selectedDay = itinerary?.days.find(day => day.id === selectedDayId);

  // Handle back navigation
  const handleBack = () => {
    navigate("/concierge-management/itineraries");
  };

  // Handle preview generation
  const handlePreview = async () => {
    if (!itineraryId) {
      toast.error("No itinerary ID available");
      return;
    }

    setIsGeneratingPreview(true);
    try {
      console.log("🚀 Generating preview for itinerary:", itineraryId);

      const response = await fetch(`/admin/concierge-management/itineraries/${itineraryId}/preview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // Add any additional data needed for preview generation
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("✅ Preview generated successfully:", data);

      if (data.success && data.data.preview_url) {
        toast.success("Preview generated! Redirecting to CRM...");

        // Redirect to the CRM preview page
        window.open(data.data.preview_url, '_blank');
      } else {
        throw new Error("No preview URL received from CRM");
      }

    } catch (error) {
      console.error("❌ Error generating preview:", error);
      toast.error(
        error instanceof Error
          ? `Failed to generate preview: ${error.message}`
          : "Failed to generate preview. Please try again."
      );
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  // Handle save
  const handleSave = async () => {
    if (!itinerary) return;
    
    setIsSaving(true);
    try {
      // Auto-save is handled by individual components
      toast.success("Itinerary saved successfully");
    } catch (error) {
      console.error("Error saving itinerary:", error);
      toast.error("Failed to save itinerary");
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner size="medium" />
        <div className="ml-4">Loading itinerary...</div>
      </div>
    );
  }

  if (!itinerary) {
    return (
      <div className="p-8 text-center">
        <Heading level="h1">Itinerary Not Found</Heading>
        <Text className="mt-2">
          The requested itinerary could not be found.
        </Text>
        <Button className="mt-4" onClick={handleBack}>
          Back to Itineraries
        </Button>
      </div>
    );
  }

  return (
    <>
      <Toaster />
      
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="small" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Trips
            </Button>
            <div>
              <Heading level="h1" className="text-xl font-semibold">
                {itinerary.title || "Cruise Itinerary"}
              </Heading>
              <Text className="text-sm text-gray-500">
                Booking #{itinerary.booking_id}
              </Text>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Badge color={itinerary.status === "DRAFT" ? "yellow" : "green"}>
              {itinerary.status}
            </Badge>
            <Button
              variant="secondary"
              size="small"
              onClick={handlePreview}
              disabled={isGeneratingPreview}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isGeneratingPreview ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                  Generating...
                </div>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </>
              )}
            </Button>
            <Button variant="ghost" size="small">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="ghost" size="small">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-120px)]">
        {/* Left Sidebar - Days */}
        <div className="w-80 bg-gray-50 border-r border-gray-200 overflow-y-auto">
          <DaySidebar
            itinerary={itinerary}
            selectedDayId={selectedDayId}
            onDaySelect={handleDaySelect}
            onDayUpdate={handleDayUpdate}
          />
        </div>

        {/* Main Panel - Events */}
        <div className="flex-1 overflow-y-auto">
          {selectedDay ? (
            <EventPanel
              day={selectedDay}
              onEventUpdate={handleEventUpdate}
            />
          ) : (
            <div className="p-8 text-center">
              <Text className="text-gray-500">
                Select a day to view and manage events
              </Text>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ItineraryBuilder;
