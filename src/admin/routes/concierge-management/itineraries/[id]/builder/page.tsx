import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import ItineraryBuilder from "./page-client";
import { RoleGuard } from "../../../../../components/rbac/RoleGuard";

const ItineraryBuilderPage = () => {
  const { id } = useParams();

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        <RoleGuard
          requirePermission="itineraries:edit"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to edit itineraries.
              </Text>
            </div>
          }
        >
          <ItineraryBuilder itineraryId={id} />
        </RoleGuard>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Itinerary Builder",
});

export default ItineraryBuilderPage;
