import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";
import StatusConfiguration from "../../../../components/concierge/status-configuration";

const ConciergeStatusConfigPage = () => {
  return (
    <>
      <PermissionBasedSidebarHider />
      <Container>
        <RoleGuard
          requirePermission="concierge_config:manage"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to manage status configuration.
              </Text>
            </div>
          }
        >
          <StatusConfiguration />
        </RoleGuard>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Status Configuration",
});

export default ConciergeStatusConfigPage;
