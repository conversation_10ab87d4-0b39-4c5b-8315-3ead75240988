import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";
import EmailTemplateManagement from "../../../../components/concierge/email-template-management";

const ConciergeEmailTemplatesPage = () => {
  return (
    <>
      <PermissionBasedSidebarHider />
      <Container>
        <RoleGuard
          requirePermission="concierge_config:manage"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to manage email templates.
              </Text>
            </div>
          }
        >
          <EmailTemplateManagement />
        </RoleGuard>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Email Templates",
});

export default ConciergeEmailTemplatesPage;
