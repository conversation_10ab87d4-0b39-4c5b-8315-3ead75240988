import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Toaster,
  toast,
  Label,
  Switch,
  IconButton,
} from "@camped-ai/ui";
import React, { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Plus, Trash2, Save, Loader2 } from "lucide-react";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { MultiSelect } from "../../../../../components/common/MultiSelect";
import { useRbac } from "../../../../../hooks/use-rbac";

import {
  useSupplier,
  useUpdateSupplier,
} from "../../../../../hooks/vendor-management/use-suppliers";
import { Supplier } from "../../../../../hooks/supplier-management/use-suppliers-list";

import { useCategories } from "../../../../../hooks/supplier-products-services/use-categories";
import {
  REGIONS,
  TIMEZONES,
  LANGUAGES,
  PAYMENT_METHODS,
  PAYOUT_TERMS,
  CURRENCIES,
  SUPPLIER_STATUSES,
  SUPPLIER_TYPES,
} from "../../../../../constants/supplier-form-options";
import { DocumentUpload } from "../../../../../components/vendor-management";

// Contact interface
interface Contact {
  id: string;
  name: string;
  email: string;
  phone_number?: string;
  is_whatsapp: boolean;
  is_primary: boolean;
}

// Form validation schema
const supplierSchema = z.object({
  // Basic Info
  name: z.string().min(1, "Supplier name is required"),
  supplier_type: z.enum(["Company", "Individual"], {
    required_error: "Supplier type is required",
  }),
  website: z.string().url().optional().or(z.literal("")),
  handle: z.string().optional(),
  status: z
    .enum(["Active", "Inactive", "Pending Approval", "Suspended", "Terminated"])
    .default("Active"),

  preference: z.enum(["Preferred", "Backup"]).optional(),
  region: z.string().optional(),
  timezone: z.string().optional(),
  language_preference: z.array(z.string()).optional(),
  payment_method: z.string().optional(),
  payout_terms: z.string().optional(),
  tax_id: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[A-Z0-9\-]{5,20}$/.test(val.replace(/[\s]/g, "")),
      "Please enter a valid tax ID (5-20 alphanumeric characters)"
    ),
  default_currency: z.string().default("CHF"),
  bank_account_details: z.string().optional(),
  categories: z.array(z.string()).optional(),

  // Address
  address: z.string().optional(),

  // Contacts
  contacts: z
    .array(
      z.object({
        id: z.string(),
        name: z.string().min(1, "Contact name is required"),
        email: z.string().email("Valid email is required"),
        phone_number: z
          .string()
          .optional()
          .refine(
            (val) => !val || /^[\+]?[1-9][\d]{0,15}$/.test(val.replace(/[\s\-\(\)]/g, "")),
            "Please enter a valid phone number"
          ),
        is_whatsapp: z.boolean().default(false),
        is_primary: z.boolean(),
      })
    )
    .min(1, "At least one contact is required"),
});

type SupplierFormData = z.infer<typeof supplierSchema>;

const EditSupplierPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();

  // API hooks
  const updateSupplier = useUpdateSupplier();
  const { data: categoriesData, isLoading: categoriesLoading } = useCategories({
    is_active: true,
  });
  const { data: supplierData, isLoading, error } = useSupplier(id!);
  const vendor = supplierData?.supplier as unknown as Supplier;

  // Form setup
  const form = useForm<SupplierFormData>({
    resolver: zodResolver(supplierSchema),
    defaultValues: {
      name: "",
      supplier_type: "Company",
      website: "",
      handle: "",
      status: "Active",

      preference: undefined,
      region: "",
      timezone: "",
      language_preference: [],
      payment_method: "",
      payout_terms: "",
      tax_id: "",
      default_currency: "CHF",
      bank_account_details: "",
      categories: [],
      address: "",
      contacts: [
        {
          id: "1",
          name: "",
          email: "",
          phone_number: "",
          is_whatsapp: false,
          is_primary: true,
        },
      ],
    },
  });

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting },
  } = form;
  const watchedContacts = watch("contacts");
  const watchedName = watch("name");

  const watchedCategories = watch("categories");

  // Generate handle from name
  useEffect(() => {
    if (watchedName) {
      const handle = watchedName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();
      setValue("handle", handle);
    }
  }, [watchedName, setValue]);

  // Check permission and redirect if not authorized
  useEffect(() => {
    if (!hasPermission("supplier_management:edit")) {
      navigate("/supplier-management");
    }
  }, [hasPermission, navigate]);



  // Generate available categories from all categories (no business type filtering)
  const availableCategories = React.useMemo(() => {
    if (!categoriesData?.categories) {
      return [];
    }

    return categoriesData.categories.map((category) => ({
      value: category.id, // Use category ID as value
      label: category.name, // Display category name as label
    }));
  }, [categoriesData]);

  // Helper function to handle category selection change
  const handleCategoryChange = (selectedCategoryIds: string[]) => {
    setValue("categories", selectedCategoryIds);
  };

  // Load supplier data into form
  useEffect(() => {
    if (vendor) {
      // Transform contacts from API format to form format
      const transformedContacts = vendor.contacts?.length
        ? (() => {
            // First, check if any contact is already marked as primary
            const hasPrimaryContact = vendor.contacts.some((contact: any) => contact.is_primary === true);

            return vendor.contacts.map((contact: any, index: number) => ({
              id: contact.id || (index + 1).toString(),
              name: contact.name || "",
              email: contact.email || "",
              phone_number: contact.phone_number || "",
              is_whatsapp: contact.is_whatsapp || false,
              // Only set first contact as primary if no contact is explicitly marked as primary
              is_primary: contact.is_primary === true || (!hasPrimaryContact && index === 0),
            }));
          })()
        : [
            {
              id: "1",
              name: "",
              email: "",
              phone_number: "",
              is_whatsapp: false,
              is_primary: true,
            },
          ];

      // Helper function to find matching option value for dropdowns
      const findOptionValue = (options: any[], dbValue: string) => {
        if (!dbValue) return "";

        // First try exact match on value
        const exactMatch = options.find((opt) => opt.value === dbValue);
        if (exactMatch) return dbValue;

        // Try case-insensitive match on value
        const caseInsensitiveMatch = options.find(
          (opt) => opt.value.toLowerCase() === dbValue.toLowerCase()
        );
        if (caseInsensitiveMatch) return caseInsensitiveMatch.value;

        // Try match on label (for cases where DB stores label instead of value)
        const labelMatch = options.find(
          (opt) => opt.label.toLowerCase() === dbValue.toLowerCase()
        );
        if (labelMatch) return labelMatch.value;

        return "";
      };

      const mappedRegion = findOptionValue(REGIONS, vendor.region || "");
      const mappedPaymentMethod = findOptionValue(
        PAYMENT_METHODS,
        vendor.payment_method || ""
      );
      const mappedPayoutTerms = findOptionValue(
        PAYOUT_TERMS,
        vendor.payout_terms || ""
      );



      reset({
        name: vendor.name || "",
        supplier_type: (vendor.supplier_type === "Company" || vendor.supplier_type === "Individual")
          ? vendor.supplier_type
          : "Company",
        website: vendor.website || "",
        handle: vendor.handle || "",
        status: vendor.status === "Active" ? "Active" :
               vendor.status === "Inactive" ? "Inactive" :
               vendor.status === "pending_approval" ? "Pending Approval" :
               vendor.status === "suspended" ? "Suspended" :
               vendor.status === "terminated" ? "Terminated" : "Active",

        preference: (vendor as any).preference as
          | "Preferred"
          | "Backup"
          | undefined,
        region: mappedRegion,
        timezone: vendor.timezone || "",
        language_preference: Array.isArray(vendor.language_preference)
          ? vendor.language_preference
          : [],
        payment_method: mappedPaymentMethod,
        payout_terms: mappedPayoutTerms,
        tax_id: vendor.tax_id || "",
        default_currency: vendor.default_currency || "CHF",
        bank_account_details: vendor.bank_account_details || "",
        categories: (() => {
          const categories = Array.isArray(vendor.categories) ? vendor.categories : [];
          return categories;
        })(),
        address: (vendor as any).address || "",
        contacts: transformedContacts,
      });




    }
  }, [vendor, reset]);

  // Handle error state
  useEffect(() => {
    if (error) {
      toast.error("Failed to load supplier details");
      navigate("/supplier-management/suppliers");
    }
  }, [error, navigate]);

  // Contact management functions
  const addContact = () => {
    const newContact: Contact = {
      id: Date.now().toString(),
      name: "",
      email: "",
      phone_number: "",
      is_whatsapp: false,
      is_primary: false,
    };
    setValue("contacts", [...watchedContacts, newContact]);
  };

  const removeContact = (contactId: string) => {
    if (watchedContacts.length <= 1) {
      toast.error("At least one contact is required");
      return;
    }
    const updatedContacts = watchedContacts.filter((c) => c.id !== contactId);
    setValue("contacts", updatedContacts);
  };

  const setPrimaryContact = (contactId: string) => {
    const updatedContacts = watchedContacts.map((contact) => ({
      ...contact,
      is_primary: contact.id === contactId,
    }));
    setValue("contacts", updatedContacts);
  };



  // Update supplier
  const updateSupplierData = async (data: SupplierFormData) => {
    try {
      // Transform contacts data - ensure proper structure and types
      const transformedContacts = data.contacts.map((contact) => ({
        ...(contact.id && { id: contact.id }), // Only include id if it exists
        name: contact.name,
        email: contact.email,
        phone_number: contact.phone_number && contact.phone_number.trim()
          ? String(contact.phone_number).trim()
          : undefined,
        is_whatsapp: Boolean(contact.is_whatsapp),
        is_primary: Boolean(contact.is_primary),
      }));

      // Helper function to only include non-empty values
      const cleanValue = (value: any) => {
        if (value === null || value === undefined || value === "") {
          return "";
        }
        return value;
      };

      // Transform data for API - use new format with contacts array
      const supplierData = {
        id: id!,
        // Basic Info
        name: data.name,
        supplier_type: data.supplier_type,
        website: cleanValue(data.website),
        handle: cleanValue(data.handle),
        status: data.status,

        preference: data.preference,

        // Business Info
        region: cleanValue(data.region),
        timezone: cleanValue(data.timezone),
        language_preference: data.language_preference && data.language_preference.length > 0 ? data.language_preference : undefined,
        payment_method: cleanValue(data.payment_method),
        payout_terms: cleanValue(data.payout_terms),
        tax_id: cleanValue(data.tax_id),
        default_currency: data.default_currency,
        bank_account_details: cleanValue(data.bank_account_details),

        // Categories
        categories: data.categories && data.categories.length > 0 ? data.categories : undefined,

        // Address
        address: cleanValue(data.address),

        // Contacts with proper string conversion
        contacts: transformedContacts,
      };

      // Show loading toast
      const loadingToast = toast.loading("Updating supplier...");

      // Perform the update
      await updateSupplier.mutateAsync(supplierData);

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success("Supplier updated successfully!");

      // Navigate with a slight delay for better UX
      setTimeout(() => {
        navigate(`/supplier-management/suppliers/${id}`);
      }, 500);

    } catch (error: any) {
      console.error("Error updating supplier:", error);

      // Show error message
      const errorMessage = error instanceof Error
        ? error.message
        : "Failed to update supplier. Please try again.";
      toast.error(errorMessage);
    }
  };

  // Form submission
  const onSubmit = async (data: SupplierFormData) => {
    try {
      // Ensure only one primary contact
      const primaryContacts = data.contacts.filter((c) => c.is_primary);
      if (primaryContacts.length !== 1) {
        toast.error("Exactly one contact must be marked as primary");
        return;
      }

      // Proceed with update
      await updateSupplierData(data);
    } catch (error: any) {
      console.error("Error in form submission:", error);
      toast.error(error.message || "Failed to update supplier");
    }
  };



  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="divide-y p-0">
          <div className="flex items-center justify-center py-8">
            <Text>Loading supplier details...</Text>
          </div>
        </Container>
      </>
    );
  }

  if (!vendor) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="divide-y p-0">
          <div className="flex items-center justify-center py-8">
            <Text>Supplier not found</Text>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <IconButton
              size="small"
              onClick={() => navigate(`/supplier-management/suppliers/${id}`)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
            </IconButton>
            <Heading level="h1">Edit Supplier</Heading>
          </div>

          {/* Form Actions */}
          <div className="flex items-center gap-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => navigate(`/supplier-management/suppliers/${id}`)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              disabled={isSubmitting}
              className="flex items-center gap-2"
              onClick={handleSubmit(onSubmit)}
            >
              {isSubmitting ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {isSubmitting ? "Updating..." : "Update Supplier"}
            </Button>
          </div>
        </div>


      <>
        {/* Header */}
      
        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Single Page Layout with Sections */}

          {/* Section 1: Basic Information */}
          <div className="bg-white rounded-lg border p-6 shadow-sm">
            <div className="border-b border-gray-200 pb-4 mb-6">
              <Text weight="plus" className="text-xl text-gray-900">Basic Information</Text>
              <Text className="text-sm text-gray-600 mt-1">Essential supplier details and identification</Text>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Supplier Type *</Label>
                <Controller
                  name="supplier_type"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value placeholder="Select supplier type" />
                      </Select.Trigger>
                      <Select.Content>
                        {SUPPLIER_TYPES.map((type) => (
                          <Select.Item key={type.value} value={type.value}>
                            {type.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
                {errors.supplier_type && (
                  <Text size="small" className="text-red-600 mt-1">
                    {errors.supplier_type.message}
                  </Text>
                )}
              </div>

              <div>
                <Label>Status</Label>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value />
                      </Select.Trigger>
                      <Select.Content>
                        {SUPPLIER_STATUSES.map((status) => (
                          <Select.Item
                            key={status.value}
                            value={status.value}
                          >
                            {status.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div>
                <Label htmlFor="name">Name *</Label>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="name"
                      placeholder="Enter supplier name"
                      className={errors.name ? "border-red-500" : ""}
                    />
                  )}
                />
                {errors.name && (
                  <Text size="small" className="text-red-600 mt-1">
                    {errors.name.message}
                  </Text>
                )}
              </div>

              <div>
                <Label htmlFor="website">Website</Label>
                <Controller
                  name="website"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="website"
                      type="url"
                      placeholder="https://www.example.com"
                      className={errors.website ? "border-red-500" : ""}
                    />
                  )}
                />
                {errors.website && (
                  <Text size="small" className="text-red-600 mt-1">
                    {errors.website.message}
                  </Text>
                )}
              </div>
            </div>
          </div>

          {/* Section 2: Business Information */}
          <div className="bg-white rounded-lg border p-6 shadow-sm">
            <div className="border-b border-gray-200 pb-4 mb-6">
              <Text weight="plus" className="text-xl text-gray-900">Business Information</Text>
              <Text className="text-sm text-gray-600 mt-1">Business details, preferences, and financial information</Text>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label>Categories</Label>
                <Controller
                  name="categories"
                  control={control}
                  render={({ field }) => (
                    <MultiSelect
                      options={availableCategories}
                      selectedValues={field.value || []}
                      onChange={(selectedValues) => {
                        field.onChange(selectedValues);
                        handleCategoryChange(selectedValues);
                      }}
                      placeholder={
                        categoriesLoading
                          ? "Loading categories..."
                          : availableCategories.length === 0
                          ? "No categories available"
                          : "Select categories..."
                      }
                      disabled={categoriesLoading}
                      showSelectAll={true}
                      showSelectedTags={true}
                      className="my-1"
                    />
                  )}
                />
                <Text className="text-ui-fg-subtle text-sm mt-1">
                  Select the categories this supplier can provide.
                </Text>
                {errors.categories && (
                  <Text size="small" className="text-red-600 mt-1">
                    {errors.categories.message}
                  </Text>
                )}
              </div>

                    <div>
                      <Label>Preference</Label>
                      <Controller
                        name="preference"
                        control={control}
                        render={({ field }) => (
                          <Select
                            value={field.value || ""}
                            onValueChange={field.onChange}
                          >
                            <Select.Trigger>
                              <Select.Value placeholder="Select preference" />
                            </Select.Trigger>
                            <Select.Content>
                              <Select.Item value="Preferred">Preferred</Select.Item>
                              <Select.Item value="Backup">Backup</Select.Item>
                            </Select.Content>
                          </Select>
                        )}
                      />
                    </div>

                    <div>
                      <Label>Timezone</Label>
                      <Controller
                        name="timezone"
                        control={control}
                        render={({ field }) => (
                          <Select
                            value={field.value || ""}
                            onValueChange={field.onChange}
                          >
                            <Select.Trigger>
                              <Select.Value placeholder="Select timezone" />
                            </Select.Trigger>
                            <Select.Content>
                              {TIMEZONES.map((timezone) => (
                                <Select.Item
                                  key={timezone.value}
                                  value={timezone.value}
                                >
                                  {timezone.label}
                                </Select.Item>
                              ))}
                            </Select.Content>
                          </Select>
                        )}
                      />
                    </div>

                    <div>
                      <Label>Language Preference</Label>
                      <Controller
                        name="language_preference"
                        control={control}
                        render={({ field }) => (
                          <MultiSelect
                            options={LANGUAGES}
                            selectedValues={field.value || []}
                            onChange={field.onChange}
                            placeholder="Select languages"
                          />
                        )}
                      />
                    </div>

                    <div>
                      <Label>Payment Terms</Label>
                      <Controller
                        name="payout_terms"
                        control={control}
                        render={({ field }) => (
                          <Select
                            value={field.value || ""}
                            onValueChange={field.onChange}
                          >
                            <Select.Trigger>
                              <Select.Value placeholder="Select payment terms" />
                            </Select.Trigger>
                            <Select.Content>
                              {PAYOUT_TERMS.map((term) => (
                                <Select.Item key={term.value} value={term.value}>
                                  {term.label}
                                </Select.Item>
                              ))}
                            </Select.Content>
                          </Select>
                        )}
                      />
                    </div>

                    <div>
                      <Label htmlFor="payment_method">Payment Method</Label>
                      <Controller
                        name="payment_method"
                        control={control}
                        render={({ field }) => (
                          <Select
                            value={field.value || ""}
                            onValueChange={field.onChange}
                          >
                            <Select.Trigger>
                              <Select.Value placeholder="Select payment method" />
                            </Select.Trigger>
                            <Select.Content>
                              {PAYMENT_METHODS.map((method) => (
                                <Select.Item key={method.value} value={method.value}>
                                  {method.label}
                                </Select.Item>
                              ))}
                            </Select.Content>
                          </Select>
                        )}
                      />
                    </div>

                    <div>
                      <Label>Default Currency</Label>
                      <Controller
                        name="default_currency"
                        control={control}
                        render={({ field }) => (
                          <Select value={field.value} onValueChange={field.onChange}>
                            <Select.Trigger>
                              <Select.Value />
                            </Select.Trigger>
                            <Select.Content>
                              {CURRENCIES.map((currency) => (
                                <Select.Item key={currency.value} value={currency.value}>
                                  {currency.label}
                                </Select.Item>
                              ))}
                            </Select.Content>
                          </Select>
                        )}
                      />
                    </div>

                    <div>
                      <Label htmlFor="tax_id">Tax ID</Label>
                      <Controller
                        name="tax_id"
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            id="tax_id"
                            placeholder="Enter tax identification number"
                          />
                        )}
                      />
                      {errors.tax_id && (
                        <Text size="small" className="text-red-600 mt-1">
                          {errors.tax_id.message}
                        </Text>
                      )}
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="bank_account_details">Bank Account Details</Label>
                      <Controller
                        name="bank_account_details"
                        control={control}
                        render={({ field }) => (
                          <Textarea
                            {...field}
                            id="bank_account_details"
                            placeholder="Enter bank account information"
                            rows={3}
                          />
                        )}
                      />
                    </div>

            </div>
          </div>

          {/* Section 3: Address Information */}
          <div className="bg-white rounded-lg border p-6 shadow-sm">
            <div className="border-b border-gray-200 pb-4 mb-6">
              <Text weight="plus" className="text-xl text-gray-900">Address Information</Text>
              <Text className="text-sm text-gray-600 mt-1">Supplier location and contact address</Text>
            </div>
            <div>
              <Label htmlFor="address">Address Details</Label>
              <Controller
                name="address"
                control={control}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    id="address"
                    placeholder="Enter complete address details including street, city, state, postal code, country..."
                    rows={4}
                  />
                )}
              />
              <Text className="text-ui-fg-subtle text-sm mt-1">
                Enter the complete address information in a single block.
              </Text>
            </div>
          </div>



          {/* Section 4: Contacts */}
          <div className="bg-white rounded-lg border p-6 shadow-sm">
            <div className="border-b border-gray-200 pb-4 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <Text weight="plus" className="text-xl text-gray-900">Contacts</Text>
                  <Text className="text-sm text-gray-600 mt-1">Manage supplier contact information</Text>
                </div>
                <Button
                  type="button"
                  variant="secondary"
                  size="small"
                  onClick={addContact}
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Add Contact
                </Button>
              </div>
            </div>

                <div className="space-y-4">
                  {watchedContacts.map((contact, index) => (
                    <div key={contact.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <Text weight="plus">Contact {index + 1}</Text>
                        <div className="flex items-center gap-2">
                          <Controller
                            name={`contacts.${index}.is_primary`}
                            control={control}
                            render={({ field }) => (
                              <label className="flex items-center gap-2 cursor-pointer">
                                <input
                                  type="radio"
                                  name="primary_contact"
                                  checked={field.value}
                                  onChange={() => setPrimaryContact(contact.id)}
                                  className="text-blue-600"
                                />
                                <Text size="small">Primary</Text>
                              </label>
                            )}
                          />
                          {watchedContacts.length > 1 && (
                            <Button
                              type="button"
                              variant="danger"
                              size="small"
                              onClick={() => removeContact(contact.id)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor={`contact_name_${contact.id}`}>
                        Name *
                      </Label>
                      <Controller
                        name={`contacts.${index}.name`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            id={`contact_name_${contact.id}`}
                            placeholder="Contact name"
                            className={
                              errors.contacts?.[index]?.name
                                ? "border-red-500"
                                : ""
                            }
                          />
                        )}
                      />
                      {errors.contacts?.[index]?.name && (
                        <Text size="small" className="text-red-600 mt-1">
                          {errors.contacts[index].name?.message}
                        </Text>
                      )}
                    </div>

                    <div>
                      <Label htmlFor={`contact_email_${contact.id}`}>
                        Email *
                      </Label>
                      <Controller
                        name={`contacts.${index}.email`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            id={`contact_email_${contact.id}`}
                            type="email"
                            placeholder="<EMAIL>"
                            className={
                              errors.contacts?.[index]?.email
                                ? "border-red-500"
                                : ""
                            }
                          />
                        )}
                      />
                      {errors.contacts?.[index]?.email && (
                        <Text size="small" className="text-red-600 mt-1">
                          {errors.contacts[index].email?.message}
                        </Text>
                      )}
                    </div>
                  </div>

                  {/* Phone Number and WhatsApp */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <Label htmlFor={`contact_phone_${contact.id}`}>
                        Phone Number
                      </Label>
                      <Controller
                        name={`contacts.${index}.phone_number`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            id={`contact_phone_${contact.id}`}
                            type="tel"
                            placeholder="+****************"
                          />
                        )}
                      />
                      {errors.contacts?.[index]?.phone_number && (
                        <Text size="small" className="text-red-600 mt-1">
                          {errors.contacts[index]?.phone_number?.message}
                        </Text>
                      )}
                    </div>

                    <div className="flex items-center space-x-3 mt-6">
                      <Controller
                        name={`contacts.${index}.is_whatsapp`}
                        control={control}
                        render={({ field }) => (
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        )}
                      />
                      <Label htmlFor={`contact_whatsapp_${contact.id}`}>
                        WhatsApp Available
                      </Label>
                    </div>
                      </div>
                    </div>
                  ))}
                </div>
          </div>

          {/* Section 5: Documents */}
          <div className="bg-white rounded-lg border p-6 shadow-sm">
            <div className="border-b border-gray-200 pb-4 mb-6">
              <Text weight="plus" className="text-xl text-gray-900">Documents</Text>
              <Text className="text-sm text-gray-600 mt-1">Upload and manage supplier documents</Text>
            </div>
            <div>
              <DocumentUpload supplierId={id!} />
            </div>
          </div>
        </form>




      </>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Edit Supplier",
});

export default EditSupplierPage;
