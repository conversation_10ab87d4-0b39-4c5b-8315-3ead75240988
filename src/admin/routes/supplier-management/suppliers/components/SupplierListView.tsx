import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Edit, Eye, MoreHorizontal, Trash, UserX, ChevronUp, ChevronDown } from "lucide-react";
import { Buildings, PlusMini } from "@camped-ai/icons";
import {
  Text,
  Button,
  Badge,
  DropdownMenu,
  IconButton,
  Table,
  Prompt,
} from "@camped-ai/ui";
import { useRbac } from "../../../../hooks/use-rbac";
import { useDeleteSupplier, useUpdateSupplier } from "../../../../hooks/vendor-management/use-suppliers";
import { getCurrencyDisplayName } from "../../../../constants/supplier-form-options";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";

import type { Supplier } from "../../../../hooks/supplier-management/use-suppliers-list";

// Sorting interface
interface SortingOptions {
  sort_by: "name" | "status" | "created_at" | "updated_at";
  sort_order: "asc" | "desc";
}

interface SupplierListViewProps {
  suppliers: Supplier[];
  isLoading: boolean;
  onSupplierClick?: (id: string) => void;
  onEditClick?: (id: string) => void;
  onCreateClick?: () => void;
  onDeleteClick?: (id: string) => void;
  hasCreatePermission?: boolean;
  hasEditPermission?: boolean;
  hasDeletePermission?: boolean;
  // Sorting props
  sorting?: SortingOptions;
  onSortChange?: (sorting: SortingOptions) => void;
}

const SupplierListView: React.FC<SupplierListViewProps> = ({
  suppliers,
  isLoading,
  onSupplierClick,
  onEditClick,
  onCreateClick,
  onDeleteClick,
  hasCreatePermission = true,
  hasEditPermission = true,
  hasDeletePermission = true,
  sorting = { sort_by: "created_at", sort_order: "desc" },
  onSortChange,
}) => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const deleteSupplier = useDeleteSupplier();
  const updateSupplier = useUpdateSupplier();
  const { data: categoriesData } = useCategories({ is_active: true, limit: 100 });
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);
  const [activateConfirmOpen, setActivateConfirmOpen] = useState(false);
  const [supplierToActivate, setSupplierToActivate] = useState<Supplier | null>(null);

  // Helper functions (standardized with card view)

  const getStatusBadgeVariant = (
    status: string
  ): "green" | "red" | "orange" | "grey" => {
    switch (status?.toLowerCase()) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      case "pending":
        return "orange";
      default:
        return "grey";
    }
  };

  // Sorting helper functions
  const handleSort = (column: "name" | "status" | "created_at" | "updated_at") => {
    if (!onSortChange) return;

    let newSortOrder: "asc" | "desc" = "asc";

    // If clicking the same column, toggle the order
    if (sorting.sort_by === column) {
      newSortOrder = sorting.sort_order === "asc" ? "desc" : "asc";
    } else {
      // If clicking a different column, use default order for that column
      if (column === "created_at" || column === "updated_at") {
        newSortOrder = "desc"; // Date columns default to newest first
      } else {
        newSortOrder = "asc"; // Name and status default to alphabetical order (A-Z)
      }
    }

    onSortChange({
      sort_by: column,
      sort_order: newSortOrder,
    });
  };

  const getSortIcon = (column: "name" | "status" | "created_at" | "updated_at") => {
    if (sorting.sort_by !== column) {
      return <ChevronDown className="h-4 w-4 text-ui-fg-muted opacity-50" />;
    }

    return sorting.sort_order === "asc" ? (
      <ChevronUp className="h-4 w-4 text-ui-fg-base" />
    ) : (
      <ChevronDown className="h-4 w-4 text-ui-fg-base" />
    );
  };

  const SortableHeader: React.FC<{
    column: "name" | "status" | "created_at" | "updated_at";
    children: React.ReactNode;
    className?: string;
  }> = ({ column, children, className }) => (
    <Table.HeaderCell
      className={`${className} cursor-pointer hover:bg-ui-bg-subtle transition-colors select-none`}
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-1">
        {children}
        {getSortIcon(column)}
      </div>
    </Table.HeaderCell>
  );



  // Helper function to get category names from IDs
  const getCategoryNames = (categoryIds: string[]): string[] => {
    if (!categoriesData?.categories || !Array.isArray(categoryIds)) return [];
    return categoryIds
      .map((id) => {
        const category = categoriesData.categories.find((cat) => cat.id === id);
        return category ? category.name : null;
      })
      .filter((name): name is string => name !== null);
  };

  // Helper function to render categories
  const renderCategories = (categories: string[]) => {
    if (!categories || categories.length === 0) {
      return (
        <Text size="small" className="text-ui-fg-subtle">
          —
        </Text>
      );
    }

    const categoryNames = getCategoryNames(categories);
    if (categoryNames.length === 0) {
      return (
        <Text size="small" className="text-ui-fg-subtle">
          —
        </Text>
      );
    }

    // If only one category, show as a badge
    if (categoryNames.length === 1) {
      return (
        <Badge
          color="blue"
          className="rounded-full text-xs px-3 py-1.5 font-medium"
        >
          {categoryNames[0]}
        </Badge>
      );
    }

    // If multiple categories, show first one as badge with count
    return (
      <div className="flex items-center gap-1">
        <Badge
          color="blue"
          className="rounded-full text-xs px-3 py-1.5 font-medium"
        >
          {categoryNames[0]}
        </Badge>
        {categoryNames.length > 1 && (
          <Badge
            color="grey"
            className="rounded-full text-xs px-2 py-1 font-medium"
          >
            +{categoryNames.length - 1}
          </Badge>
        )}
      </div>
    );
  };

  // Helper function to render currency
  const renderCurrency = (currencyCode?: string) => {
    if (!currencyCode) {
      return (
        <Text size="small" className="text-ui-fg-subtle">
          Not set
        </Text>
      );
    }

    return (
      <Badge
        color="green"
        className="rounded-full text-xs px-3 py-1.5 font-medium"
      >
        {getCurrencyDisplayName(currencyCode)}
      </Badge>
    );
  };





  const handleDeleteClick = (supplier: Supplier, event: React.MouseEvent) => {
    event.stopPropagation();
    setSupplierToDelete(supplier);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!supplierToDelete) return;

    try {
      if (onDeleteClick) {
        onDeleteClick(supplierToDelete.id);
      } else {
        await deleteSupplier.mutateAsync(supplierToDelete.id);
      }
    } catch (error) {
      console.error("Error deleting supplier:", error);
    } finally {
      setDeleteConfirmOpen(false);
      setSupplierToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setSupplierToDelete(null);
  };

  const handleActivateClick = (supplier: Supplier, event: React.MouseEvent) => {
    event.stopPropagation();
    setSupplierToActivate(supplier);
    setActivateConfirmOpen(true);
  };

  const handleConfirmActivate = async () => {
    if (!supplierToActivate) return;

    try {
      const isCurrentlyActive = supplierToActivate.status?.toLowerCase() === 'active';
      const newStatus = isCurrentlyActive ? 'Inactive' : 'Active';

      await updateSupplier.mutateAsync({
        id: supplierToActivate.id,
        status: newStatus,
      });
    } catch (error) {
      console.error("Error updating supplier status:", error);
    } finally {
      setActivateConfirmOpen(false);
      setSupplierToActivate(null);
    }
  };

  const handleCancelActivate = () => {
    setActivateConfirmOpen(false);
    setSupplierToActivate(null);
  };



  const renderLoadingSkeleton = () =>
    Array.from({ length: 5 }).map((_, index) => (
      <Table.Row key={`skeleton-${index}`}>
        {/* Supplier Name skeleton */}
        <Table.Cell className="w-64">
          <div className="space-y-1">
            <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-32" />
            <div className="h-3 bg-ui-bg-subtle rounded animate-pulse w-24" />
          </div>
        </Table.Cell>
        {/* Status skeleton */}
        <Table.Cell className="w-32">
          <div className="h-6 bg-ui-bg-subtle rounded animate-pulse w-16" />
        </Table.Cell>
        {/* Category skeleton */}
        <Table.Cell className="w-40">
          <div className="h-6 bg-ui-bg-subtle rounded animate-pulse w-24" />
        </Table.Cell>
        {/* Currency skeleton */}
        <Table.Cell className="w-32">
          <div className="h-6 bg-ui-bg-subtle rounded animate-pulse w-16" />
        </Table.Cell>
        {/* Date Added skeleton */}
        <Table.Cell className="w-36">
          <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-20" />
        </Table.Cell>
        {/* Last Updated skeleton */}
        <Table.Cell className="w-36">
          <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-20" />
        </Table.Cell>
        {/* Actions skeleton */}
        <Table.Cell className="w-24">
          <div className="h-8 w-8 bg-ui-bg-subtle rounded animate-pulse" />
        </Table.Cell>
      </Table.Row>
    ));

  const renderEmptyState = () => (
    <div className="py-16 text-center">
      <div className="max-w-sm mx-auto">
        <Buildings className="h-16 w-16 text-ui-fg-muted mx-auto mb-4" />
        <Text size="large" weight="plus" className="text-ui-fg-base">
          No suppliers found
        </Text>
        <Text className="text-ui-fg-subtle mt-2">
          Try adjusting your filters or get started by adding your first
          supplier
        </Text>
        {(hasCreatePermission ||
          hasPermission("supplier_management:create")) && (
            <Button
              size="small"
              className="mt-6"
              onClick={() => {
                if (onCreateClick) {
                  onCreateClick();
                } else {
                  navigate("/supplier-management/suppliers-new/create");
                }
              }}
            >
              <PlusMini />
              Add your first supplier
            </Button>
          )}
      </div>
    </div>
  );

  const renderActionDropdown = (supplier: Supplier) => (
    <div onClick={(e) => e.stopPropagation()}>
      <DropdownMenu>
        <DropdownMenu.Trigger asChild>
          <IconButton size="small">
            <MoreHorizontal className="h-4 w-4" />
          </IconButton>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content align="end">
          <DropdownMenu.Item
            onClick={(e) => {
              e.stopPropagation();
              if (onSupplierClick) {
                onSupplierClick(supplier.id);
              } else {
                navigate(`/supplier-management/suppliers/${supplier.id}`);
              }
            }}
          >
            <Eye className="h-4 w-4 mr-2" />
            View
          </DropdownMenu.Item>
          {(hasEditPermission || hasPermission("supplier_management:edit")) && (
            <DropdownMenu.Item
              onClick={(e) => {
                e.stopPropagation();
                if (onEditClick) {
                  onEditClick(supplier.id);
                } else {
                  navigate(
                    `/supplier-management/suppliers/${supplier.id}/edit`
                  );
                }
              }}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </DropdownMenu.Item>
          )}
          <DropdownMenu.Item
            onClick={(e) => {
              handleActivateClick(supplier, e);
            }}
            className={
              supplier.status?.toLowerCase() === 'active'
                ?  "text-red-600 hover:text-red-700 hover:bg-red-50"
                : "text-green-600 hover:text-green-700 hover:bg-green-50"
            }
          >
            <UserX className="h-4 w-4 mr-2" />
            {supplier.status?.toLowerCase() === 'active' ? 'Inactivate' : 'Activate'}
          </DropdownMenu.Item>
          {(hasDeletePermission ||
            hasPermission("supplier_management:delete")) && (
              <DropdownMenu.Item
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteClick(supplier, e);
                }}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenu.Item>
            )}
        </DropdownMenu.Content>
      </DropdownMenu>
    </div>
  );

  return (
    <>
      <div className="mx-0 mt-6">
        {suppliers.length === 0 && !isLoading ? (
          renderEmptyState()
        ) : (
          <Table>
            <Table.Header>
              <Table.Row>
                <SortableHeader column="name" className="w-64">
                  Supplier Name
                </SortableHeader>
                <SortableHeader column="status" className="w-32">
                  Status
                </SortableHeader>
                <Table.HeaderCell className="w-40">Category</Table.HeaderCell>
                <Table.HeaderCell className="w-32">Currency</Table.HeaderCell>
                <SortableHeader column="created_at" className="w-36">
                  Date Added
                </SortableHeader>
                <SortableHeader column="updated_at" className="w-36">
                  Last Updated
                </SortableHeader>
                <Table.HeaderCell className="text-right w-24">
                  Actions
                </Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {isLoading
                ? renderLoadingSkeleton()
                : suppliers.map((supplier) => {
                  return (
                    <Table.Row
                      key={supplier.id}
                      className="cursor-pointer hover:bg-ui-bg-subtle transition-colors"
                      onClick={() => {
                        if (onSupplierClick) {
                          onSupplierClick(supplier.id);
                        } else {
                          navigate(
                            `/supplier-management/suppliers/${supplier.id}`
                          );
                        }
                      }}
                    >
                      {/* Supplier Name */}
                      <Table.Cell className="w-64">
                        <div className="min-w-0 max-w-64">
                          <div
                            className="font-medium text-ui-fg-base truncate text-sm"
                            title={supplier.name}
                          >
                            {supplier.name}
                          </div>
                          {supplier.website && (
                            <div className="flex items-center text-sm text-ui-fg-subtle mt-0.5">
                              <span className="truncate">
                                {supplier.website}
                              </span>
                            </div>
                          )}
                        </div>
                      </Table.Cell>

                      {/* Status */}
                      <Table.Cell>
                        <Badge
                          color={getStatusBadgeVariant(supplier.status)}
                          className="rounded-full text-xs px-3 py-1.5 font-medium"
                        >
                          {supplier.status}
                        </Badge>
                      </Table.Cell>

                      {/* Category */}
                      <Table.Cell>
                        {renderCategories(supplier.categories)}
                      </Table.Cell>

                      {/* Currency */}
                      <Table.Cell>
                        {renderCurrency(supplier.default_currency)}
                      </Table.Cell>

                      {/* Date Added */}
                      <Table.Cell className="w-36">
                        <Text className="text-sm text-ui-fg-subtle">
                          {supplier.created_at ? new Date(supplier.created_at).toLocaleDateString() : '-'}
                        </Text>
                      </Table.Cell>

                      {/* Last Updated */}
                      <Table.Cell className="w-36">
                        <Text className="text-sm text-ui-fg-subtle">
                          {supplier.updated_at ? new Date(supplier.updated_at).toLocaleDateString() : '-'}
                        </Text>
                      </Table.Cell>

                      {/* Actions */}
                      <Table.Cell className="text-right">
                        <div onClick={(e) => e.stopPropagation()}>
                          {renderActionDropdown(supplier)}
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  );
                })}
            </Table.Body>
          </Table>
        )}
      </div>

      {/* Delete Confirmation Prompt */}
      <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Supplier</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{supplierToDelete?.name}"? This action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelDelete}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={handleConfirmDelete}
              disabled={deleteSupplier.isPending}
            >
              {deleteSupplier.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      {/* Activate/Inactivate Confirmation Prompt */}
      <Prompt open={activateConfirmOpen} onOpenChange={setActivateConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>
              {supplierToActivate?.status?.toLowerCase() === 'active' ? 'Inactivate' : 'Activate'} Supplier
            </Prompt.Title>
            <Prompt.Description>
              Are you sure you want to {supplierToActivate?.status?.toLowerCase() === 'active' ? 'inactivate' : 'activate'} "{supplierToActivate?.name}"?
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelActivate}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={handleConfirmActivate}
              disabled={updateSupplier.isPending}
            >
              {updateSupplier.isPending
                ? (supplierToActivate?.status?.toLowerCase() === 'active' ? "Inactivating..." : "Activating...")
                : (supplierToActivate?.status?.toLowerCase() === 'active' ? 'Inactivate' : 'Activate')
              }
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export default SupplierListView;
