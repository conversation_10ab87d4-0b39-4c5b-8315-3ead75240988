import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import { Edit, Trash } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Label,
  Toaster,
  Badge,
  Prompt,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../hooks/use-rbac";
import {
  useSupplierOffering,
  useDeleteSupplierOffering,
} from "../../../../hooks/supplier-products-services/use-supplier-offerings";
import { type DynamicFieldSchema } from "../../../../components/supplier-management/dynamic-field-renderer";
import { formatCustomFieldValue } from "../../../../utils/format-custom-field-value";
import { useHotels } from "../../../../hooks/supplier-products-services/use-hotels";
import { useDestinations } from "../../../../hooks/supplier-products-services/use-destinations";
import { useProductsServices } from "../../../../hooks/supplier-products-services/use-products-services";
import CostHistoryTable from "../../../../components/supplier-management/cost-history-table";
import { useSupplierOfferingCostHistory } from "../../../../hooks/supplier-products-services/use-supplier-offering-cost-history";
import PricingCalculator from "../../../../components/supplier-management/pricing-calculator";

// Function to resolve hotel and destination IDs in product service names
const resolveProductServiceName = (
  productService: any,
  hotels: any[],
  destinations: any[]
): string => {
  if (
    !productService?.name ||
    !productService?.category?.dynamic_field_schema ||
    !productService?.custom_fields
  ) {
    return productService?.name || "Unknown Product/Service";
  }

  let resolvedName = productService.name;

  // Find hotel and destination fields in the category schema
  const hotelFields = productService.category.dynamic_field_schema.filter(
    (field: any) => field.type === "hotels" && field.used_in_product !== false
  );
  const destinationFields = productService.category.dynamic_field_schema.filter(
    (field: any) =>
      field.type === "destinations" && field.used_in_product !== false
  );

  // Resolve hotel field names
  hotelFields.forEach((field: any) => {
    if (productService.custom_fields[field.key]) {
      try {
        let hotelIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(productService.custom_fields[field.key])) {
          hotelIds = productService.custom_fields[field.key];
        } else if (
          typeof productService.custom_fields[field.key] === "string"
        ) {
          try {
            const parsed = JSON.parse(productService.custom_fields[field.key]);
            hotelIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            hotelIds = [productService.custom_fields[field.key]];
          }
        }

        hotelIds.forEach((hotelId) => {
          const hotel = hotels.find((h) => h.id === hotelId);
          if (hotel) {
            // Replace ID with name in the product service name
            resolvedName = resolvedName.replace(hotelId, hotel.name);
          }
        });
      } catch (error) {
        console.warn(
          "Error resolving hotel names in product service name:",
          error
        );
      }
    }
  });

  // Resolve destination field names
  destinationFields.forEach((field: any) => {
    if (productService.custom_fields[field.key]) {
      try {
        let destinationIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(productService.custom_fields[field.key])) {
          destinationIds = productService.custom_fields[field.key];
        } else if (
          typeof productService.custom_fields[field.key] === "string"
        ) {
          try {
            const parsed = JSON.parse(productService.custom_fields[field.key]);
            destinationIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            destinationIds = [productService.custom_fields[field.key]];
          }
        }

        destinationIds.forEach((destinationId) => {
          const destination = destinations.find((d) => d.id === destinationId);
          if (destination) {
            // Replace ID with name in the product service name
            resolvedName = resolvedName.replace(
              destinationId,
              destination.name
            );
          }
        });
      } catch (error) {
        console.warn(
          "Error resolving destination names in product service name:",
          error
        );
      }
    }
  });

  return resolvedName;
};

const SupplierOfferingDetailPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { hasPermission } = useRbac();

  // API calls
  const {
    data: offeringResponse,
    isLoading: offeringLoading,
    error,
  } = useSupplierOffering(id!);
  const { data: hotelsResponse } = useHotels({ is_active: true });
  const { data: destinationsResponse } = useDestinations({ is_active: true });
  const { data: productServicesResponse } = useProductsServices({
    status: "active",
    limit: 1000,
  });

  // Additional fetches for name resolution (includes inactive items)
  const { data: allHotelsResponse } = useHotels({ limit: 1000 });
  const { data: allDestinationsResponse } = useDestinations({ limit: 1000 });
  const { data: allProductServicesResponse } = useProductsServices({
    limit: 1000,
  });

  const deleteSupplierOffering = useDeleteSupplierOffering();

  // Cost history data
  const { data: costHistoryResponse, isLoading: costHistoryLoading } =
    useSupplierOfferingCostHistory(id!, { limit: 10 });

  // Extract data
  const offering = offeringResponse?.supplier_offering;
  const hotels = hotelsResponse?.hotels || [];
  const destinations = destinationsResponse?.destinations || [];
  const productServices = productServicesResponse?.product_services || [];

  // Extract additional data for name resolution (includes inactive items)
  const allHotels = allHotelsResponse?.hotels || [];
  const allDestinations = allDestinationsResponse?.destinations || [];
  const allProductServices = allProductServicesResponse?.product_services || [];
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const [categorySchema, setCategorySchema] = useState<DynamicFieldSchema[]>(
    []
  );

  // Set category schema when offering loads
  useEffect(() => {
    if (offering?.product_service?.category?.dynamic_field_schema) {
      const offeringFields =
        offering.product_service.category.dynamic_field_schema.filter(
          (field: any) => field.used_in_supplier_offering
        );
      setCategorySchema(offeringFields);
    }
  }, [offering]);

  const handleDelete = async () => {
    try {
      await deleteSupplierOffering.mutateAsync(id!);
      navigate("/supplier-management/supplier-offerings");
    } catch (error) {
      console.error("Error deleting supplier offering:", error);
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      default:
        return "grey";
    }
  };

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return "Not set";

    // Use UTC methods to prevent timezone conversion issues
    const date = new Date(dateStr);
    const day = String(date.getUTCDate()).padStart(2, '0');
    const month = String(date.getUTCMonth() + 1).padStart(2, '0');
    const year = date.getUTCFullYear();

    return `${day}/${month}/${year}`;
  };

  if (offeringLoading || allProductServicesLoading) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Text>Loading supplier offering...</Text>
        </div>
      </Container>
    );
  }

  if (error || !offering) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Text className="text-ui-fg-error">
            {error?.message || "Supplier offering not found"}
          </Text>
          <Button
            variant="secondary"
            onClick={() => navigate("/supplier-management/supplier-offerings")}
            className="mt-4"
          >
            Back to Supplier Offerings
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />

      {/* Header with Back and Action Buttons */}
      <div className="flex flex-row items-center justify-between mb-4">
        <Button
          variant="transparent"
          onClick={() => navigate("/supplier-management/supplier-offerings")}
          className="p-1"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>

        <div className="flex items-center gap-2">
          {hasPermission("supplier_offerings:edit") && (
            <Button
              variant="secondary"
              onClick={() =>
                navigate(`/supplier-management/supplier-offerings/${id}/edit`)
              }
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
          {hasPermission("supplier_offerings:delete") && (
            <Button variant="danger" onClick={() => setShowDeleteModal(true)}>
              <Trash className="h-4 w-4" />
              Delete
            </Button>
          )}
        </div>
      </div>

      <Container className="p-0">
        {/* Page Header */}
        <div className="px-6 py-4">
          <div>
            <Heading level="h1">
              {resolveProductServiceName(
                offering.product_service,
                hotels,
                destinations
              )}{" "}
              by {offering.supplier?.name}
            </Heading>
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col gap-4 px-6 pb-6">
          <div className="space-y-8">
            {/* Basic Information */}
            <div className="border border-ui-border-base rounded-lg p-6">
              <div className="mb-6">
                <div className="flex items-start justify-between">
                  <div>
                    <Heading level="h3">Basic Information</Heading>
                    <Text className="text-ui-fg-subtle">
                      Core details, validity period, and status for this
                      supplier offering
                    </Text>
                  </div>
                  <Badge color={getStatusBadgeColor(offering.status)}>
                    {offering.status.charAt(0).toUpperCase() +
                      offering.status.slice(1)}
                  </Badge>
                </div>
              </div>

              <div className="space-y-4">
                {/* Supplier and Product/Service */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label>Supplier</Label>
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text weight="plus">{offering.supplier?.name}</Text>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Product/Service</Label>
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text weight="plus">
                        {resolveProductServiceName(
                          offering.product_service,
                          hotels,
                          destinations
                        )}
                      </Text>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge size="small">
                          {offering.product_service?.type}
                        </Badge>
                        <Badge size="small">
                          {offering.product_service?.category?.name}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Validity Period */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label>Active From</Label>
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text>{formatDate(offering.active_from)}</Text>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Active To</Label>
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text>{formatDate(offering.active_to)}</Text>
                    </div>
                  </div>
                </div>

                {/* Availability & Status */}
                <div className="space-y-6">
                  <div className="space-y-2">
                    <Label>Availability Notes</Label>
                    <div className="p-3 bg-ui-bg-subtle rounded-lg min-h-[80px]">
                      <Text>
                        {offering.availability_notes ||
                          "No availability notes provided"}
                      </Text>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2 mt-4">
                <Label>Currency</Label>
                <div className="p-3 bg-ui-bg-subtle rounded-lg">
                  <div className="flex items-center gap-2">
                    <Text weight="plus">{offering.currency || "CHF"}</Text>
                    {offering.currency_override && (
                      <Badge size="small">Override</Badge>
                    )}
                  </div>
                  {!offering.currency_override &&
                    offering.supplier?.default_currency && (
                      <Text size="small" className="text-ui-fg-subtle mt-1">
                        Inherited from supplier default
                      </Text>
                    )}
                </div>
              </div>
            </div>
          </div>
          {/* Enhanced Pricing */}
          <div className="border border-ui-border-base rounded-lg p-6">
            <PricingCalculator
              initialData={{
                commission: offering.commission, // Now comes as percentage from API
                grossPrice: offering.gross_price || offering.public_price, // Handle both old and new field names
                supplierPrice: offering.net_cost,
                marginRate: offering.margin_rate, // Now comes as percentage from API
                sellingPrice: offering.selling_price,

                // Currency fields
                currency: offering.currency,

                // Selling currency fields
                sellingCurrency: offering.selling_currency,
                sellingPriceSellingCurrency:
                  offering.selling_price_selling_currency,
                exchangeRate: offering.exchange_rate,
                exchangeRateDate: offering.exchange_rate_date
                  ? new Date(offering.exchange_rate_date)
                  : undefined,

                // Add-ons data
                addonLineItems: offering.add_ons || [],
              }}
              disabled={true}
              costCurrency={offering.currency || "CHF"}
              defaultSupplierId={offering.supplier_id}
            />
          </div>

          {/* Dynamic Custom Fields */}
          {categorySchema.length > 0 && (
            <div className="border border-ui-border-base rounded-lg p-6">
              <div className="mb-6">
                <Heading level="h3">Custom Fields</Heading>
                <Text className="text-ui-fg-subtle">
                  Category-specific fields for this offering
                </Text>
              </div>
              <div className="space-y-4">
                {categorySchema.map((field) => (
                  <div key={field.key} className="space-y-2">
                    <Label>{field.label}</Label>
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text>
                        {(() => {
                          // Priority 1: Check for API-resolved names with standard naming pattern
                          const namesField = `${field.key}_names`;
                          const resolvedNames =
                            offering.custom_fields?.[namesField];
                          // Use resolved names if available
                          if (
                            resolvedNames &&
                            Array.isArray(resolvedNames) &&
                            resolvedNames.length > 0
                          ) {
                            const validNames = resolvedNames.filter(
                              (name) =>
                                name && typeof name === "string" && name.trim()
                            );
                            if (validNames.length > 0) {
                              return validNames.join(", ");
                            }
                          }

                          // Fallback to formatCustomFieldValue with complete datasets for name resolution
                          return (
                            formatCustomFieldValue(
                              offering.custom_fields?.[field.key],
                              field,
                              {
                                hotels: allHotels,
                                destinations: allDestinations,
                                productServices: allProductServices,
                              }
                            ) || "Not set"
                          );
                        })()}
                      </Text>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Cost History */}
          {/* <div
            id="cost-history"
            className="border border-ui-border-base rounded-lg p-6"
          >
            <div className="mb-6">
              <Heading level="h3">Cost History</Heading>
              <Text className="text-ui-fg-subtle">
                Track of all cost and currency changes for this offering
              </Text>
            </div>

            <CostHistoryTable
              costHistory={costHistoryResponse?.cost_history || []}
              stats={costHistoryResponse?.stats}
              isLoading={costHistoryLoading}
              showSupplierOffering={false}
            />
          </div> */}

          {/* Audit Information */}
          {/* {!isEditing && (
              <div className="border border-ui-border-base rounded-lg p-6">
                <div className="mb-6">
                  <Heading level="h3">Audit Information</Heading>
                  <Text className="text-ui-fg-subtle">
                    Creation and modification details
                  </Text>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label>Created</Label>
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text>{formatDate(offering.created_at)}</Text>
                      {offering.created_by && (
                        <Text size="small" className="text-ui-fg-subtle">
                          by {offering.created_by}
                        </Text>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Last Updated</Label>
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text>{formatDate(offering.updated_at)}</Text>
                      {offering.updated_by && (
                        <Text size="small" className="text-ui-fg-subtle">
                          by {offering.updated_by}
                        </Text>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )} */}
        </div>
      </Container>

      {/* Delete Confirmation Prompt */}
      <Prompt open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Supplier Offering</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete this supplier offering? This
              action cannot be undone.
              {offering && (
                <div className="mt-2 text-sm font-medium">
                  {resolveProductServiceName(
                    offering.product_service,
                    hotels,
                    destinations
                  )}{" "}
                  by {offering.supplier?.name}
                </div>
              )}
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={() => setShowDeleteModal(false)}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={handleDelete}
              disabled={deleteSupplierOffering.isPending}
            >
              {deleteSupplierOffering.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Supplier Offering Details",
});

export default SupplierOfferingDetailPage;
