import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import { Container, <PERSON>ing, Button, Toaster, toast } from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";
import { useCreateProductService } from "../../../../hooks/supplier-products-services/use-products-services";
import ProductServiceForm, {
  type FormData,
} from "../../../../components/supplier-management/product-service-form";

const CreateProductServicePage = () => {
  const navigate = useNavigate();
  const createProductService = useCreateProductService();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (formData: FormData) => {
    setIsSubmitting(true);
    try {
      await createProductService.mutateAsync({
        // name is not sent as it will be auto-generated on the backend
        type: formData.type,
        description: formData.description,
        base_cost:
          formData.base_cost !== "" ? Number(formData.base_cost) : undefined,
        custom_fields:
          Object.keys(formData.custom_fields).length > 0
            ? formData.custom_fields
            : undefined,
        category_id: formData.category_id,
        unit_type_id: formData.unit_type_id,
        tag_ids: formData.tags,
        status: formData.status,
      });

      toast.success("Product/Service created successfully!");
      navigate("/supplier-management/products-services");
    } catch (error: any) {
      console.error("Error creating product/service:", error);
      // Error handling is now done in the hook
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate("/supplier-management/products-services");
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_products_services:create"
        fallback={
          <Container className="p-6">
            <div className="text-center">
              <Heading level="h2">Access Denied</Heading>
              <p className="text-ui-fg-subtle mt-2">
                You don't have permission to create products and services.
              </p>
              <Button
                onClick={() => navigate("/supplier-management/products-services")}
                className="mt-4"
              >
                Back to Products & Services
              </Button>
            </div>
          </Container>
        }
      >
        <Container className="divide-y p-0">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center gap-3">
              <Button
                variant="secondary"
                size="small"
                onClick={() => navigate("/supplier-management/products-services")}
              >
                <ArrowLeft className="w-4 h-4" />
                Back
              </Button>
              <Heading level="h1">Add New Product/Service</Heading>
            </div>
          </div>
        </Container>

        <ProductServiceForm
          mode="create"
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
        />
      </RoleGuard>

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Add Product/Service",
});

export default CreateProductServicePage;
