import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ShoppingCart, PlusMini, MagnifyingGlass } from "@camped-ai/icons";
import { Eye, Package, Calendar, Edit, MoreHorizontal } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Toaster,
  toast,
  DropdownMenu,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { ViewSupplierOrderModal } from "../../../components/supplier-management/orders/ViewSupplierOrderModal";
import { EditSupplierOrderModal } from "../../../components/supplier-management/orders/EditSupplierOrderModal";

import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { useRbac } from "../../../hooks/use-rbac";

interface SupplierOrder {
  id: string;
  order_number: string;
  supplier_name: string;
  supplier_id: string;
  order_type: string;
  status: string;
  total_amount: number;
  currency_code: string;
  subtotal: number;
  tax_amount: number;
  requested_delivery_date?: string;
  actual_delivery_date?: string;
  delivery_address?: string;
  notes?: string;
  internal_notes?: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  hotel_id?: string;
  booking_id?: string;
  items_count: number;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

const SupplierOrdersPage = () => {
  const { hasPermission } = useRbac();
  const [orders, setOrders] = useState<SupplierOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // Modal states
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      console.log("🔍 Fetching supplier orders from API...");

      // Build query parameters
      const params = new URLSearchParams();
      if (searchTerm) params.append("search", searchTerm);
      params.append("limit", "50");
      params.append("offset", "0");

      const url = `/admin/supplier-management/orders${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      const response = await fetch(url, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ Fetched ${data.orders.length} supplier orders`);

      setOrders(data.orders);
    } catch (error) {
      console.error("❌ Error fetching supplier orders:", error);
      toast.error("Failed to load supplier orders");
      // Set empty array on error
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    // TODO: Implement actual search functionality
  };

  // Modal handlers
  const handleViewOrder = (orderId: string) => {
    setSelectedOrderId(orderId);
    setViewModalOpen(true);
  };

  const handleEditOrder = (orderId: string) => {
    setSelectedOrderId(orderId);
    setEditModalOpen(true);
  };

  const handleCloseModals = () => {
    setViewModalOpen(false);
    setEditModalOpen(false);
    setSelectedOrderId(null);
    // Refresh orders list to get updated data
    fetchOrders();
  };

  const getOrderTypeBadgeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "product":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "service":
        return "bg-green-100 text-green-800 border-green-200";
      case "mixed":
        return "bg-purple-100 text-purple-800 border-purple-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "in_progress":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_orders:view"
        fallback={
          <Container className="p-6">
            <div className="text-center">
              <Heading level="h2">Access Denied</Heading>
              <Text className="text-ui-fg-subtle mt-2">
                You don't have permission to view supplier orders.
              </Text>
            </div>
          </Container>
        }
      >
        <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Supplier Orders</Heading>
            <Text className="text-ui-fg-subtle">
              Track and manage orders placed with suppliers
            </Text>
          </div>
        </div>

        {/* Search */}
        <div className="px-6 py-4">
          <div className="relative">
            <Input
              placeholder="Search orders by number, supplier, or status..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
        </div>

        {/* Orders Table */}
        <div className="overflow-x-auto">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Order</Table.HeaderCell>
                <Table.HeaderCell>Order Name</Table.HeaderCell>
                <Table.HeaderCell>Supplier</Table.HeaderCell>
                <Table.HeaderCell>Type</Table.HeaderCell>
                <Table.HeaderCell>Status</Table.HeaderCell>
                <Table.HeaderCell>Amount</Table.HeaderCell>
                <Table.HeaderCell>Order Date</Table.HeaderCell>
                <Table.HeaderCell>Delivery Date</Table.HeaderCell>
                <Table.HeaderCell>Items</Table.HeaderCell>
                <Table.HeaderCell>Actions</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {loading ? (
                <Table.Row>
                  <Table.Cell
                    className="text-center py-8"
                    style={{ gridColumn: "1 / -1" }}
                  >
                    Loading supplier orders...
                  </Table.Cell>
                </Table.Row>
              ) : orders.length === 0 ? (
                <Table.Row>
                  <Table.Cell
                    className="text-center py-8"
                    style={{ gridColumn: "1 / -1" }}
                  >
                    <div className="flex flex-col items-center gap-2">
                      <ShoppingCart className="h-12 w-12 text-gray-400" />
                      <Text>No supplier orders found</Text>
                      <Button
                        size="small"
                        onClick={() => {
                          toast.info("Create supplier order form coming soon");
                        }}
                      >
                        Create your first order
                      </Button>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ) : (
                orders.map((order) => (
                  <Table.Row key={order.id}>
                    {/* <Table.Cell>
                      <div>
                        <div className="font-medium">{order.order_number}</div>
                        <div className="text-sm text-ui-fg-subtle">
                          {order.id}
                        </div>
                      </div>
                    </Table.Cell> */}
                    <Table.Cell className="font-medium">
                      <div className="flex items-center gap-2">
                        <span className="truncate max-w-[80px]">
                          {order.id}
                        </span>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="font-medium">
                        {order.metadata?.order_name || order.order_number}
                      </div>
                      {order.metadata?.order_name && (
                        <div className="text-xs text-textSecondary">
                          {order.order_number}
                        </div>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="font-medium">{order.supplier_name}</div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge
                        className={`rounded-full ${getOrderTypeBadgeColor(
                          order.order_type
                        )}`}
                      >
                        {order.order_type}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge
                        className={`rounded-full ${getStatusBadgeColor(
                          order.status
                        )}`}
                      >
                        {order.status}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="font-medium">
                        {formatCurrency(
                          order.total_amount,
                          order.currency_code
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {formatDate(order.created_at)}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-1">
                        <Package className="h-3 w-3" />
                        {order.requested_delivery_date
                          ? formatDate(order.requested_delivery_date)
                          : "-"}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge>{order.items_count} items</Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <DropdownMenu>
                        <DropdownMenu.Trigger asChild>
                          <Button variant="transparent" size="small">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Item
                            onClick={() => handleViewOrder(order.id)}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenu.Item>
                          {hasPermission("supplier_orders:edit") && (
                            <DropdownMenu.Item
                              onClick={() => handleEditOrder(order.id)}
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Order
                            </DropdownMenu.Item>
                          )}
                        </DropdownMenu.Content>
                      </DropdownMenu>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      </Container>

      {/* Modals */}
      {selectedOrderId && (
        <>
          <ViewSupplierOrderModal
            isOpen={viewModalOpen}
            onClose={handleCloseModals}
            orderId={selectedOrderId}
          />
          <EditSupplierOrderModal
            isOpen={editModalOpen}
            onClose={handleCloseModals}
            orderId={selectedOrderId}
          />
        </>
      )}

      <Toaster />
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Orders",
});

export default SupplierOrdersPage;
