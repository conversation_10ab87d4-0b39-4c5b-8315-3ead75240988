/**
 * Configuration for sidebar navigation items
 *
 * This file contains the configuration for which sidebar items should be hidden.
 * Modify the HIDDEN_SIDEBAR_ITEMS array to control which items are hidden.
 */

/**
 * Array of sidebar item labels to hide (case-insensitive)
 *
 * Examples:
 * - "Products"
 * - "Customers"
 * - "Orders"
 * - "Settings"
 *
 * You can also use partial matches, which will hide any item containing the text.
 * For example, "Product" will hide both "Products" and "Product Categories".
 */
export const HIDDEN_SIDEBAR_ITEMS: string[] = [
  // Main navigation items to hide
  "Products",
  "Customers",
  "Price Lists",
  "Promotions",
  "Inventory", // Removed - now using custom Inventory menu
  "Orders",
  "Search",
  // Analytics items to hide
  "User Analytics",
  "Store Analytics",
  // Settings sidebar items to hide
  "Return Reasons",
  "Sales Channels",
  "Product Types",
  "Product Tags",
  "Users",
  "Locations & Shipping",
  // Developer-related items
  "Publishable API Keys",
  "Secret API Keys",
  "Workflows",
  "Webhooks", // If present
  // Vendor Management items (replaced by Supplier Management)
  "Vendor Management",
  "Create Vendor",
  "Vendors",
  "Vendor",
];

/**
 * Array of sidebar items that should NOT be hidden even if they contain text from HIDDEN_SIDEBAR_ITEMS
 *
 * This array contains items that should remain visible even if they contain words
 * that are in the HIDDEN_SIDEBAR_ITEMS array.
 *
 * Example: If "Customers" is hidden but you want "All Customers" to remain visible,
 * add "All Customers" to this array.
 */
export const EXCEPTION_ITEMS: string[] = ["All Customers"];
