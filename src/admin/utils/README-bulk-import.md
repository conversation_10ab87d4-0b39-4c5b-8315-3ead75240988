# Bulk Import Implementation for Hotel Pricing

## 🎯 **Overview**

This implementation provides a **frontend-only bulk import solution** that reuses existing APIs instead of creating new parse/import endpoints. The solution parses Excel files in the browser and transforms the data to match the existing bulk pricing API structure.

## 🏗️ **Architecture**

### **Key Design Decisions:**
1. **Frontend Excel Parsing**: Uses `xlsx` package to parse Excel files in the browser
2. **API Reuse**: Leverages existing bulk pricing APIs instead of creating new endpoints
3. **Data Transformation**: Converts Excel data to match existing API JSON structure
4. **Validation**: Frontend validation with detailed error reporting

### **Components:**

#### **1. BulkImportModal Component**
- **Location**: `src/admin/components/hotel/pricing/bulk-import-modal.tsx`
- **Purpose**: Main UI component for bulk import functionality
- **Features**:
  - Excel/CSV file upload with drag-and-drop
  - Real-time parsing and validation
  - Progress tracking during import
  - Error reporting with row-level details
  - Success/failure feedback

#### **2. Data Transformer Utility**
- **Location**: `src/admin/utils/bulk-import-transformer.ts`
- **Purpose**: Transform Excel data to API format
- **Functions**:
  - `transformExcelDataToApiFormat()`: Main transformation function
  - `executeBulkImport()`: Execute API calls using existing endpoints
  - `validateExcelData()`: Validate Excel data before transformation

## 📊 **Excel File Format**

### **Required Columns:**
- `room_config_name`: Name of the room configuration
- `occupancy_name`: Name of the occupancy configuration  
- `meal_plan_name`: Name of the meal plan
- `currency_code`: Currency code (must match current currency)

### **Optional Columns:**

#### **Pricing Type:**
- `pricing_type`: "Base Pricing" or "Seasonal Pricing"

#### **Seasonal Pricing (if applicable):**
- `seasonal_period`: Name of the seasonal period
- `seasonal_start_date`: Start date (YYYY-MM-DD format)
- `seasonal_end_date`: End date (YYYY-MM-DD format)

#### **Weekday Prices:**
- `monday_price`, `tuesday_price`, `wednesday_price`, `thursday_price`
- `friday_price`, `saturday_price`, `sunday_price`

#### **Default Cost/Margin Values:**
- `default_gross_cost`: Default gross cost
- `default_fixed_margin`: Default fixed margin
- `default_margin_percentage`: Default margin percentage

#### **Weekday-Specific Cost/Margin:**
- `monday_gross_cost`, `monday_fixed_margin`, `monday_margin_percentage`
- `tuesday_gross_cost`, `tuesday_fixed_margin`, `tuesday_margin_percentage`
- ... (same pattern for all weekdays)

## 🔄 **Data Flow**

### **1. File Upload & Parsing**
```typescript
// User uploads Excel file
const buffer = await file.arrayBuffer();
const workbook = XLSX.read(buffer, { type: 'array' });
const worksheet = workbook.Sheets[sheetName];
const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
```

### **2. Data Validation**
```typescript
// Validate required columns and data integrity
const validation = validateExcelData(data, roomConfigs, occupancyConfigs, mealPlans);
if (!validation.isValid) {
  throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
}
```

### **3. Data Transformation**
```typescript
// Transform to API format
const transformedData = transformExcelDataToApiFormat(
  data, roomConfigs, occupancyConfigs, mealPlans, seasonalPeriods
);

// Result structure:
{
  baseRules: {
    "room_config_id": {
      currency_code: "GBP",
      weekday_rules: [
        {
          occupancy_type_id: "occ_123",
          meal_plan_id: "mp_456",
          default_values: { gross_cost: 100, fixed_margin: 20, margin_percentage: 15, total: 135 },
          weekday_prices: { mon: 150, tue: 150, ... },
          weekday_values: { mon: { gross_cost: 100, fixed_margin: 20, margin_percentage: 15 }, ... }
        }
      ]
    }
  },
  seasonalRules: {
    "room_config_id_season_name": {
      currency_code: "GBP",
      name: "Summer Season",
      start_date: "2024-06-01",
      end_date: "2024-08-31",
      weekday_rules: [ /* same structure as base rules */ ]
    }
  }
}
```

### **4. API Execution**
```typescript
// Execute bulk import using existing APIs
for (const [roomConfigId, payload] of Object.entries(baseRules)) {
  await fetch(`/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing/bulk`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload)
  });
}

for (const [seasonalKey, payload] of Object.entries(seasonalRules)) {
  const roomConfigId = seasonalKey.split('_')[0];
  await fetch(`/admin/hotel-management/room-configs/${roomConfigId}/seasonal-pricing/bulk`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload)
  });
}
```

## 🔌 **API Integration**

### **Existing APIs Used:**

#### **Base Pricing API:**
- **Endpoint**: `POST /admin/hotel-management/room-configs/{id}/weekday-pricing/bulk`
- **Purpose**: Bulk create/update base pricing rules
- **Payload**: `{ currency_code, weekday_rules[] }`

#### **Seasonal Pricing API:**
- **Endpoint**: `POST /admin/hotel-management/room-configs/{id}/seasonal-pricing/bulk`
- **Purpose**: Bulk create/update seasonal pricing rules  
- **Payload**: `{ currency_code, name, start_date, end_date, weekday_rules[] }`

### **No New APIs Required:**
- ✅ Reuses existing bulk pricing endpoints
- ✅ Maintains compatibility with current data models
- ✅ No backend changes needed

## 🎨 **UI Integration**

### **Button Placement:**
- Added "Bulk Import" button next to Export button in comprehensive pricing table
- Only visible in edit mode (not read-only mode)
- Disabled during loading states

### **Modal Features:**
- **File Upload**: Drag-and-drop or click to select Excel/CSV files
- **Real-time Validation**: Shows validation errors as user uploads file
- **Progress Tracking**: Visual progress bar during import process
- **Error Reporting**: Detailed error messages with row numbers
- **Success Feedback**: Confirmation of successful imports

## ✅ **Validation & Error Handling**

### **Frontend Validation:**
1. **File Format**: Ensures Excel/CSV format
2. **Required Columns**: Validates presence of required columns
3. **Data Integrity**: Checks for valid room configs, occupancy configs, meal plans
4. **Currency Matching**: Ensures currency matches current currency
5. **Pricing Data**: Validates that either prices or cost/margin data exists

### **Error Reporting:**
- Row-level error messages
- Field-specific validation errors
- Clear error descriptions for users
- Prevents import if validation fails

## 🚀 **Usage Instructions**

### **For Users:**
1. Click "Bulk Import" button in pricing table
2. Upload Excel file with pricing data
3. Review validation results
4. Click "Import" to execute bulk import
5. Monitor progress and review results

### **For Developers:**
1. Import `BulkImportModal` component
2. Pass required props (hotelId, currency, configs, etc.)
3. Handle `onImportComplete` callback for data refresh
4. Customize validation rules in transformer utility

## 🔧 **Configuration**

### **Supported File Formats:**
- Excel (.xlsx, .xls)
- CSV (.csv)

### **Batch Processing:**
- Processes all room configs in parallel
- Progress tracking for user feedback
- Error isolation (one failure doesn't stop others)

### **Memory Efficiency:**
- Frontend parsing with streaming for large files
- Minimal memory footprint
- Garbage collection friendly

## 📈 **Benefits**

### **1. No Backend Changes:**
- Reuses existing APIs
- No new endpoints required
- Maintains current data models

### **2. Better User Experience:**
- Real-time validation feedback
- Progress tracking
- Detailed error reporting
- Familiar Excel format

### **3. Maintainability:**
- Single source of truth for API structure
- Consistent with existing save functionality
- Easy to extend for new fields

### **4. Performance:**
- Frontend parsing reduces server load
- Parallel API calls for efficiency
- Optimized for large datasets

## 🔮 **Future Enhancements**

### **Potential Improvements:**
1. **Template Generation**: Auto-generate Excel templates
2. **Data Preview**: Show parsed data before import
3. **Partial Import**: Allow importing subset of valid rows
4. **Import History**: Track import operations
5. **Advanced Validation**: Custom validation rules
6. **Bulk Updates**: Support for updating existing data

### **Extensibility:**
- Easy to add new column types
- Configurable validation rules
- Pluggable transformation logic
- Support for additional file formats
