# Hotel Pricing Export Utility

A standalone utility for exporting hotel pricing data to Excel (.xlsx) and CSV formats. This utility is completely independent and can be imported into any component that needs pricing export functionality.

## Files

- `pricing-export-utils.ts` - Core export utility functions
- `pricing-export-example.tsx` - Example React components showing usage
- `README-pricing-export.md` - This documentation file

## Features

- **API-based data fetching** - Fetches pricing data directly from backend API endpoints
- **Multiple export formats** - Supports both Excel (.xlsx) and CSV formats
- **Comprehensive data export** - Includes base pricing, seasonal pricing, cost/margin data
- **Configurable options** - Control what data to include in exports
- **Error handling** - Proper error handling for API calls and file generation
- **TypeScript support** - Fully typed interfaces and functions
- **Browser download** - Automatic file download functionality

## Basic Usage

### 1. Simple Export Function

```typescript
import { exportHotelPricingData, downloadFile } from '../utils/pricing-export-utils';

// Export hotel pricing data
const handleExport = async () => {
  const result = await exportHotelPricingData('hotel_123', {
    format: 'excel',
    includeSeasonalPricing: true,
    includeCostMarginData: true,
    currency: 'CHF'
  });

  if (result.success && result.data) {
    downloadFile(result.data as Blob, result.filename);
  }
};
```

### 2. Using React Components

```typescript
import { PricingExportButtons, SimpleExportButton } from '../utils/pricing-export-example';

// Full export interface with options
<PricingExportButtons 
  hotelId="hotel_123"
  currentCurrency="CHF"
  disabled={false}
/>

// Simple export button
<SimpleExportButton 
  hotelId="hotel_123"
  currentCurrency="CHF"
  format="excel"
/>
```

### 3. Using the Hook

```typescript
import { usePricingExport } from '../utils/pricing-export-example';

const MyComponent = () => {
  const { exportPricingData, isExporting } = usePricingExport();

  const handleExport = () => {
    exportPricingData('hotel_123', {
      format: 'csv',
      includeSeasonalPricing: true,
      includeCostMarginData: false,
      currency: 'EUR'
    });
  };

  return (
    <button onClick={handleExport} disabled={isExporting}>
      {isExporting ? 'Exporting...' : 'Export CSV'}
    </button>
  );
};
```

## API Reference

### Types

```typescript
interface ExportOptions {
  format: 'excel' | 'csv';
  includeSeasonalPricing?: boolean;
  includeCostMarginData?: boolean;
  currency?: string;
}

interface ExportResult {
  success: boolean;
  filename: string;
  data?: Blob | string;
  error?: string;
}
```

### Core Functions

#### `fetchPricingData(hotelId: string, currency?: string)`
Fetches comprehensive pricing data from the API.

#### `transformPricingDataForExport(pricingData, options)`
Transforms API response into flat export format.

#### `generateCSV(exportData: any[])`
Generates CSV content from export data.

#### `generateExcel(exportData: any[], hotelName: string, options: ExportOptions)`
Generates Excel workbook from export data.

#### `exportHotelPricingData(hotelId: string, options: ExportOptions)`
Main export function that handles the complete export process.

#### `downloadFile(blob: Blob, filename: string)`
Triggers file download in browser.

## Export Data Structure

The exported data includes the following columns:

### Basic Information
- Hotel Name, ID
- Room Configuration Name, ID, Type
- Max Occupancy, Max Cots
- Occupancy Configuration (Adults, Children, Infants)
- Meal Plan Name, Type
- Pricing Type (Base/Seasonal)
- Currency Code, Priority

### Seasonal Information (if included)
- Seasonal Period Name
- Start Date, End Date

### Pricing Data
- Monday through Sunday Prices

### Cost/Margin Data (if included)
- Default Gross Cost, Fixed Margin, Margin Percentage
- Monday through Sunday: Gross Cost, Fixed Margin, Margin Percentage

## Integration with Comprehensive Pricing Table

To integrate with the existing comprehensive pricing table:

```typescript
// In comprehensive-pricing-table.tsx
import { SimpleExportButton } from '../../../utils/pricing-export-example';

// Add to the component's action buttons section
<div className="flex gap-2">
  {/* Existing buttons */}
  <SimpleExportButton 
    hotelId={hotelId}
    currentCurrency={currencyCode}
    format="excel"
    disabled={isLoading}
  />
  <SimpleExportButton 
    hotelId={hotelId}
    currentCurrency={currencyCode}
    format="csv"
    disabled={isLoading}
  />
</div>
```

## Error Handling

The utility includes comprehensive error handling:

- API fetch errors
- Data transformation errors
- File generation errors
- User-friendly error messages via toast notifications

## Dependencies

- `exceljs` - Excel file generation
- `@camped-ai/framework/utils` - SDK for API calls
- `@camped-ai/ui` - UI components and toast notifications
- `lucide-react` - Icons

## File Naming Convention

Generated files follow this naming pattern:
- Excel: `hotel_pricing_{hotelName}_{currency}_{date}.xlsx`
- CSV: `hotel_pricing_{hotelName}_{currency}_{date}.csv`

Where:
- `{hotelName}` is sanitized (special characters replaced with underscores)
- `{currency}` is the selected currency code
- `{date}` is in YYYY-MM-DD format

## Browser Compatibility

The utility uses modern browser APIs:
- `Blob` for file creation
- `URL.createObjectURL` for download links
- Automatic cleanup of object URLs

Supported in all modern browsers (Chrome, Firefox, Safari, Edge).
