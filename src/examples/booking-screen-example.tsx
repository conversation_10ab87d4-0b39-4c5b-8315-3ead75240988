import React, { useEffect, useState } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { 
  bookingScreenLoader, 
  hotelBookingsLoader, 
  customerBookingsLoader,
  BookingScreenFilters,
  BookingScreenResponse 
} from '../loaders/booking-screen-loader'
import { useBookings } from '../hooks/api/bookings'

// Example component showing how to use the booking screen loader
export const BookingScreenExample: React.FC = () => {
  const queryClient = useQueryClient()
  const [filters, setFilters] = useState<BookingScreenFilters>({
    limit: 20,
    offset: 0,
    sort_by: 'created_at',
    sort_order: 'desc'
  })

  // Method 1: Using the loader directly with useQuery
  const {
    data: bookingData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['booking-screen', filters],
    queryFn: () => bookingScreenLoader(queryClient)(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  })

  // Method 2: Using the existing useBookings hook (alternative approach)
  const {
    data: alternativeData,
    isLoading: isAlternativeLoading
  } = useBookings(filters)

  // Example of loading data on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Pre-load data using the loader
        await bookingScreenLoader(queryClient)(filters)
        console.log('Initial booking data loaded')
      } catch (error) {
        console.error('Failed to load initial booking data:', error)
      }
    }

    loadInitialData()
  }, [queryClient])

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<BookingScreenFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  // Handle pagination
  const handlePageChange = (page: number) => {
    const offset = (page - 1) * (filters.limit || 20)
    handleFilterChange({ offset, page })
  }

  // Handle hotel filter
  const handleHotelFilter = async (hotelId: string) => {
    try {
      // Use hotel-specific loader
      const hotelBookings = await hotelBookingsLoader(queryClient)(hotelId, {
        limit: filters.limit,
        offset: 0
      })
      console.log('Hotel bookings loaded:', hotelBookings)
      
      // Update filters to show hotel bookings
      handleFilterChange({ hotel_id: hotelId, offset: 0 })
    } catch (error) {
      console.error('Failed to load hotel bookings:', error)
    }
  }

  // Handle customer filter
  const handleCustomerFilter = async (customerId: string) => {
    try {
      // Use customer-specific loader
      const customerBookings = await customerBookingsLoader(queryClient)(customerId, {
        limit: filters.limit,
        offset: 0
      })
      console.log('Customer bookings loaded:', customerBookings)
      
      // Update filters to show customer bookings
      handleFilterChange({ customer_id: customerId, offset: 0 })
    } catch (error) {
      console.error('Failed to load customer bookings:', error)
    }
  }

  // Refresh data
  const handleRefresh = async () => {
    try {
      await refetch()
      console.log('Booking data refreshed')
    } catch (error) {
      console.error('Failed to refresh booking data:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-lg">Loading bookings...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-red-600">
          Error loading bookings: {error instanceof Error ? error.message : 'Unknown error'}
        </div>
      </div>
    )
  }

  const { bookings, count, summary } = bookingData || { bookings: [], count: 0 }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Booking Management</h1>
        <button
          onClick={handleRefresh}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Refresh
        </button>
      </div>

      {/* Summary Statistics */}
      {summary && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Total Bookings</div>
            <div className="text-2xl font-bold">{summary.total_bookings}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Confirmed</div>
            <div className="text-2xl font-bold text-green-600">{summary.confirmed_bookings}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Pending</div>
            <div className="text-2xl font-bold text-yellow-600">{summary.pending_bookings}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Cancelled</div>
            <div className="text-2xl font-bold text-red-600">{summary.cancelled_bookings}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Total Revenue</div>
            <div className="text-2xl font-bold">${summary.total_revenue.toFixed(2)}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Avg. Booking Value</div>
            <div className="text-2xl font-bold">${summary.average_booking_value.toFixed(2)}</div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filters.status || ''}
              onChange={(e) => handleFilterChange({ status: e.target.value || undefined })}
              className="w-full border border-gray-300 rounded px-3 py-2"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
              <option value="canceled">Canceled</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Status
            </label>
            <select
              value={filters.payment_status || ''}
              onChange={(e) => handleFilterChange({ payment_status: e.target.value || undefined })}
              className="w-full border border-gray-300 rounded px-3 py-2"
            >
              <option value="">All Payment Statuses</option>
              <option value="pending">Pending</option>
              <option value="captured">Captured</option>
              <option value="canceled">Canceled</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sort By
            </label>
            <select
              value={filters.sort_by || 'created_at'}
              onChange={(e) => handleFilterChange({ sort_by: e.target.value })}
              className="w-full border border-gray-300 rounded px-3 py-2"
            >
              <option value="created_at">Created Date</option>
              <option value="updated_at">Updated Date</option>
              <option value="total">Total Amount</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sort Order
            </label>
            <select
              value={filters.sort_order || 'desc'}
              onChange={(e) => handleFilterChange({ sort_order: e.target.value as 'asc' | 'desc' })}
              className="w-full border border-gray-300 rounded px-3 py-2"
            >
              <option value="desc">Newest First</option>
              <option value="asc">Oldest First</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bookings List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Booking ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Guest
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Hotel & Room
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Dates
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {bookings.map((booking) => (
                <tr key={booking.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    #{booking.display_id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {booking.metadata?.guest_name || booking.email}
                    </div>
                    <div className="text-sm text-gray-500">{booking.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {booking.metadata?.hotel_name || 'Unknown Hotel'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {booking.room_type} - Room {booking.metadata?.room_number}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {booking.check_in_formatted} - {booking.check_out_formatted}
                    </div>
                    <div className="text-sm text-gray-500">
                      {booking.nights} night{booking.nights !== 1 ? 's' : ''}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      booking.status === 'completed' 
                        ? 'bg-green-100 text-green-800'
                        : booking.status === 'pending'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {booking.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {booking.currency_code} {booking.total.toFixed(2)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-700">
          Showing {(filters.offset || 0) + 1} to {Math.min((filters.offset || 0) + (filters.limit || 20), count)} of {count} results
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => handlePageChange((filters.page || 1) - 1)}
            disabled={(filters.page || 1) <= 1}
            className="px-3 py-1 border border-gray-300 rounded disabled:opacity-50"
          >
            Previous
          </button>
          <button
            onClick={() => handlePageChange((filters.page || 1) + 1)}
            disabled={(filters.offset || 0) + (filters.limit || 20) >= count}
            className="px-3 py-1 border border-gray-300 rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  )
}

export default BookingScreenExample
