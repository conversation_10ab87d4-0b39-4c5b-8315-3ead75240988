# Booking Screen Loader

This document explains how to use the booking screen loader, which provides optimized data loading for booking management screens.

## Overview

The booking screen loader follows the same pattern as the products loader, providing:
- Efficient data caching with React Query
- Pre-loading capabilities for better performance
- Consistent query key management
- Type-safe interfaces
- Multiple loading strategies for different use cases

## Files Structure

```
src/
├── hooks/api/
│   ├── bookings.tsx          # React Query hooks for bookings
│   └── index.tsx             # Export all API hooks
├── loaders/
│   ├── booking-screen-loader.ts  # Main booking screen loader
│   └── README.md             # This documentation
└── examples/
    └── booking-screen-example.tsx  # Usage example
```

## Basic Usage

### 1. Import the Loader

```typescript
import { bookingScreenLoader } from '../loaders/booking-screen-loader'
import { useQueryClient } from '@tanstack/react-query'
```

### 2. Use in React Component

```typescript
const MyBookingScreen = () => {
  const queryClient = useQueryClient()
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['booking-screen', filters],
    queryFn: () => bookingScreenLoader(queryClient)(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
  
  // Use the data...
}
```

### 3. Pre-loading Data

```typescript
// Pre-load data before component mounts
useEffect(() => {
  const preloadData = async () => {
    await bookingScreenLoader(queryClient)({
      limit: 20,
      hotel_id: 'hotel-123'
    })
  }
  preloadData()
}, [])
```

## Available Loaders

### 1. `bookingScreenLoader`
Main loader for general booking screen data.

```typescript
const data = await bookingScreenLoader(queryClient)({
  limit: 20,
  offset: 0,
  status: 'completed',
  sort_by: 'created_at',
  sort_order: 'desc'
})
```

### 2. `hotelBookingsLoader`
Specialized loader for hotel-specific bookings.

```typescript
const hotelBookings = await hotelBookingsLoader(queryClient)('hotel-123', {
  limit: 50,
  status: 'confirmed'
})
```

### 3. `customerBookingsLoader`
Specialized loader for customer-specific bookings.

```typescript
const customerBookings = await customerBookingsLoader(queryClient)('customer-456', {
  limit: 10,
  sort_by: 'created_at'
})
```

### 4. `realtimeBookingScreenLoader`
Loader that always fetches fresh data for real-time scenarios.

```typescript
const freshData = await realtimeBookingScreenLoader(queryClient)({
  hotel_id: 'hotel-123'
})
```

## Filter Options

```typescript
interface BookingScreenFilters {
  limit?: number              // Number of results (default: 20)
  offset?: number            // Pagination offset (default: 0)
  hotel_id?: string          // Filter by specific hotel
  status?: string            // Booking status filter
  payment_status?: string    // Payment status filter
  booking_status?: string    // Booking-specific status
  sort_by?: string          // Sort field (default: 'created_at')
  sort_order?: 'asc' | 'desc' // Sort direction (default: 'desc')
  customer_id?: string       // Filter by customer
  page?: number             // Page number for pagination
}
```

## Response Data Structure

```typescript
interface BookingScreenResponse {
  bookings: BookingScreenData[]
  count: number
  limit: number
  offset: number
  summary?: {
    total_bookings: number
    confirmed_bookings: number
    pending_bookings: number
    cancelled_bookings: number
    total_revenue: number
    average_booking_value: number
  }
}

interface BookingScreenData {
  id: string
  display_id: string
  customer_id: string
  email: string
  status: string
  payment_status: string
  total: number
  currency_code: string
  created_at: string
  updated_at: string
  metadata?: {
    hotel_name?: string
    room_config_name?: string
    room_number?: string
    check_in_date?: string
    check_out_date?: string
    guest_name?: string
    // ... more metadata fields
  }
  // Computed fields for UI
  nights?: number
  guest_count?: number
  room_type?: string
  check_in_formatted?: string
  check_out_formatted?: string
}
```

## Advanced Usage

### Combining with React Query Hooks

You can also use the traditional React Query hooks from `hooks/api/bookings.tsx`:

```typescript
import { useBookings, useBooking } from '../hooks/api/bookings'

// List bookings
const { data: bookings } = useBookings({
  hotel_id: 'hotel-123',
  limit: 20
})

// Single booking
const { data: booking } = useBooking('booking-456')
```

### Mutation Hooks

```typescript
import { 
  useCreateBookingReservation,
  useConfirmBooking,
  useUpdateBookingStatus,
  useProcessBookingPayment
} from '../hooks/api/bookings'

// Create reservation
const createReservation = useCreateBookingReservation()

// Confirm booking
const confirmBooking = useConfirmBooking()

// Update status
const updateStatus = useUpdateBookingStatus('booking-123')

// Process payment
const processPayment = useProcessBookingPayment()
```

### Error Handling

```typescript
const { data, isLoading, error } = useQuery({
  queryKey: ['booking-screen', filters],
  queryFn: () => bookingScreenLoader(queryClient)(filters),
  onError: (error) => {
    console.error('Failed to load bookings:', error)
    // Handle error (show toast, etc.)
  }
})
```

### Cache Management

```typescript
// Invalidate cache after mutations
const queryClient = useQueryClient()

// Invalidate all booking queries
queryClient.invalidateQueries({ queryKey: ['bookings'] })

// Invalidate specific booking
queryClient.invalidateQueries({ queryKey: ['bookings', 'detail', 'booking-123'] })

// Pre-fetch next page
queryClient.prefetchQuery({
  queryKey: ['booking-screen', { ...filters, offset: filters.offset + 20 }],
  queryFn: () => bookingScreenLoader(queryClient)({ ...filters, offset: filters.offset + 20 })
})
```

## Performance Tips

1. **Use appropriate stale times**: Set longer stale times for data that doesn't change frequently
2. **Pre-load critical data**: Use loaders in route loaders or useEffect for better UX
3. **Implement pagination**: Use offset/limit for large datasets
4. **Cache invalidation**: Invalidate relevant queries after mutations
5. **Real-time updates**: Use the realtime loader sparingly for critical updates

## Integration with Router

If using React Router, you can use the loader in route definitions:

```typescript
// In your router configuration
{
  path: "/bookings",
  element: <BookingScreen />,
  loader: async () => {
    return await bookingScreenLoader(queryClient)({
      limit: 20,
      sort_by: 'created_at'
    })
  }
}
```

## Example Implementation

See `src/examples/booking-screen-example.tsx` for a complete implementation example showing:
- Data loading with loaders
- Filter management
- Pagination
- Summary statistics
- Error handling
- Real-time updates
