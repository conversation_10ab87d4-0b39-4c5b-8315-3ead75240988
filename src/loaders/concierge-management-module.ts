import { MedusaContainer } from "@camped-ai/framework/types";
import { asClass } from "awilix";
import { CONCIERGE_MANAGEMENT_MODULE, ITINERARY_SERVICE } from "../modules/concierge-management";
import ConciergeManagementService from "../modules/concierge-management/service";
import ItineraryService from "../modules/concierge-management/itinerary-service";

/**
 * Register the Concierge Management Module and its services in the dependency injection container
 * This ensures all concierge-related services are available even if the module registration fails
 */
export default async (container: MedusaContainer): Promise<void> => {
  try {
    console.log("🚀 Starting Concierge Management Module registration...");

    // Register the main concierge management module
    if (!container.hasRegistration(CONCIERGE_MANAGEMENT_MODULE)) {
      container.register({
        [CONCIERGE_MANAGEMENT_MODULE]: {
          resolve: () => new ConciergeManagementService(container),
        },
      });
      console.log("✅ Concierge Management Module registered successfully");
    } else {
      console.log("ℹ️ Concierge Management Module already registered");
    }

    // Register the itinerary service
    if (!container.hasRegistration(ITINERARY_SERVICE)) {
      container.register({
        [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
      });
      console.log("✅ Itinerary Service registered successfully");
    } else {
      console.log("ℹ️ Itinerary Service already registered");
    }

    // Also register with the exact string for backward compatibility
    if (!container.hasRegistration("itineraryService")) {
      container.register({
        itineraryService: asClass(ItineraryService).singleton(),
      });
      console.log("✅ Itinerary Service registered with string key");
    } else {
      console.log("ℹ️ Itinerary Service with string key already registered");
    }

    // Register the concierge management service with string key for backward compatibility
    if (!container.hasRegistration("conciergeManagementService")) {
      container.register({
        conciergeManagementService: {
          resolve: () => new ConciergeManagementService(container),
        },
      });
      console.log("✅ Concierge Management Service registered with string key");
    } else {
      console.log("ℹ️ Concierge Management Service with string key already registered");
    }

  } catch (error) {
    console.error("❌ Failed to register Concierge Management Module:", error);
    throw error;
  }
};
