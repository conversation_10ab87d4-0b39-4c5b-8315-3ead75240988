/**
 * Utility functions for calculating prices from cost and margin data
 * in the hotel pricing system
 */

export type DayOfWeek = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';

export interface CostMarginData {
  gross_cost?: number | null;
  fixed_margin?: number | null;
  margin_percentage?: number | null;
}

export interface DefaultCostMarginData {
  default_gross_cost?: number | null;
  default_fixed_margin?: number | null;
  default_margin_percentage?: number | null;
}

export interface BasePriceRuleData extends DefaultCostMarginData {
  amount?: number | null;
  
  // Day-specific price fields (legacy)
  monday_price?: number | null;
  tuesday_price?: number | null;
  wednesday_price?: number | null;
  thursday_price?: number | null;
  friday_price?: number | null;
  saturday_price?: number | null;
  sunday_price?: number | null;

  // Day-specific cost and margin fields
  monday_gross_cost?: number | null;
  monday_fixed_margin?: number | null;
  monday_margin_percentage?: number | null;
  
  tuesday_gross_cost?: number | null;
  tuesday_fixed_margin?: number | null;
  tuesday_margin_percentage?: number | null;
  
  wednesday_gross_cost?: number | null;
  wednesday_fixed_margin?: number | null;
  wednesday_margin_percentage?: number | null;
  
  thursday_gross_cost?: number | null;
  thursday_fixed_margin?: number | null;
  thursday_margin_percentage?: number | null;
  
  friday_gross_cost?: number | null;
  friday_fixed_margin?: number | null;
  friday_margin_percentage?: number | null;
  
  saturday_gross_cost?: number | null;
  saturday_fixed_margin?: number | null;
  saturday_margin_percentage?: number | null;
  
  sunday_gross_cost?: number | null;
  sunday_fixed_margin?: number | null;
  sunday_margin_percentage?: number | null;
}

/**
 * Calculate total price from cost and margin data
 * @param costMarginData - Object containing gross_cost, fixed_margin, and margin_percentage
 * @returns Calculated total price in currency units, or null if insufficient data
 */
export function calculateTotalFromCostMargin(costMarginData: CostMarginData): number | null {
  const { gross_cost, fixed_margin, margin_percentage } = costMarginData;

  // Need at least gross cost to calculate
  if (gross_cost === null || gross_cost === undefined) {
    return null;
  }

  let total = gross_cost;

  // Add fixed margin if provided
  if (fixed_margin !== null && fixed_margin !== undefined) {
    total += fixed_margin;
  }

  // Apply percentage margin if provided
  if (margin_percentage !== null && margin_percentage !== undefined) {
    total += (gross_cost * margin_percentage) / 100;
  }

  return total;
}

/**
 * Calculate default total from default cost and margin values
 * @param data - Object containing default cost and margin fields
 * @returns Calculated default total in currency units, or null if insufficient data
 */
export function calculateDefaultTotal(data: DefaultCostMarginData): number | null {
  return calculateTotalFromCostMargin({
    gross_cost: data.default_gross_cost,
    fixed_margin: data.default_fixed_margin,
    margin_percentage: data.default_margin_percentage
  });
}

/**
 * Calculate total price for a specific day from cost and margin data
 * @param data - Base price rule data containing day-specific fields
 * @param day - Day of the week
 * @returns Calculated total price in currency units, or null if insufficient data
 */
export function calculateDayTotal(data: BasePriceRuleData, day: DayOfWeek): number | null {
  const grossCostField = `${day}_gross_cost` as keyof BasePriceRuleData;
  const fixedMarginField = `${day}_fixed_margin` as keyof BasePriceRuleData;
  const marginPercentageField = `${day}_margin_percentage` as keyof BasePriceRuleData;

  return calculateTotalFromCostMargin({
    gross_cost: data[grossCostField] as number | null,
    fixed_margin: data[fixedMarginField] as number | null,
    margin_percentage: data[marginPercentageField] as number | null
  });
}

/**
 * Get effective price for a specific day, considering cost/margin calculation,
 * day-specific price, or fallback to base amount
 * @param data - Base price rule data
 * @param day - Day of the week
 * @returns Effective price in currency units
 */
export function getEffectivePrice(data: BasePriceRuleData, day: DayOfWeek): number {
  // First try to calculate from cost and margin
  const calculatedTotal = calculateDayTotal(data, day);
  if (calculatedTotal !== null) {
    return calculatedTotal;
  }

  // Fall back to day-specific price
  const dayPriceField = `${day}_price` as keyof BasePriceRuleData;
  const dayPrice = data[dayPriceField] as number | null;
  if (dayPrice !== null && dayPrice !== undefined) {
    return dayPrice;
  }

  // Fall back to base amount
  return data.amount || 0;
}

/**
 * Check if cost and margin data is available for a specific day
 * @param data - Base price rule data
 * @param day - Day of the week
 * @returns True if cost data is available for the day
 */
export function hasCostDataForDay(data: BasePriceRuleData, day: DayOfWeek): boolean {
  const grossCostField = `${day}_gross_cost` as keyof BasePriceRuleData;
  const grossCost = data[grossCostField] as number | null;
  return grossCost !== null && grossCost !== undefined;
}

/**
 * Check if default cost and margin data is available
 * @param data - Base price rule data
 * @returns True if default cost data is available
 */
export function hasDefaultCostData(data: DefaultCostMarginData): boolean {
  return data.default_gross_cost !== null && data.default_gross_cost !== undefined;
}

/**
 * Validate margin percentage is within reasonable bounds
 * @param marginPercentage - Margin percentage to validate
 * @param fieldName - Name of the field for error messages
 * @throws Error if margin percentage is invalid
 */
export function validateMarginPercentage(marginPercentage: number | null, fieldName: string): void {
  if (marginPercentage !== null && marginPercentage !== undefined) {
    if (marginPercentage < 0 || marginPercentage > 1000) { // Allow up to 1000% for extreme cases
      throw new Error(`${fieldName} must be between 0 and 1000 percent`);
    }
  }
}

/**
 * Validate that cost value is non-negative
 * @param cost - Cost value to validate
 * @param fieldName - Name of the field for error messages
 * @throws Error if cost is negative
 */
export function validateCost(cost: number | null, fieldName: string): void {
  if (cost !== null && cost !== undefined && cost < 0) {
    throw new Error(`${fieldName} cannot be negative`);
  }
}

/**
 * Validate all cost and margin fields in a base price rule data object
 * @param data - Base price rule data to validate
 * @throws Error if any validation fails
 */
export function validateCostMarginData(data: BasePriceRuleData): void {
  // Validate default values
  validateCost(data.default_gross_cost, 'default_gross_cost');
  validateMarginPercentage(data.default_margin_percentage, 'default_margin_percentage');

  // Validate day-specific values
  const days: DayOfWeek[] = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  
  for (const day of days) {
    const grossCostField = `${day}_gross_cost` as keyof BasePriceRuleData;
    const marginPercentageField = `${day}_margin_percentage` as keyof BasePriceRuleData;
    
    validateCost(data[grossCostField] as number | null, grossCostField);
    validateMarginPercentage(data[marginPercentageField] as number | null, marginPercentageField);
  }
}

/**
 * Apply default values to all days of the week
 * @param data - Base price rule data to modify
 * @param applyGrossCost - Whether to apply default gross cost to all days
 * @param applyFixedMargin - Whether to apply default fixed margin to all days
 * @param applyMarginPercentage - Whether to apply default margin percentage to all days
 * @returns Modified data object with default values applied to all days
 */
export function applyDefaultsToAllDays(
  data: BasePriceRuleData,
  applyGrossCost: boolean = true,
  applyFixedMargin: boolean = true,
  applyMarginPercentage: boolean = true
): BasePriceRuleData {
  const days: DayOfWeek[] = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  const updatedData = { ...data };

  for (const day of days) {
    if (applyGrossCost && data.default_gross_cost !== null && data.default_gross_cost !== undefined) {
      const grossCostField = `${day}_gross_cost` as keyof BasePriceRuleData;
      (updatedData as any)[grossCostField] = data.default_gross_cost;
    }

    if (applyFixedMargin && data.default_fixed_margin !== null && data.default_fixed_margin !== undefined) {
      const fixedMarginField = `${day}_fixed_margin` as keyof BasePriceRuleData;
      (updatedData as any)[fixedMarginField] = data.default_fixed_margin;
    }

    if (applyMarginPercentage && data.default_margin_percentage !== null && data.default_margin_percentage !== undefined) {
      const marginPercentageField = `${day}_margin_percentage` as keyof BasePriceRuleData;
      (updatedData as any)[marginPercentageField] = data.default_margin_percentage;
    }
  }

  return updatedData;
}
