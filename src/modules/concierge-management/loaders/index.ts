import { LoaderOptions } from "@camped-ai/framework/types";
import { asClass } from "awilix";
import { BaseRepository } from "../repositories";
import ItineraryService from "../itinerary-service";
import { ITINERARY_SERVICE } from "../index";

export default async ({ container }: LoaderOptions): Promise<void> => {
  console.log("🚀 [CONCIERGE-MODULE] Starting concierge management module loader...");

  try {
    // Register base repository
    container.register({
      baseRepository: asClass(BaseRepository).singleton(),
    });
    console.log("✅ [CONCIERGE-MODULE] BaseRepository registered");

    // Register itinerary service with constant key
    if (!container.hasRegistration(ITINERARY_SERVICE)) {
      container.register({
        [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
      });
      console.log("✅ [CONCIERGE-MODULE] ItineraryService registered with constant key");
    }

    // Register itinerary service with string key for backward compatibility
    if (!container.hasRegistration("itineraryService")) {
      container.register({
        itineraryService: asClass(ItineraryService).singleton(),
      });
      console.log("✅ [CONCIERGE-MODULE] ItineraryService registered with string key");
    }

    // Verify registration
    const hasConstantKey = container.hasRegistration(ITINERARY_SERVICE);
    const hasStringKey = container.hasRegistration("itineraryService");
    console.log(`🔍 [CONCIERGE-MODULE] Registration verification - Constant: ${hasConstantKey}, String: ${hasStringKey}`);

  } catch (error) {
    console.error("❌ [CONCIERGE-MODULE] Failed to register services:", error);
    throw error;
  }
}
