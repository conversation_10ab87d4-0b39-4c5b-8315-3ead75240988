/**
 * Concierge Management Module Types
 * 
 * This file contains all type definitions for the concierge management system
 * including task management, assignments, and related functionality.
 */

// Enums
export enum TaskStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress", 
  REVIEW = "review",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

export enum TaskPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent",
}

export enum EntityType {
  BOOKING = "booking",
  DEAL = "deal",
  GUEST = "guest",
  ITINERARY = "itinerary",
}

// Note interfaces
export interface NoteInput {
  title: string;
  content?: string;
  entity: string;
  entity_id: string;
  created_by_id?: string;
}

export interface NoteResponse extends NoteInput {
  id: string;
  created_at: Date;
  updated_at: Date;
  updated_by_id?: string;
  deleted: boolean;
  deleted_at?: Date;
}

export interface NoteUpdateInput {
  title?: string;
  content?: string;
  updated_by_id?: string;
  deleted?: boolean;
}

// Note request/response types
export interface CreateNoteRequest {
  note: NoteInput;
}

export interface UpdateNoteRequest {
  note: NoteUpdateInput;
}

export interface ListNotesRequest {
  filters?: {
    entity?: string;
    entity_id?: string;
    deleted?: boolean;
    created_by_id?: string;
    q?: string; // Search query
    created_at_gte?: string;
    created_at_lte?: string;
  };
  options?: {
    limit?: number;
    offset?: number;
    order?: Record<string, "ASC" | "DESC">;
  };
}

export interface NoteListResponse {
  notes: NoteResponse[];
  count: number;
  limit: number;
  offset: number;
}

// Base interfaces
export interface ConciergeTaskInput {
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string; // User ID
  created_by?: string; // User ID
  due_date?: Date;
  metadata?: Record<string, any>;
}

export interface ConciergeTaskResponse extends ConciergeTaskInput {
  id: string;
  created_at: Date;
  updated_at: Date;
  updated_by?: string;
  is_deleted: boolean;
  deleted_at?: Date;
}

export interface ConciergeTaskUpdateInput {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  due_date?: Date;
  metadata?: Record<string, any>;
  updated_by?: string;
}

// Query interfaces
export interface ConciergeTaskFilters {
  id?: string | string[];
  title?: string;
  status?: TaskStatus | TaskStatus[];
  priority?: TaskPriority | TaskPriority[];
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  created_by?: string;
  due_date?: {
    gte?: Date;
    lte?: Date;
  };
  created_at?: {
    gte?: Date;
    lte?: Date;
  };
  is_deleted?: boolean;
}

export interface ConciergeTaskListOptions {
  limit?: number;
  offset?: number;
  order?: Record<string, "ASC" | "DESC">;
}

export interface ConciergeTaskListResponse {
  tasks: ConciergeTaskResponse[];
  count: number;
  limit: number;
  offset: number;
}

// Service method interfaces
export interface CreateConciergeTaskRequest {
  task: ConciergeTaskInput;
}

export interface UpdateConciergeTaskRequest {
  task: ConciergeTaskUpdateInput;
}

export interface ListConciergeTasksRequest {
  filters?: ConciergeTaskFilters;
  options?: ConciergeTaskListOptions;
}

// Service interface
export interface ConciergeManagementServiceMethods {
  // Task management
  createTask(data: CreateConciergeTaskRequest): Promise<ConciergeTaskResponse>;
  updateTask(id: string, data: UpdateConciergeTaskRequest): Promise<ConciergeTaskResponse>;
  retrieveTask(id: string): Promise<ConciergeTaskResponse>;
  listTasks(request: ListConciergeTasksRequest): Promise<ConciergeTaskListResponse>;
  deleteTask(id: string): Promise<void>;
  softDeleteTask(id: string, deletedBy?: string): Promise<ConciergeTaskResponse>;

  // Task assignment
  assignTask(taskId: string, userId: string, assignedBy?: string): Promise<ConciergeTaskResponse>;
  unassignTask(taskId: string, unassignedBy?: string): Promise<ConciergeTaskResponse>;

  // Task status management
  completeTask(taskId: string, completedBy?: string): Promise<ConciergeTaskResponse>;
  cancelTask(taskId: string, cancelledBy?: string): Promise<ConciergeTaskResponse>;

  // Entity-specific methods
  getTasksForEntity(entityType: string, entityId: string): Promise<ConciergeTaskResponse[]>;
  getTasksForUser(userId: string): Promise<ConciergeTaskResponse[]>;

  // Note management
  createNote(data: CreateNoteRequest): Promise<NoteResponse>;
  updateNote(id: string, data: UpdateNoteRequest): Promise<NoteResponse>;
  getNote(id: string): Promise<NoteResponse>;
  getNotes(request: ListNotesRequest): Promise<NoteListResponse>;
  deleteNote(id: string): Promise<void>;
  softDeleteNote(id: string, deletedBy?: string): Promise<NoteResponse>;

  // Note entity-specific methods
  getNotesForEntity(entity: string, entityId: string): Promise<NoteResponse[]>;
}
