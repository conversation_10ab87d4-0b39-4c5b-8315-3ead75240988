import { Module } from "@camped-ai/framework/utils";
import ConciergeManagementService from "./service";
import ItineraryService from "./itinerary-service";
import repositoryLoader from "./loaders";

export const CONCIERGE_MANAGEMENT_MODULE = "conciergeManagementModuleService";
export const ITINERARY_SERVICE = "itineraryService";

export const conciergeManagementModule = Module(CONCIERGE_MANAGEMENT_MODULE, {
  service: ConciergeManagementService,
  loaders: [repositoryLoader],
});

export default conciergeManagementModule;

// Export models
export { ConciergeTask } from "./models/concierge-task";
export { Note } from "./models/note";
export { Itinerary, ItineraryDay, ItineraryEvent } from "./models/itinerary";

// Export services
export { default as ItineraryService } from "./itinerary-service";

// Export types
export * from "./types";
