import { MedusaService, MedusaError } from "@camped-ai/framework/utils";
import { ConciergeTask } from "./models/concierge-task";
import { Note } from "./models/note";
import {
  ConciergeManagementServiceMethods,
  ConciergeTaskResponse,
  CreateConciergeTaskRequest,
  UpdateConciergeTaskRequest,
  ListConciergeTasksRequest,
  ConciergeTaskListResponse,
  TaskStatus,
  NoteResponse,
  CreateNoteRequest,
  UpdateNoteRequest,
  ListNotesRequest,
  NoteListResponse,
} from "./types";

/**
 * Concierge Management Service
 *
 * Provides comprehensive concierge management functionality including
 * task management, assignments, and entity linking.
 */
class ConciergeManagementService extends MedusaService({
  ConciergeTask,
  Note,
}) implements ConciergeManagementServiceMethods {
  
  /**
   * Create a new concierge task
   */
  async createTask(data: CreateConciergeTaskRequest): Promise<ConciergeTaskResponse> {
    try {
      const taskData = {
        ...data.task,
        is_deleted: false,
      };

      const tasks = await this.createConciergeTasks([taskData]);
      return tasks[0] as ConciergeTaskResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create concierge task: ${error.message}`
      );
    }
  }
  
  /**
   * Update an existing concierge task
   */
  async updateTask(id: string, data: UpdateConciergeTaskRequest): Promise<ConciergeTaskResponse> {
    try {
      const updateData = {
        ...data.task,
      };

      const tasks = await this.updateConciergeTasks([{
        id,
        ...updateData,
      }]);

      return tasks[0] as ConciergeTaskResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge task: ${error.message}`
      );
    }
  }
  
  /**
   * Retrieve a single concierge task
   */
  async retrieveTask(id: string): Promise<ConciergeTaskResponse> {
    try {
      const task = await this.retrieveConciergeTask(id);

      if (!task) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Task with id ${id} not found`
        );
      }

      return task as ConciergeTaskResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge task: ${error.message}`
      );
    }
  }
  
  /**
   * List concierge tasks with filtering and pagination
   */
  async listTasks(request: ListConciergeTasksRequest): Promise<ConciergeTaskListResponse> {
    try {
      const { filters = {}, options = {} } = request;
      const { limit = 20, offset = 0, order = { created_at: "DESC" } } = options;

      // Build filters - exclude soft deleted by default
      const queryFilters = {
        ...filters,
        is_deleted: filters.is_deleted !== undefined ? filters.is_deleted : false,
      };

      const tasks = await this.listConciergeTasks(queryFilters, {
        skip: offset,
        take: limit,
        order,
      });

      // Get count separately
      const allTasks = await this.listConciergeTasks(queryFilters);
      const count = Array.isArray(allTasks) ? allTasks.length : 0;

      return {
        tasks: tasks as ConciergeTaskResponse[],
        count,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge tasks: ${error.message}`
      );
    }
  }
  
  /**
   * Hard delete a concierge task
   */
  async deleteTask(id: string): Promise<void> {
    try {
      await this.deleteConciergeTasks([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete concierge task: ${error.message}`
      );
    }
  }

  /**
   * Soft delete a concierge task
   */
  async softDeleteTask(id: string, deletedBy?: string): Promise<ConciergeTaskResponse> {
    try {
      const updateData = {
        is_deleted: true,
        deleted_at: new Date(),
        ...(deletedBy && { updated_by: deletedBy }),
      };

      const tasks = await this.updateConciergeTasks([{
        id,
        ...updateData,
      }]);

      return tasks[0] as ConciergeTaskResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to soft delete concierge task: ${error.message}`
      );
    }
  }
  
  /**
   * Assign a task to a user
   */
  async assignTask(taskId: string, userId: string, assignedBy?: string): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        assigned_to: userId,
        status: TaskStatus.IN_PROGRESS,
        ...(assignedBy && { updated_by: assignedBy }),
      },
    });
  }

  /**
   * Unassign a task from a user
   */
  async unassignTask(taskId: string, unassignedBy?: string): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        assigned_to: undefined,
        status: TaskStatus.PENDING,
        ...(unassignedBy && { updated_by: unassignedBy }),
      },
    });
  }

  /**
   * Mark a task as completed
   */
  async completeTask(taskId: string, completedBy?: string): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        status: TaskStatus.COMPLETED,
        ...(completedBy && { updated_by: completedBy }),
      },
    });
  }

  /**
   * Cancel a task
   */
  async cancelTask(taskId: string, cancelledBy?: string): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        status: TaskStatus.CANCELLED,
        ...(cancelledBy && { updated_by: cancelledBy }),
      },
    });
  }
  
  /**
   * Get all tasks for a specific entity
   */
  async getTasksForEntity(entityType: string, entityId: string): Promise<ConciergeTaskResponse[]> {
    try {
      const result = await this.listTasks({
        filters: {
          entity_type: entityType,
          entity_id: entityId,
          is_deleted: false,
        },
        options: {
          order: { created_at: "DESC" },
          limit: 100,
        },
      });

      return result.tasks;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get tasks for entity: ${error.message}`
      );
    }
  }

  /**
   * Get all tasks assigned to a specific user
   */
  async getTasksForUser(userId: string): Promise<ConciergeTaskResponse[]> {
    try {
      const result = await this.listTasks({
        filters: {
          assigned_to: userId,
          is_deleted: false,
        },
        options: {
          order: { due_date: "ASC", priority: "DESC" },
          limit: 100,
        },
      });

      return result.tasks;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get tasks for user: ${error.message}`
      );
    }
  }

  // ===== NOTE MANAGEMENT METHODS =====

  /**
   * Create a new note
   */
  async createNote(data: CreateNoteRequest): Promise<NoteResponse> {
    try {
      const noteData = {
        ...data.note,
        deleted: false,
      };

      const notes = await this.createNotes([noteData]);
      return notes[0] as NoteResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create note: ${error.message}`
      );
    }
  }

  /**
   * Update an existing note
   */
  async updateNote(id: string, data: UpdateNoteRequest): Promise<NoteResponse> {
    try {
      const updateData = {
        ...data.note,
      };

      const notes = await this.updateNotes([{
        id,
        ...updateData,
      }]);

      return notes[0] as NoteResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update note: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a single note by ID
   */
  async getNote(id: string): Promise<NoteResponse> {
    try {
      const [notes] = await this.listAndCountNotes({ id });
      const note = notes[0];

      if (!note || note.deleted) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Note with id ${id} not found`
        );
      }

      return note as NoteResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve note: ${error.message}`
      );
    }
  }

  /**
   * List notes with filtering and pagination
   */
  async getNotes(request: ListNotesRequest): Promise<NoteListResponse> {
    try {
      const { filters = {}, options = {} } = request;
      const { limit = 20, offset = 0, order = { created_at: "DESC" } } = options;

      // Build filters - exclude soft deleted by default
      const queryFilters = {
        ...filters,
        deleted: filters.deleted !== undefined ? filters.deleted : false,
      };

      const [notes, count] = await this.listAndCountNotes(queryFilters, {
        skip: offset,
        take: limit,
        order,
      });

      return {
        notes: notes as NoteResponse[],
        count,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list notes: ${error.message}`
      );
    }
  }

  /**
   * Hard delete a note
   */
  async deleteNote(id: string): Promise<void> {
    try {
      await this.deleteNotes([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete note: ${error.message}`
      );
    }
  }

  /**
   * Soft delete a note
   */
  async softDeleteNote(id: string, deletedBy?: string): Promise<NoteResponse> {
    return await this.updateNote(id, {
      note: {
        deleted: true,
        ...(deletedBy && { updated_by_id: deletedBy }),
      },
    });
  }

  /**
   * Get all notes for a specific entity
   */
  async getNotesForEntity(entity: string, entityId: string): Promise<NoteResponse[]> {
    try {
      const result = await this.getNotes({
        filters: {
          entity,
          entity_id: entityId,
          deleted: false,
        },
        options: {
          order: { created_at: "DESC" },
          limit: 100,
        },
      });

      return result.notes;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get notes for entity: ${error.message}`
      );
    }
  }
}

export default ConciergeManagementService;
