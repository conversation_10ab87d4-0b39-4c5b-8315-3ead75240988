import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import multer from 'multer';
import * as XLSX from 'xlsx';

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (_req: any, file: any, cb: any) => {
    // Accept Excel and CSV files
    if (
      file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.mimetype === 'application/vnd.ms-excel' ||
      file.mimetype === 'text/csv' ||
      file.mimetype === 'application/csv'
    ) {
      cb(null, true);
    } else {
      cb(new Error('Only Excel (.xlsx, .xls) and CSV files are allowed'));
    }
  },
});

// Helper function to normalize column names
const normalizeColumnName = (columnName: string): string => {
  return columnName
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '_')
    .replace(/[^a-z0-9_]/g, '');
};

// Helper function to validate required columns
const validateColumns = (headers: string[]): { isValid: boolean; missingColumns: string[] } => {
  const normalizedHeaders = headers.map(normalizeColumnName);

  const requiredColumns = [
    'room_config_name',
    'occupancy_name',
    'meal_plan_name',
    'currency_code'
  ];

  // At least one pricing method should be present (either direct prices or cost/margin data)
  const pricingColumns = [
    'monday_price', 'tuesday_price', 'wednesday_price', 'thursday_price',
    'friday_price', 'saturday_price', 'sunday_price'
  ];

  const costMarginColumns = [
    'default_gross_cost', 'default_fixed_margin', 'default_margin_percentage'
  ];

  const hasPricing = pricingColumns.some(col => normalizedHeaders.includes(col));
  const hasCostMargin = costMarginColumns.some(col => normalizedHeaders.includes(col));

  const missingColumns = requiredColumns.filter(col => !normalizedHeaders.includes(col));

  if (!hasPricing && !hasCostMargin) {
    missingColumns.push('pricing_data (either weekday prices or cost/margin columns)');
  }

  return {
    isValid: missingColumns.length === 0,
    missingColumns
  };
};

// Helper function to parse and validate pricing data
const validatePricingRow = (row: any, rowIndex: number): { isValid: boolean; errors: any[] } => {
  const errors: any[] = [];

  // Check required fields
  if (!row.room_config_name || row.room_config_name.toString().trim() === '') {
    errors.push({
      row: rowIndex,
      field: 'room_config_name',
      message: 'Room Config Name is required',
      value: row.room_config_name
    });
  }

  if (!row.occupancy_name || row.occupancy_name.toString().trim() === '') {
    errors.push({
      row: rowIndex,
      field: 'occupancy_name',
      message: 'Occupancy Name is required',
      value: row.occupancy_name
    });
  }

  // Allow "-" as a valid value for no meal plan, but require the field to be present
  const mealPlanValue = row.meal_plan_name?.toString().trim();
  if (!mealPlanValue) {
    errors.push({
      row: rowIndex,
      field: 'meal_plan_name',
      message: 'Meal Plan Name is required (use "-" for no meal plan)',
      value: row.meal_plan_name
    });
  }

  if (!row.currency_code || row.currency_code.toString().trim() === '') {
    errors.push({
      row: rowIndex,
      field: 'currency_code',
      message: 'Currency Code is required',
      value: row.currency_code
    });
  }

  // Validate weekday prices
  const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  weekdays.forEach(day => {
    const priceField = `${day}_price`;
    const price = row[priceField];

    if (price !== undefined && price !== null && price !== '') {
      const numericPrice = parseFloat(price.toString());
      if (isNaN(numericPrice) || numericPrice < 0) {
        errors.push({
          row: rowIndex,
          field: priceField,
          message: `${day.charAt(0).toUpperCase() + day.slice(1)} price must be a valid positive number`,
          value: price
        });
      }
    }

    // Validate weekday cost fields
    const costField = `${day}_gross_cost`;
    const cost = row[costField];
    if (cost !== undefined && cost !== null && cost !== '') {
      const numericCost = parseFloat(cost.toString());
      if (isNaN(numericCost) || numericCost < 0) {
        errors.push({
          row: rowIndex,
          field: costField,
          message: `${day.charAt(0).toUpperCase() + day.slice(1)} gross cost must be a valid positive number`,
          value: cost
        });
      }
    }

    // Validate weekday fixed margin fields
    const fixedMarginField = `${day}_fixed_margin`;
    const fixedMargin = row[fixedMarginField];
    if (fixedMargin !== undefined && fixedMargin !== null && fixedMargin !== '') {
      const numericMargin = parseFloat(fixedMargin.toString());
      if (isNaN(numericMargin) || numericMargin < 0) {
        errors.push({
          row: rowIndex,
          field: fixedMarginField,
          message: `${day.charAt(0).toUpperCase() + day.slice(1)} fixed margin must be a valid positive number`,
          value: fixedMargin
        });
      }
    }

    // Validate weekday margin percentage fields
    const marginPercentageField = `${day}_margin_percentage`;
    const marginPercentage = row[marginPercentageField];
    if (marginPercentage !== undefined && marginPercentage !== null && marginPercentage !== '') {
      const numericPercentage = parseFloat(marginPercentage.toString());
      if (isNaN(numericPercentage) || numericPercentage < 0 || numericPercentage > 100) {
        errors.push({
          row: rowIndex,
          field: marginPercentageField,
          message: `${day.charAt(0).toUpperCase() + day.slice(1)} margin percentage must be between 0 and 100`,
          value: marginPercentage
        });
      }
    }
  });

  // Validate default cost/margin fields
  const defaultFields = [
    { field: 'default_gross_cost', name: 'Default Gross Cost', min: 0 },
    { field: 'default_fixed_margin', name: 'Default Fixed Margin', min: 0 },
    { field: 'default_margin_percentage', name: 'Default Margin Percentage', min: 0, max: 100 }
  ];

  defaultFields.forEach(({ field, name, min, max }) => {
    const value = row[field];
    if (value !== undefined && value !== null && value !== '') {
      const numericValue = parseFloat(value.toString());
      if (isNaN(numericValue) || numericValue < min || (max !== undefined && numericValue > max)) {
        const rangeText = max !== undefined ? `between ${min} and ${max}` : `at least ${min}`;
        errors.push({
          row: rowIndex,
          field: field,
          message: `${name} must be a valid number ${rangeText}`,
          value: value
        });
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * POST endpoint to parse hotel pricing data from uploaded Excel/CSV file
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  // Use multer to handle the file upload
  const multerUpload = upload.single('file');

  multerUpload(req as any, res as any, async (err: any) => {
    if (err) {
      return res.status(400).json({ 
        success: false,
        message: err.message 
      });
    }

    if (!(req as any).file) {
      return res.status(400).json({ 
        success: false,
        message: 'No file uploaded' 
      });
    }

    try {
      const hotelId = req.params.id;
      const file = (req as any).file;
      let workbook: XLSX.WorkBook;

      console.log(`[Pricing Parse API] Processing file for hotel: ${hotelId}`);
      console.log(`[Pricing Parse API] File: ${file.originalname}, Size: ${file.size} bytes`);

      // Parse the file based on its type
      if (file.mimetype.includes('csv')) {
        // Parse CSV
        const csvData = file.buffer.toString('utf8');
        workbook = XLSX.read(csvData, { type: 'string' });
      } else {
        // Parse Excel
        workbook = XLSX.read(file.buffer, { type: 'buffer' });
      }

      // Get the first sheet
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) {
        return res.status(400).json({
          success: false,
          message: 'No sheets found in the uploaded file'
        });
      }

      const worksheet = workbook.Sheets[sheetName];
      const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (rawData.length < 2) {
        return res.status(400).json({
          success: false,
          message: 'File must contain at least a header row and one data row'
        });
      }

      // Extract headers and normalize them
      const headers = (rawData[0] as string[]).map(h => h?.toString().trim() || '');
      const normalizedHeaders = headers.map(normalizeColumnName);

      // Validate required columns
      const columnValidation = validateColumns(headers);
      if (!columnValidation.isValid) {
        return res.status(400).json({
          success: false,
          message: `Missing required columns: ${columnValidation.missingColumns.join(', ')}`,
          errors: columnValidation.missingColumns.map(col => ({
            row: 0,
            field: col,
            message: `Required column '${col}' is missing`,
            value: null
          }))
        });
      }

      // Parse data rows
      const pricingData: any[] = [];
      const errors: any[] = [];

      for (let i = 1; i < rawData.length; i++) {
        const rowData = rawData[i] as any[];
        if (!rowData || rowData.every(cell => !cell || cell.toString().trim() === '')) {
          continue; // Skip empty rows
        }

        const row: any = {};
        headers.forEach((header, index) => {
          const normalizedHeader = normalizeColumnName(header);
          row[normalizedHeader] = rowData[index]?.toString().trim() || '';
        });

        // Validate the row
        const validation = validatePricingRow(row, i);
        if (!validation.isValid) {
          errors.push(...validation.errors);
        } else {
          pricingData.push(row);
        }
      }

      console.log(`[Pricing Parse API] Parsed ${pricingData.length} valid rows with ${errors.length} errors`);

      return res.json({
        success: true,
        message: `Successfully parsed ${pricingData.length} pricing records`,
        pricing_data: pricingData,
        errors: errors,
        total_rows: rawData.length - 1,
        valid_rows: pricingData.length,
        error_count: errors.length
      });

    } catch (error) {
      console.error('Error parsing pricing file:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to parse file',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });
};
