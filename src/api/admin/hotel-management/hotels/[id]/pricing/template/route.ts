import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import * as ExcelJS from 'exceljs';
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";

// Query validation schema
const GetAdminHotelPricingTemplateQuery = z.object({
  currency: z.string().optional().default("CHF"),
});

type GetAdminHotelPricingTemplateQueryType = z.infer<typeof GetAdminHotelPricingTemplateQuery>;

/**
 * GET /admin/hotel-management/hotels/[id]/pricing/template
 * Generate Excel template for hotel pricing import
 */
export const GET = async (
  req: MedusaRequest<{}, GetAdminHotelPricingTemplateQueryType>,
  res: MedusaResponse
) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Parse and validate query parameters
    const validatedQuery = GetAdminHotelPricingTemplateQuery.parse(req.query);
    const { currency } = validatedQuery;

    console.log(`[Pricing Template API] Generating template for hotel: ${hotelId}, currency: ${currency}`);

    // Get hotel details
    const { data: hotel } = await query.graph({
      entity: "hotel",
      filters: { id: [hotelId] },
      fields: ["id", "name", "handle"],
    });

    if (!hotel || hotel.length === 0) {
      return res.status(404).json({
        message: "Hotel not found",
        type: "not_found",
      });
    }

    // Get room configurations
    const { data: allProducts } = await query.graph({
      entity: "product",
      filters: {},
      fields: ["id", "title", "handle", "metadata"],
    });

    const roomConfigs = allProducts?.filter(product =>
      product.metadata &&
      (product.metadata.hotel_id === hotelId || product.metadata.type === "room_config")
    ) || [];

    // Get occupancy configurations
    const { data: occupancyConfigs } = await query.graph({
      entity: "occupancy_config",
      filters: { hotel_id: hotelId },
      fields: ["id", "name"],
    });

    // Get meal plans
    const mealPlans = await hotelPricingService.listMealPlans({
      hotel_id: hotelId,
    });

    console.log(`[Pricing Template API] Found ${roomConfigs.length} room configs, ${occupancyConfigs.length} occupancy configs, ${mealPlans.length} meal plans`);

    // Create workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Hotel Management System';
    workbook.created = new Date();

    // Create main pricing sheet
    const worksheet = workbook.addWorksheet('Hotel Pricing Import');

    // Define columns
    const columns = [
      { header: 'Room Config Name', key: 'room_config_name', width: 25 },
      { header: 'Occupancy Name', key: 'occupancy_name', width: 20 },
      { header: 'Meal Plan Name', key: 'meal_plan_name', width: 25 },
      { header: 'Currency Code', key: 'currency_code', width: 15 },

      // Default cost/margin columns
      { header: 'Default Gross Cost', key: 'default_gross_cost', width: 18 },
      { header: 'Default Fixed Margin', key: 'default_fixed_margin', width: 20 },
      { header: 'Default Margin %', key: 'default_margin_percentage', width: 18 },

      // Weekday price columns
      { header: 'Monday Price', key: 'monday_price', width: 15 },
      { header: 'Tuesday Price', key: 'tuesday_price', width: 15 },
      { header: 'Wednesday Price', key: 'wednesday_price', width: 15 },
      { header: 'Thursday Price', key: 'thursday_price', width: 15 },
      { header: 'Friday Price', key: 'friday_price', width: 15 },
      { header: 'Saturday Price', key: 'saturday_price', width: 15 },
      { header: 'Sunday Price', key: 'sunday_price', width: 15 },

      // Weekday cost columns
      { header: 'Monday Gross Cost', key: 'monday_gross_cost', width: 18 },
      { header: 'Tuesday Gross Cost', key: 'tuesday_gross_cost', width: 18 },
      { header: 'Wednesday Gross Cost', key: 'wednesday_gross_cost', width: 20 },
      { header: 'Thursday Gross Cost', key: 'thursday_gross_cost', width: 19 },
      { header: 'Friday Gross Cost', key: 'friday_gross_cost', width: 18 },
      { header: 'Saturday Gross Cost', key: 'saturday_gross_cost', width: 19 },
      { header: 'Sunday Gross Cost', key: 'sunday_gross_cost', width: 18 },

      // Weekday fixed margin columns
      { header: 'Monday Fixed Margin', key: 'monday_fixed_margin', width: 20 },
      { header: 'Tuesday Fixed Margin', key: 'tuesday_fixed_margin', width: 20 },
      { header: 'Wednesday Fixed Margin', key: 'wednesday_fixed_margin', width: 22 },
      { header: 'Thursday Fixed Margin', key: 'thursday_fixed_margin', width: 21 },
      { header: 'Friday Fixed Margin', key: 'friday_fixed_margin', width: 20 },
      { header: 'Saturday Fixed Margin', key: 'saturday_fixed_margin', width: 21 },
      { header: 'Sunday Fixed Margin', key: 'sunday_fixed_margin', width: 20 },

      // Weekday margin percentage columns
      { header: 'Monday Margin %', key: 'monday_margin_percentage', width: 18 },
      { header: 'Tuesday Margin %', key: 'tuesday_margin_percentage', width: 18 },
      { header: 'Wednesday Margin %', key: 'wednesday_margin_percentage', width: 20 },
      { header: 'Thursday Margin %', key: 'thursday_margin_percentage', width: 19 },
      { header: 'Friday Margin %', key: 'friday_margin_percentage', width: 18 },
      { header: 'Saturday Margin %', key: 'saturday_margin_percentage', width: 19 },
      { header: 'Sunday Margin %', key: 'sunday_margin_percentage', width: 18 },
    ];

    worksheet.columns = columns;

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
    headerRow.height = 25;

    // Add sample data rows
    const sampleRows = [];
    
    // Generate sample combinations (limit to first few for template)
    const maxSamples = Math.min(5, roomConfigs.length);
    for (let i = 0; i < maxSamples; i++) {
      const roomConfig = roomConfigs[i];
      const occupancy = occupancyConfigs[0] || { name: '2 Adults' };
      const mealPlan = mealPlans[0] || { name: 'Room Only' };

      sampleRows.push({
        room_config_name: roomConfig.title,
        occupancy_name: occupancy.name,
        meal_plan_name: mealPlan.name,
        currency_code: currency,

        // Default cost/margin values
        default_gross_cost: 100.00,
        default_fixed_margin: 30.00,
        default_margin_percentage: 25,

        // Weekday prices (can be calculated from cost/margin or set directly)
        monday_price: 150.00,
        tuesday_price: 150.00,
        wednesday_price: 150.00,
        thursday_price: 150.00,
        friday_price: 180.00,
        saturday_price: 200.00,
        sunday_price: 180.00,

        // Weekday cost values (optional - leave empty to use defaults)
        monday_gross_cost: '',
        tuesday_gross_cost: '',
        wednesday_gross_cost: '',
        thursday_gross_cost: '',
        friday_gross_cost: 120.00, // Example: Friday has higher cost
        saturday_gross_cost: 140.00, // Example: Saturday has higher cost
        sunday_gross_cost: '',

        // Weekday fixed margin values (optional - leave empty to use defaults)
        monday_fixed_margin: '',
        tuesday_fixed_margin: '',
        wednesday_fixed_margin: '',
        thursday_fixed_margin: '',
        friday_fixed_margin: 40.00, // Example: Friday has higher margin
        saturday_fixed_margin: 45.00, // Example: Saturday has higher margin
        sunday_fixed_margin: '',

        // Weekday margin percentage values (optional - leave empty to use defaults)
        monday_margin_percentage: '',
        tuesday_margin_percentage: '',
        wednesday_margin_percentage: '',
        thursday_margin_percentage: '',
        friday_margin_percentage: '', // Using fixed margin instead
        saturday_margin_percentage: '', // Using fixed margin instead
        sunday_margin_percentage: '',
      });
    }

    // Add sample rows to worksheet
    sampleRows.forEach(row => {
      worksheet.addRow(row);
    });

    // Style data rows
    for (let i = 2; i <= sampleRows.length + 1; i++) {
      const row = worksheet.getRow(i);
      row.alignment = { horizontal: 'left', vertical: 'middle' };
      
      // Alternate row colors
      if (i % 2 === 0) {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F8F9FA' }
        };
      }
    }

    // Add borders to all cells
    const range = `A1:${String.fromCharCode(65 + columns.length - 1)}${sampleRows.length + 1}`;
    worksheet.getCell(range).border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };

    // Create reference sheets
    
    // Room Configs sheet
    const roomConfigsSheet = workbook.addWorksheet('Room Configs Reference');
    roomConfigsSheet.columns = [
      { header: 'Room Config Name', key: 'name', width: 30 },
      { header: 'Room Config ID', key: 'id', width: 30 },
    ];
    
    roomConfigsSheet.getRow(1).font = { bold: true };
    roomConfigs.forEach(rc => {
      roomConfigsSheet.addRow({ name: rc.title, id: rc.id });
    });

    // Occupancy Configs sheet
    const occupancySheet = workbook.addWorksheet('Occupancy Reference');
    occupancySheet.columns = [
      { header: 'Occupancy Name', key: 'name', width: 25 },
      { header: 'Occupancy ID', key: 'id', width: 30 },
    ];
    
    occupancySheet.getRow(1).font = { bold: true };
    occupancyConfigs.forEach(oc => {
      occupancySheet.addRow({ name: oc.name, id: oc.id });
    });

    // Meal Plans sheet
    const mealPlansSheet = workbook.addWorksheet('Meal Plans Reference');
    mealPlansSheet.columns = [
      { header: 'Meal Plan Name', key: 'name', width: 30 },
      { header: 'Meal Plan ID', key: 'id', width: 30 },
    ];
    
    mealPlansSheet.getRow(1).font = { bold: true };
    mealPlans.forEach(mp => {
      mealPlansSheet.addRow({ name: mp.name, id: mp.id });
    });

    // Instructions sheet
    const instructionsSheet = workbook.addWorksheet('Instructions');
    instructionsSheet.columns = [
      { header: 'Import Instructions', key: 'instruction', width: 80 },
    ];

    const instructions = [
      'Hotel Pricing Import Template Instructions',
      '',
      '1. Fill in the "Hotel Pricing Import" sheet with your pricing data',
      '2. Use exact names from the reference sheets for Room Configs, Occupancy, and Meal Plans',
      '3. Currency Code should match your hotel\'s supported currencies (e.g., CHF, USD, EUR)',
      '4. Prices should be entered as decimal numbers (e.g., 150.00, not 150)',
      '5. Leave price fields empty if not applicable for that day',
      '6. Do not modify the column headers',
      '7. You can add more rows as needed',
      '',
      'Required Columns:',
      '- Room Config Name: Must match exactly with names in "Room Configs Reference" sheet',
      '- Occupancy Name: Must match exactly with names in "Occupancy Reference" sheet', 
      '- Meal Plan Name: Must match exactly with names in "Meal Plans Reference" sheet',
      '- Currency Code: 3-letter currency code (e.g., CHF, USD, EUR)',
      '- Weekday Prices: Monday through Sunday pricing (optional, can be left empty)',
      '',
      'Tips:',
      '- Use the reference sheets to copy exact names to avoid errors',
      '- Prices will be validated to ensure they are positive numbers',
      '- Existing pricing rules will be updated if they already exist',
      '- New pricing rules will be created for new combinations',
    ];

    instructionsSheet.getRow(1).font = { bold: true, size: 14 };
    instructions.forEach((instruction, index) => {
      const row = instructionsSheet.addRow({ instruction });
      if (index === 0) {
        row.font = { bold: true, size: 14 };
      } else if (instruction.endsWith(':')) {
        row.font = { bold: true };
      }
    });

    // Generate filename
    const timestamp = new Date().toISOString().split('T')[0];
    const hotelName = hotel[0].name.replace(/[^a-zA-Z0-9]/g, '_');
    const filename = `hotel_pricing_import_template_${hotelName}_${currency}_${timestamp}.xlsx`;

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    console.log(`[Pricing Template API] Generated template with ${sampleRows.length} sample rows`);

    // Write workbook to response
    await workbook.xlsx.write(res);
  } catch (error) {
    console.error('Error generating pricing template:', error);
    return res.status(500).json({
      message: "Failed to generate template",
      type: "internal_error",
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
