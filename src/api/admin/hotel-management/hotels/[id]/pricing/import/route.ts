import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";

// Validation schema for import request
const PostAdminImportHotelPricingSchema = z.object({
  pricing_data: z.array(
    z.object({
      room_config_name: z.string().min(1, "Room Config Name is required"),
      occupancy_name: z.string().min(1, "Occupancy Name is required"),
      meal_plan_name: z.string().min(1, "Meal Plan Name is required"),
      currency_code: z.string().min(1, "Currency Code is required"),

      // Weekday price fields
      monday_price: z.union([z.string(), z.number()]).optional(),
      tuesday_price: z.union([z.string(), z.number()]).optional(),
      wednesday_price: z.union([z.string(), z.number()]).optional(),
      thursday_price: z.union([z.string(), z.number()]).optional(),
      friday_price: z.union([z.string(), z.number()]).optional(),
      saturday_price: z.union([z.string(), z.number()]).optional(),
      sunday_price: z.union([z.string(), z.number()]).optional(),

      // Default cost/margin fields
      default_gross_cost: z.union([z.string(), z.number()]).optional(),
      default_fixed_margin: z.union([z.string(), z.number()]).optional(),
      default_margin_percentage: z.union([z.string(), z.number()]).optional(),

      // Weekday cost fields
      monday_gross_cost: z.union([z.string(), z.number()]).optional(),
      tuesday_gross_cost: z.union([z.string(), z.number()]).optional(),
      wednesday_gross_cost: z.union([z.string(), z.number()]).optional(),
      thursday_gross_cost: z.union([z.string(), z.number()]).optional(),
      friday_gross_cost: z.union([z.string(), z.number()]).optional(),
      saturday_gross_cost: z.union([z.string(), z.number()]).optional(),
      sunday_gross_cost: z.union([z.string(), z.number()]).optional(),

      // Weekday fixed margin fields
      monday_fixed_margin: z.union([z.string(), z.number()]).optional(),
      tuesday_fixed_margin: z.union([z.string(), z.number()]).optional(),
      wednesday_fixed_margin: z.union([z.string(), z.number()]).optional(),
      thursday_fixed_margin: z.union([z.string(), z.number()]).optional(),
      friday_fixed_margin: z.union([z.string(), z.number()]).optional(),
      saturday_fixed_margin: z.union([z.string(), z.number()]).optional(),
      sunday_fixed_margin: z.union([z.string(), z.number()]).optional(),

      // Weekday margin percentage fields
      monday_margin_percentage: z.union([z.string(), z.number()]).optional(),
      tuesday_margin_percentage: z.union([z.string(), z.number()]).optional(),
      wednesday_margin_percentage: z.union([z.string(), z.number()]).optional(),
      thursday_margin_percentage: z.union([z.string(), z.number()]).optional(),
      friday_margin_percentage: z.union([z.string(), z.number()]).optional(),
      saturday_margin_percentage: z.union([z.string(), z.number()]).optional(),
      sunday_margin_percentage: z.union([z.string(), z.number()]).optional(),

      // Optional seasonal fields
      seasonal_period_name: z.string().optional(),
      seasonal_start_date: z.string().optional(),
      seasonal_end_date: z.string().optional(),
    })
  ).min(1, "At least one pricing record is required"),
  currency: z.string().optional(),
  batch_size: z.number().optional().default(50), // Process in batches to avoid payload size limits
});

type PostAdminImportHotelPricingType = z.infer<typeof PostAdminImportHotelPricingSchema>;

/**
 * POST /admin/hotel-management/hotels/[id]/pricing/import
 * Import hotel pricing data from parsed CSV/Excel data
 */
export const POST = async (
  req: MedusaRequest<{}, PostAdminImportHotelPricingType>,
  res: MedusaResponse
) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Validate request body
    const validatedBody = PostAdminImportHotelPricingSchema.parse(req.body);
    const { pricing_data, currency, batch_size } = validatedBody;

    console.log(`[Pricing Import API] Starting import for hotel: ${hotelId}`);
    console.log(`[Pricing Import API] Records to import: ${pricing_data.length}`);
    console.log(`[Pricing Import API] Batch size: ${batch_size}`);

    // Verify hotel exists
    const { data: hotel } = await query.graph({
      entity: "hotel",
      filters: { id: [hotelId] },
      fields: ["id", "name"],
    });

    if (!hotel || hotel.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Hotel not found",
        type: "not_found",
      });
    }

    // Get room configurations for validation
    const { data: roomConfigs } = await query.graph({
      entity: "product",
      filters: {},
      fields: ["id", "title", "handle", "metadata"],
    });

    const hotelRoomConfigs = roomConfigs?.filter(product =>
      product.metadata &&
      (product.metadata.hotel_id === hotelId || product.metadata.type === "room_config")
    ) || [];

    // Get occupancy configurations
    const { data: occupancyConfigs } = await query.graph({
      entity: "occupancy_config",
      filters: { hotel_id: hotelId },
      fields: ["id", "name"],
    });

    // Get meal plans
    const mealPlans = await hotelPricingService.listMealPlans({
      hotel_id: hotelId,
    });

    // Create lookup maps (trim whitespace to handle data inconsistencies)
    const roomConfigMap = new Map(hotelRoomConfigs.map(rc => [rc.title.trim().toLowerCase(), rc]));
    const occupancyMap = new Map(occupancyConfigs.map(oc => [oc.name.trim().toLowerCase(), oc]));
    const mealPlanMap = new Map(mealPlans.map(mp => [mp.name.trim().toLowerCase(), mp]));

    console.log(`[Pricing Import API] Found ${hotelRoomConfigs.length} room configs, ${occupancyConfigs.length} occupancy configs, ${mealPlans.length} meal plans`);

    // Split data into chunks to avoid payload size issues
    const chunks = [];
    for (let i = 0; i < pricing_data.length; i += batch_size) {
      chunks.push(pricing_data.slice(i, i + batch_size));
    }

    console.log(`[Pricing Import API] Processing ${pricing_data.length} records in ${chunks.length} chunks of max ${batch_size} records each`);

    // Process and validate each pricing record
    const results = {
      imported: 0,
      errors: [] as Array<{ row: number; message: string; data: any }>,
    };

    // Process each chunk
    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
      const chunk = chunks[chunkIndex];
      console.log(`[Pricing Import API] Processing chunk ${chunkIndex + 1}/${chunks.length} with ${chunk.length} records`);

      for (let i = 0; i < chunk.length; i++) {
        const record = chunk[i];
        const globalIndex = chunkIndex * batch_size + i; // Calculate global index for error reporting
      
      try {
        // Find matching entities (trim whitespace to handle data inconsistencies)
        const roomConfigKey = record.room_config_name.trim().toLowerCase();
        const occupancyKey = record.occupancy_name.trim().toLowerCase();
        const roomConfig = roomConfigMap.get(roomConfigKey);
        const occupancyConfig = occupancyMap.get(occupancyKey);

        console.log(`[Pricing Import API] Looking up entities for record ${globalIndex + 1}:`);
        console.log(`[Pricing Import API] - Room config: "${record.room_config_name}" -> key: "${roomConfigKey}" -> found: ${!!roomConfig}`);
        console.log(`[Pricing Import API] - Occupancy: "${record.occupancy_name}" -> key: "${occupancyKey}" -> found: ${!!occupancyConfig}`);

        // Handle special case for "No Meal Plan" or similar placeholder values
        const mealPlanName = record.meal_plan_name?.trim().toLowerCase();
        const isNoMealPlan = !mealPlanName ||
                            mealPlanName === 'no meal plan' ||
                            mealPlanName === 'no meals' ||
                            mealPlanName === 'none' ||
                            mealPlanName === 'null' ||
                            mealPlanName === '' ||
                            mealPlanName === '-';

        const mealPlan = isNoMealPlan ? null : mealPlanMap.get(mealPlanName);
        console.log(`[Pricing Import API] - Meal plan: "${record.meal_plan_name}" -> key: "${mealPlanName}" -> isNoMealPlan: ${isNoMealPlan} -> found: ${!!mealPlan}`);

        // Validate entity existence
        if (!roomConfig) {
          console.log(`[Pricing Import API] Available room configs:`, Array.from(roomConfigMap.keys()));
          throw new Error(`Room configuration '${record.room_config_name}' not found`);
        }
        if (!occupancyConfig) {
          console.log(`[Pricing Import API] Available occupancy configs:`, Array.from(occupancyMap.keys()));
          throw new Error(`Occupancy configuration '${record.occupancy_name}' not found`);
        }
        if (!isNoMealPlan && !mealPlan) {
          console.log(`[Pricing Import API] Available meal plans:`, Array.from(mealPlanMap.keys()));
          throw new Error(`Meal plan '${record.meal_plan_name}' not found`);
        }

        console.log(`[Pricing Import API] Successfully found entities - Room: ${roomConfig.id}, Occupancy: ${occupancyConfig.id}, Meal Plan: ${mealPlan?.id || 'null'}`);

        // Parse weekday prices and cost/margin data
        const weekdayData: any = {};
        const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

        // Parse default cost/margin values
        const defaultValues: any = {};
        if (record.default_gross_cost !== undefined && record.default_gross_cost !== null && record.default_gross_cost !== '') {
          const cost = parseFloat(record.default_gross_cost.toString());
          if (!isNaN(cost) && cost >= 0) {
            defaultValues.default_gross_cost = Math.round(cost * 100); // Convert to cents
          }
        }

        if (record.default_fixed_margin !== undefined && record.default_fixed_margin !== null && record.default_fixed_margin !== '') {
          const margin = parseFloat(record.default_fixed_margin.toString());
          if (!isNaN(margin) && margin >= 0) {
            defaultValues.default_fixed_margin = Math.round(margin * 100); // Convert to cents
          }
        }

        if (record.default_margin_percentage !== undefined && record.default_margin_percentage !== null && record.default_margin_percentage !== '') {
          const percentage = parseFloat(record.default_margin_percentage.toString());
          if (!isNaN(percentage) && percentage >= 0 && percentage <= 100) {
            defaultValues.default_margin_percentage = percentage; // Store as percentage
          }
        }

        weekdays.forEach(day => {
          // Parse weekday prices
          const priceField = `${day}_price`;
          const price = record[priceField];
          if (price !== undefined && price !== null && price !== '') {
            const numericPrice = parseFloat(price.toString());
            if (!isNaN(numericPrice) && numericPrice >= 0) {
              weekdayData[priceField] = Math.round(numericPrice * 100); // Convert to cents
            }
          }

          // Parse weekday cost data
          const costField = `${day}_gross_cost`;
          const cost = record[costField];
          if (cost !== undefined && cost !== null && cost !== '') {
            const numericCost = parseFloat(cost.toString());
            if (!isNaN(numericCost) && numericCost >= 0) {
              weekdayData[costField] = Math.round(numericCost * 100); // Convert to cents
            }
          }

          // Parse weekday fixed margin data
          const fixedMarginField = `${day}_fixed_margin`;
          const fixedMargin = record[fixedMarginField];
          if (fixedMargin !== undefined && fixedMargin !== null && fixedMargin !== '') {
            const numericMargin = parseFloat(fixedMargin.toString());
            if (!isNaN(numericMargin) && numericMargin >= 0) {
              weekdayData[fixedMarginField] = Math.round(numericMargin * 100); // Convert to cents
            }
          }

          // Parse weekday margin percentage data
          const marginPercentageField = `${day}_margin_percentage`;
          const marginPercentage = record[marginPercentageField];
          if (marginPercentage !== undefined && marginPercentage !== null && marginPercentage !== '') {
            const numericPercentage = parseFloat(marginPercentage.toString());
            if (!isNaN(numericPercentage) && numericPercentage >= 0 && numericPercentage <= 100) {
              weekdayData[marginPercentageField] = numericPercentage; // Store as percentage
            }
          }
        });

        // Check if base price rule already exists
        const existingRules = await hotelPricingService.listBasePriceRules({
          room_config_id: roomConfig.id,
          occupancy_type_id: occupancyConfig.id,
          meal_plan_id: mealPlan?.id || null,
          currency_code: record.currency_code,
        });

        console.log(`[Pricing Import API] Checking for existing rules with filters:`, {
          room_config_id: roomConfig.id,
          occupancy_type_id: occupancyConfig.id,
          meal_plan_id: mealPlan?.id || null,
          currency_code: record.currency_code,
        });

        if (existingRules.length > 0) {
          // Update existing rule
          const existingRule = existingRules[0];
          const updateData = {
            id: existingRule.id,
            ...defaultValues,
            ...weekdayData,
            currency_code: record.currency_code,
          };
          console.log(`[Pricing Import API] About to update existing rule ${existingRule.id} with data:`, updateData);
          const updatedRules = await hotelPricingService.updateBasePriceRules([updateData]);
          console.log(`[Pricing Import API] Updated existing rule for ${record.room_config_name}/${record.occupancy_name}/${record.meal_plan_name}. Result:`, updatedRules[0]?.id);
        } else {
          // Create new rule
          const createData = {
            hotel_id: hotelId,
            room_config_id: roomConfig.id,
            occupancy_type_id: occupancyConfig.id,
            meal_plan_id: mealPlan?.id || null,
            currency_code: record.currency_code,
            amount: weekdayData.monday_price || weekdayData.sunday_price || 0, // Fallback amount
            ...defaultValues,
            ...weekdayData,
          };
          console.log(`[Pricing Import API] About to create new rule with data:`, createData);
          const createdRules = await hotelPricingService.createBasePriceRules([createData]);
          console.log(`[Pricing Import API] Created new rule for ${record.room_config_name}/${record.occupancy_name}/${record.meal_plan_name}. Result:`, createdRules[0]?.id);
        }

        results.imported++;
      } catch (error) {
        console.error(`[Pricing Import API] Error processing record ${globalIndex + 1}:`, error);
        results.errors.push({
          row: globalIndex + 1,
          message: error instanceof Error ? error.message : 'Unknown error',
          data: record,
        });
      }
      }
    }

    console.log(`[Pricing Import API] Import completed. Imported: ${results.imported}, Errors: ${results.errors.length}`);

    const response = {
      success: results.errors.length === 0,
      message: results.errors.length === 0 
        ? `Successfully imported ${results.imported} pricing records`
        : `Imported ${results.imported} records with ${results.errors.length} errors`,
      imported: results.imported,
      errors: results.errors,
      total_processed: pricing_data.length,
    };

    if (results.errors.length > 0) {
      return res.status(207).json(response); // 207 Multi-Status for partial success
    }

    return res.status(201).json(response);
  } catch (error) {
    console.error('Error importing hotel pricing data:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        type: "validation_error",
        errors: error.errors,
      });
    }

    return res.status(500).json({
      success: false,
      message: "Failed to import pricing data",
      type: "internal_error",
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
