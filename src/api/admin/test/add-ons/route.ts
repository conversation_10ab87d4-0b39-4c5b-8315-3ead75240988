import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { BOOKING_ADD_ONS_MODULE } from "../../../../modules/booking-add-ons";

/**
 * Test endpoint to debug add-ons functionality
 * GET /admin/test/add-ons
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("🧪 Testing add-ons functionality...");

    const tests = [];

    // Test 1: Check supplier products/services API
    try {
      const response = await fetch(`${req.protocol}://${req.get('host')}/admin/supplier-management/products-services?status=active&limit=10`, {
        headers: {
          'Cookie': req.headers.cookie || '',
        }
      });

      if (response.ok) {
        const data = await response.json();
        tests.push({
          name: "Supplier Products/Services API",
          status: "✅ PASS",
          details: `Found ${data.product_services?.length || 0} products/services`,
          data: {
            count: data.count,
            sample: data.product_services?.slice(0, 3).map((ps: any) => ({
              id: ps.id,
              name: ps.name,
              type: ps.type,
              base_cost: ps.base_cost,
              service_level: ps.service_level
            }))
          }
        });
      } else {
        tests.push({
          name: "Supplier Products/Services API",
          status: "❌ FAIL",
          details: `HTTP ${response.status}: ${await response.text()}`
        });
      }
    } catch (error) {
      tests.push({
        name: "Supplier Products/Services API",
        status: "❌ FAIL",
        details: error.message,
        error: error
      });
    }

    // Test 2: Check if booking add-ons service is available
    try {
      const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);
      tests.push({
        name: "Booking Add-ons Service Resolution",
        status: "✅ PASS",
        details: "Service resolved successfully"
      });
    } catch (error) {
      tests.push({
        name: "Booking Add-ons Service Resolution",
        status: "❌ FAIL",
        details: error.message,
        error: error
      });
    }

    // Test 3: Check query service
    try {
      const query = req.scope.resolve("query");
      const { data: products } = await query.graph({
        entity: "product",
        filters: {},
        fields: ["id", "title", "metadata"],
        take: 5
      });
      
      tests.push({
        name: "Query Service - Products",
        status: "✅ PASS",
        details: `Found ${products.length} products`,
        data: products.map(p => ({ id: p.id, title: p.title, hasMetadata: !!p.metadata }))
      });

      // Check for add-on variants specifically
      const { data: variants } = await query.graph({
        entity: "product_variant",
        filters: {},
        fields: ["id", "title", "metadata"],
        take: 10
      });

      const addOnVariants = variants.filter(v => 
        v.metadata?.add_on_service === true || 
        v.metadata?.add_on_service === "true" ||
        v.metadata?.type === "add-on"
      );

      tests.push({
        name: "Query Service - Add-on Variants",
        status: addOnVariants.length > 0 ? "✅ PASS" : "⚠️ WARN",
        details: `Found ${addOnVariants.length} add-on variants out of ${variants.length} total variants`,
        data: addOnVariants.map(v => ({ 
          id: v.id, 
          title: v.title, 
          metadata: v.metadata 
        }))
      });

    } catch (error) {
      tests.push({
        name: "Query Service Test",
        status: "❌ FAIL",
        details: error.message,
        error: error
      });
    }

    // Test 4: Test filtered products/services (destination-specific)
    try {
      const testDestinationId = "01JSBNCPRYENAAM64WY5QW8XS5"; // Use a real destination ID from the sample data
      const filteredResponse = await fetch(`${req.protocol}://${req.get('host')}/admin/supplier-management/products-services?destination_id=${testDestinationId}&status=active&limit=5`, {
        headers: {
          'Cookie': req.headers.cookie || '',
        }
      });

      if (filteredResponse.ok) {
        const data = await filteredResponse.json();
        tests.push({
          name: "Destination-Filtered Products/Services",
          status: "✅ PASS",
          details: `Found ${data.product_services?.length || 0} destination-specific products/services`,
          data: {
            destination_id: testDestinationId,
            count: data.count,
            sample: data.product_services?.slice(0, 2).map((ps: any) => ({
              id: ps.id,
              name: ps.name,
              destination_id: ps.destination_id,
              service_level: ps.service_level
            }))
          }
        });
      } else {
        tests.push({
          name: "Destination-Filtered Products/Services",
          status: "❌ FAIL",
          details: `HTTP ${filteredResponse.status}: ${await filteredResponse.text()}`
        });
      }
    } catch (error) {
      tests.push({
        name: "Destination-Filtered Products/Services",
        status: "❌ FAIL",
        details: error.message,
        error: error
      });
    }

    return res.json({
      success: true,
      message: "Add-ons functionality test completed",
      timestamp: new Date().toISOString(),
      tests: tests,
      summary: {
        total: tests.length,
        passed: tests.filter(t => t.status.includes("PASS")).length,
        failed: tests.filter(t => t.status.includes("FAIL")).length,
        warnings: tests.filter(t => t.status.includes("WARN")).length
      }
    });

  } catch (error) {
    console.error("❌ Error in add-ons test endpoint:", error);
    return res.status(500).json({
      success: false,
      message: "Test endpoint failed",
      error: error.message,
      stack: error.stack
    });
  }
};
