import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import ItineraryService from "../../../../../modules/concierge-management/itinerary-service";
import { asClass } from "awilix";
import { ITINERARY_SERVICE } from "../../../../../modules/concierge-management";

// Validation schemas
export const PutAdminUpdateItinerary = z.object({
  title: z.string().optional(),
  status: z.enum(["DRAFT", "FINALIZED"]).optional(),
});

export type PutAdminUpdateItineraryType = z.infer<typeof PutAdminUpdateItinerary>;

/**
 * Ensure itinerary service is registered
 */
function ensureItineraryServiceRegistered(container: any): ItineraryService {
  // Register with constant key
  if (!container.hasRegistration(ITINERARY_SERVICE)) {
    container.register({
      [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
    });
  }

  // Register with string key for backward compatibility
  if (!container.hasRegistration("itineraryService")) {
    container.register({
      itineraryService: asClass(ItineraryService).singleton(),
    });
  }

  return container.resolve("itineraryService");
}

/**
 * GET /admin/concierge-management/itineraries/[id]
 * Retrieve a specific itinerary with days and events
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        error: "Itinerary ID is required",
      });
    }

    const itineraryService: ItineraryService = ensureItineraryServiceRegistered(req.scope);

    const itinerary = await itineraryService.getItineraryWithDaysAndEvents(id);

    if (!itinerary) {
      return res.status(404).json({
        error: "Itinerary not found",
      });
    }

    // Ensure days is always an array
    const normalizedItinerary = {
      ...itinerary,
      days: Array.isArray(itinerary.days) ? itinerary.days : [],
    };

    res.json({ itinerary: normalizedItinerary });
  } catch (error) {
    console.error("Error retrieving itinerary:", error);
    res.status(404).json({
      error: error instanceof Error ? error.message : "Itinerary not found",
    });
  }
}

/**
 * PUT /admin/concierge-management/itineraries/[id]
 * Update an itinerary
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;
    const validatedData = PutAdminUpdateItinerary.parse(req.body);
    
    const itineraryService: ItineraryService = ensureItineraryServiceRegistered(req.scope);
    
    const itinerary = await itineraryService.updateItineraries(id, validatedData);
    
    res.json({
      itinerary,
      message: "Itinerary updated successfully",
    });
  } catch (error) {
    console.error("Error updating itinerary:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to update itinerary",
    });
  }
}
