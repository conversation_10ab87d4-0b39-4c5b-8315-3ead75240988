import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const { 
      limit = 20, 
      offset = 0, 
      search,
      status,
      payment_status,
      hotel_id,
      sort_by = "created_at",
      sort_order = "desc"
    } = req.query || {};
    
    const filters: Record<string, any> = {};

    // Add search filter
    if (search) {
      filters.$or = [
        { id: { $ilike: `%${search}%` } },
        { "metadata.guest_name": { $ilike: `%${search}%` } },
        { "metadata.guest_email": { $ilike: `%${search}%` } },
        { "metadata.hotel_name": { $ilike: `%${search}%` } },
      ];
    }

    // Add status filter
    if (status && status !== "all") {
      filters.status = status;
    }

    // Add payment status filter
    if (payment_status && payment_status !== "all") {
      filters["metadata.payment_status"] = payment_status;
    }

    // Add hotel filter
    if (hotel_id && hotel_id !== "all") {
      filters["metadata.hotel_id"] = hotel_id;
    }

    // Build order clause
    const orderField = sort_by === "guest_name" ? "metadata.guest_name" : 
                      sort_by === "hotel_name" ? "metadata.hotel_name" :
                      sort_by === "total_amount" ? "metadata.total_amount" :
                      sort_by === "payment_status" ? "metadata.payment_status" :
                      sort_by;

    const order = { [orderField]: sort_order.toUpperCase() };

    const {
      data: orders,
      metadata: { count, take, skip },
    } = await query.graph({
      entity: "order",
      fields: [
        "id",
        "status", 
        "total",
        "currency_code",
        "created_at",
        "metadata",
      ],
      filters,
      pagination: {
        skip: Number(offset),
        take: Number(limit),
      },
      order,
    });

    // Transform orders to bookings format
    const bookings = orders.map((order: any) => ({
      order_id: order.id,
      status: order.status,
      total: order.total,
      currency_code: order.currency_code,
      created_at: order.created_at,
      metadata: order.metadata || {},
      // Extract commonly used fields from metadata
      guest_name: order.metadata?.guest_name,
      guest_email: order.metadata?.guest_email,
      room_type: order.metadata?.room_type,
      payment_status: order.metadata?.payment_status,
      total_amount: order.metadata?.total_amount || order.total,
      check_in_date: order.metadata?.check_in_date,
      check_out_date: order.metadata?.check_out_date,
    }));

    res.json({
      bookings,
      count,
      limit: take,
      offset: skip,
    });
  } catch (error) {
    console.error("Error listing bookings:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to list bookings",
    });
  }
};
