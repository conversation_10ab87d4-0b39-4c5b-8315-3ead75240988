import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { z } from "zod";
import { asClass, asValue } from "awilix";
import ItineraryService from "../../../../../../../modules/concierge-management/itinerary-service";
import { ITINERARY_SERVICE } from "../../../../../../../modules/concierge-management";
import { BaseRepository } from "../../../../../../../modules/concierge-management/repositories";

// Validation schema
export const PostCreateItineraryFromBooking = z.object({
  created_by: z.string().optional(),
});

export type PostCreateItineraryFromBookingType = z.infer<typeof PostCreateItineraryFromBooking>;

/**
 * Ensure itinerary service is registered
 */
function ensureItineraryServiceRegistered(container: any): ItineraryService {
  return container.resolve(ITINERARY_SERVICE);
}

/**
 * POST /admin/concierge-management/bookings/:id/itinerary/create
 * Create an itinerary from a booking
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("🚀 [CONCIERGE-ITINERARY-CREATE] Route called with params:", req.params);
    const { id: bookingId } = req.params;
    const validatedData = PostCreateItineraryFromBooking.parse(req.body);

    // Resolve services using the proper framework pattern
    console.log("🔍 [CONCIERGE-ITINERARY-CREATE] Ensuring itinerary service is registered...");
    const itineraryService = ensureItineraryServiceRegistered(req.scope);
    console.log("✅ [CONCIERGE-ITINERARY-CREATE] Itinerary service resolved successfully");
    const orderService = req.scope.resolve(Modules.ORDER);

    // Get booking details to extract start date
    const booking = await orderService.retrieveOrder(bookingId, {
      relations: ["metadata"],
    });

    if (!booking) {
      return res.status(404).json({
        error: "Booking not found",
      });
    }

    // Extract check-in date from booking metadata
    let bookingStartDate = new Date(); // Default to today
    if (booking.metadata?.check_in_date) {
      bookingStartDate = new Date(booking.metadata.check_in_date as string);
    }

    console.log("🔍 [CONCIERGE-ITINERARY-CREATE] Creating itinerary from booking...");

    const result = await itineraryService.createItineraryFromBooking(
      bookingId,
      bookingStartDate,
      validatedData.created_by
    );

    // Update booking metadata to include itinerary_id
    await orderService.updateOrders(bookingId, {
      metadata: {
        ...booking.metadata,
        itinerary_id: result.itinerary.id,
      },
    });

    console.log("✅ [CONCIERGE-ITINERARY-CREATE] Itinerary created successfully:", result.itinerary.id);

    res.status(201).json({
      itinerary: result.itinerary,
      first_day: result.firstDay,
      message: "Itinerary created successfully",
      redirect_url: `/concierge-management/itineraries/${result.itinerary.id}/builder`,
    });
  } catch (error) {
    console.error("❌ [CONCIERGE-ITINERARY-CREATE] Error creating itinerary from booking:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to create itinerary from booking",
    });
  }
}
