import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { BOOKING_ADD_ONS_MODULE } from "../../../../../modules/booking-add-ons";

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    // Get booking ID from params
    const { id } = req.params;
    console.log(`Fetching concierge booking details for ID: ${id}`);
    
    const orderService = req.scope.resolve(Modules.ORDER);
    const booking = await orderService.retrieveOrder(id, {
      relations: ["metadata"],
    });

    if (!booking) {
      return res.status(404).json({
        message: "Booking not found",
      });
    }

    console.log(`✅ Found booking: ${booking.id}`);

    // Fetch booking add-ons
    let addOns = [];
    try {
      const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);
      const addOnsResult = await bookingAddOnService.getBookingAddOns(id);
      addOns = addOnsResult.booking_add_ons;
      console.log(`✅ Found ${addOns.length} add-ons for booking ${id}`);
    } catch (addOnError) {
      console.error("❌ Error fetching add-ons:", addOnError);
      // Don't fail the entire request if add-ons can't be fetched
    }

    // Transform booking data for concierge view
    const conciergeBooking = {
      ...booking,
      // Extract commonly used fields from metadata for easier access
      guest_name: booking.metadata?.guest_name,
      guest_email: booking.metadata?.guest_email,
      guest_phone: booking.metadata?.guest_phone,
      hotel_id: booking.metadata?.hotel_id,
      hotel_name: booking.metadata?.hotel_name,
      room_type: booking.metadata?.room_type,
      check_in_date: booking.metadata?.check_in_date,
      check_out_date: booking.metadata?.check_out_date,
      number_of_guests: booking.metadata?.number_of_guests,
      number_of_rooms: booking.metadata?.number_of_rooms,
      payment_status: booking.metadata?.payment_status,
      total_amount: booking.metadata?.total_amount || booking.total,
      // Concierge-specific fields
      concierge_notes: booking.metadata?.concierge_notes,
      special_requests: booking.metadata?.special_requests,
      assigned_concierge: booking.metadata?.assigned_concierge,
      priority_level: booking.metadata?.priority_level,
      task_assignments: booking.metadata?.task_assignments,
      itinerary_id: booking.metadata?.itinerary_id,
    };

    res.json({
      booking: conciergeBooking,
      add_ons: addOns,
    });
  } catch (error) {
    console.error("Error fetching concierge booking details:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to get booking",
    });
  }
};
