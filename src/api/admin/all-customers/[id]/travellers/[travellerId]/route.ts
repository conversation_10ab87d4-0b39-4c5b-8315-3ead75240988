import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework";
import { z } from "zod";
import CustomerTravellerModuleService from "../../../../../../modules/customer-travellers/service";
import { CUSTOMER_TRAVELLER_MODULE } from "../../../../../../modules/customer-travellers";
import {
  Gender,
  Relationship,
} from "../../../../../../modules/customer-travellers/types";

// Validation schema for updating travellers
const UpdateTravellerSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  gender: z.nativeEnum(Gender),
  date_of_birth: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  relationship: z.nativeEnum(Relationship).optional(),
});

type UpdateTravellerInput = z.infer<typeof UpdateTravellerSchema>;

/**
 * GET /admin/all-customers/[id]/travellers/[travellerId]
 * Get a specific traveller for a customer (admin access)
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const customerTravellerService: CustomerTravellerModuleService =
      req.scope.resolve(CUSTOMER_TRAVELLER_MODULE);

    const { id: customerId, travellerId } = req.params;

    if (!customerId) {
      return res.status(400).json({
        type: "invalid_request",
        message: "Customer ID is required",
      });
    }

    if (!travellerId) {
      return res.status(400).json({
        type: "invalid_request",
        message: "Traveller ID is required",
      });
    }

    const traveller = await customerTravellerService.getCustomerTraveller(
      travellerId,
      customerId
    );

    res.json({
      traveller,
    });
  } catch (error) {
    console.error("Error fetching customer traveller:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to fetch customer traveller",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * PUT /admin/all-customers/[id]/travellers/[travellerId]
 * Update a specific traveller for a customer (admin access)
 */
export const PUT = async (
  req: AuthenticatedMedusaRequest<UpdateTravellerInput>,
  res: MedusaResponse
) => {
  try {
    const validatedData = UpdateTravellerSchema.parse(req.body);

    const customerTravellerService: CustomerTravellerModuleService =
      req.scope.resolve(CUSTOMER_TRAVELLER_MODULE);

    const { id: customerId, travellerId } = req.params;

    if (!customerId) {
      return res.status(400).json({
        type: "invalid_request",
        message: "Customer ID is required",
      });
    }

    if (!travellerId) {
      return res.status(400).json({
        type: "invalid_request",
        message: "Traveller ID is required",
      });
    }

    // Convert date_of_birth string to Date object if provided
    const updateData = {
      ...validatedData,
      ...(validatedData.date_of_birth && {
        date_of_birth: new Date(validatedData.date_of_birth),
      }),
    };

    // Update the traveller (the service method will verify ownership)
    const updatedTraveller =
      await customerTravellerService.updateCustomerTraveller(
        travellerId,
        customerId,
        updateData
      );

    res.json({
      traveller: updatedTraveller,
      success: true,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        type: "validation_error",
        message: "Validation error",
        errors: error.errors,
      });
    } else {
      console.error("Error updating customer traveller:", error);
      res.status(500).json({
        type: "server_error",
        message: "Failed to update customer traveller",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
};

/**
 * DELETE /admin/all-customers/[id]/travellers/[travellerId]
 * Delete a specific traveller for a customer (admin access)
 */
export const DELETE = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const customerTravellerService: CustomerTravellerModuleService =
      req.scope.resolve(CUSTOMER_TRAVELLER_MODULE);

    const { id: customerId, travellerId } = req.params;

    if (!customerId) {
      return res.status(400).json({
        type: "invalid_request",
        message: "Customer ID is required",
      });
    }

    if (!travellerId) {
      return res.status(400).json({
        type: "invalid_request",
        message: "Traveller ID is required",
      });
    }

    // Delete the traveller (the service method will verify ownership)
    await customerTravellerService.deleteCustomerTraveller(
      travellerId,
      customerId
    );

    res.json({
      success: true,
      message: "Traveller deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting customer traveller:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to delete customer traveller",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
