import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework";
import { z } from "zod";
import CustomerTravellerModuleService from "../../../../../modules/customer-travellers/service";
import { CUSTOMER_TRAVELLER_MODULE } from "../../../../../modules/customer-travellers";
import {
  Gender,
  Relationship,
} from "../../../../../modules/customer-travellers/types";

// Validation schema for creating travellers
const CreateTravellerSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  gender: z.nativeEnum(Gender),
  date_of_birth: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  relationship: z.nativeEnum(Relationship).optional(),
});

type CreateTravellerInput = z.infer<typeof CreateTravellerSchema>;

/**
 * GET /admin/all-customers/[id]/travellers
 * Get all travellers for a specific customer (admin access)
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const customerTravellerService: CustomerTravellerModuleService =
      req.scope.resolve(CUSTOMER_TRAVELLER_MODULE);

    const { id: customerId } = req.params;

    if (!customerId) {
      return res.status(400).json({
        type: "invalid_request",
        message: "Customer ID is required",
      });
    }

    const travellers = await customerTravellerService.getCustomerTravellers(
      customerId
    );

    res.json({
      travellers,
      count: travellers.length,
    });
  } catch (error) {
    console.error("Error fetching customer travellers:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to fetch customer travellers",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * POST /admin/all-customers/[id]/travellers
 * Create a new traveller for a specific customer (admin access)
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<CreateTravellerInput>,
  res: MedusaResponse
) => {
  try {
    const validatedData = CreateTravellerSchema.parse(req.body);

    const customerTravellerService: CustomerTravellerModuleService =
      req.scope.resolve(CUSTOMER_TRAVELLER_MODULE);

    const { id: customerId } = req.params;

    if (!customerId) {
      return res.status(400).json({
        type: "invalid_request",
        message: "Customer ID is required",
      });
    }

    // Convert date_of_birth string to Date object
    const createData = {
      customer_id: customerId,
      ...validatedData,
      date_of_birth: new Date(validatedData.date_of_birth),
    };

    const traveller = await customerTravellerService.createCustomerTraveller(
      createData
    );

    res.status(201).json({
      traveller,
      success: true,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        type: "validation_error",
        message: "Validation error",
        errors: error.errors,
      });
    } else {
      console.error("Error creating customer traveller:", error);
      res.status(500).json({
        type: "server_error",
        message: "Failed to create customer traveller",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
};
