#!/usr/bin/env node

import { withClient } from './src/utils/db';

async function checkSeasonalPricing() {
  try {
    console.log('🔍 Checking seasonal pricing data in database...');
    
    await withClient(async (client) => {
      // Check if seasonal_price_rule table exists
      console.log('\n1️⃣ Checking if seasonal_price_rule table exists...');
      const tableCheck = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'seasonal_price_rule'
        );
      `);
      console.log(`✅ Table exists: ${tableCheck.rows[0].exists}`);
      
      if (!tableCheck.rows[0].exists) {
        console.log('❌ seasonal_price_rule table does not exist!');
        return;
      }

      // Count total seasonal price rules
      console.log('\n2️⃣ Counting total seasonal price rules...');
      const totalCount = await client.query(`
        SELECT COUNT(*) as total_count
        FROM seasonal_price_rule 
        WHERE deleted_at IS NULL
      `);
      console.log(`📊 Total seasonal price rules: ${totalCount.rows[0].total_count}`);

      // Check seasonal rules for The Chedi Andermatt hotel
      console.log('\n3️⃣ Checking seasonal rules for The Chedi Andermatt...');
      const hotelId = '01JWT8VVPSCRECG0GBSWBREST7'; // The Chedi Andermatt
      
      // First get base price rules for this hotel
      const basePriceRules = await client.query(`
        SELECT id, room_config_id, currency_code, occupancy_type_id, meal_plan_id
        FROM base_price_rule 
        WHERE hotel_id = $1 AND deleted_at IS NULL
      `, [hotelId]);
      
      console.log(`📋 Base price rules for hotel: ${basePriceRules.rows.length}`);
      
      if (basePriceRules.rows.length === 0) {
        console.log('❌ No base price rules found for this hotel!');
        return;
      }

      // Check seasonal overrides for these base price rules
      const basePriceRuleIds = basePriceRules.rows.map(rule => rule.id);
      
      const seasonalRules = await client.query(`
        SELECT 
          spr.id,
          spr.name,
          spr.start_date,
          spr.end_date,
          spr.currency_code,
          spr.amount,
          spr.base_price_rule_id,
          bpr.room_config_id,
          bpr.currency_code as base_currency
        FROM seasonal_price_rule spr
        JOIN base_price_rule bpr ON spr.base_price_rule_id = bpr.id
        WHERE spr.base_price_rule_id = ANY($1) 
        AND spr.deleted_at IS NULL
        ORDER BY spr.start_date
      `, [basePriceRuleIds]);
      
      console.log(`🌟 Seasonal price rules for hotel: ${seasonalRules.rows.length}`);
      
      if (seasonalRules.rows.length > 0) {
        console.log('\n📋 Seasonal rules details:');
        seasonalRules.rows.forEach((rule: any, index: number) => {
          console.log(`  ${index + 1}. ${rule.name || 'Unnamed'}`);
          console.log(`     ID: ${rule.id}`);
          console.log(`     Dates: ${rule.start_date} to ${rule.end_date}`);
          console.log(`     Currency: ${rule.currency_code || rule.base_currency}`);
          console.log(`     Amount: ${rule.amount}`);
          console.log(`     Room Config: ${rule.room_config_id}`);
          console.log('');
        });

        // Check seasonal rules by currency
        console.log('\n4️⃣ Checking seasonal rules by currency...');
        const currencyBreakdown = await client.query(`
          SELECT 
            COALESCE(spr.currency_code, bpr.currency_code, 'USD') as currency,
            COUNT(*) as count
          FROM seasonal_price_rule spr
          JOIN base_price_rule bpr ON spr.base_price_rule_id = bpr.id
          WHERE spr.base_price_rule_id = ANY($1) 
          AND spr.deleted_at IS NULL
          GROUP BY COALESCE(spr.currency_code, bpr.currency_code, 'USD')
          ORDER BY currency
        `, [basePriceRuleIds]);
        
        console.log('💱 Currency breakdown:');
        currencyBreakdown.rows.forEach((row: any) => {
          console.log(`  ${row.currency}: ${row.count} rules`);
        });

        // Check specifically for GBP currency
        console.log('\n5️⃣ Checking GBP seasonal rules specifically...');
        const gbpRules = await client.query(`
          SELECT 
            spr.id,
            spr.name,
            spr.start_date,
            spr.end_date,
            COALESCE(spr.currency_code, bpr.currency_code, 'USD') as effective_currency
          FROM seasonal_price_rule spr
          JOIN base_price_rule bpr ON spr.base_price_rule_id = bpr.id
          WHERE spr.base_price_rule_id = ANY($1) 
          AND spr.deleted_at IS NULL
          AND COALESCE(spr.currency_code, bpr.currency_code, 'USD') = 'GBP'
          ORDER BY spr.start_date
        `, [basePriceRuleIds]);
        
        console.log(`🇬🇧 GBP seasonal rules: ${gbpRules.rows.length}`);
        if (gbpRules.rows.length > 0) {
          gbpRules.rows.forEach((rule: any, index: number) => {
            console.log(`  ${index + 1}. ${rule.name || 'Unnamed'} (${rule.start_date} to ${rule.end_date})`);
          });
        }
      } else {
        console.log('❌ No seasonal price rules found for this hotel!');
      }
    });
    
  } catch (error) {
    console.error('❌ Error checking seasonal pricing:', error);
  }
}

// Run the check
checkSeasonalPricing().then(() => {
  console.log('\n✅ Seasonal pricing check completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Failed to check seasonal pricing:', error);
  process.exit(1);
});
