{"info": {"name": "Itinerary CRM Integration API", "description": "API collection for itinerary CRM integration including public endpoints for fetching complete itinerary details and admin endpoints for preview generation.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:9000", "description": "Base URL for the Medusa store API"}, {"key": "adminBaseUrl", "value": "http://localhost:9000", "description": "Base URL for the Medusa admin API"}, {"key": "itineraryId", "value": "itin_01234567890", "description": "Sample itinerary ID - replace with actual ID"}], "item": [{"name": "Public Itinerary Details API", "description": "Public endpoint for CRM integration to fetch complete itinerary details", "item": [{"name": "Get Complete Itinerary Details", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "description": "Content type header"}, {"key": "Accept", "value": "application/json", "description": "Accept header"}], "url": {"raw": "{{baseUrl}}/store/concierge-management/itineraries/{{itineraryId}}", "host": ["{{baseUrl}}"], "path": ["store", "concierge-management", "itineraries", "{{itineraryId}}"]}, "description": "Fetches complete itinerary details including all related data from itinerary, itinerary_day, and itinerary_event tables. This endpoint is designed for CRM integration and provides comprehensive data in a single response."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/store/concierge-management/itineraries/itin_01234567890", "host": ["{{baseUrl}}"], "path": ["store", "concierge-management", "itineraries", "itin_01234567890"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"itinerary\": {\n      \"id\": \"itin_01234567890\",\n      \"booking_id\": \"order_01234567890\",\n      \"title\": \"Mediterranean Cruise Adventure\",\n      \"status\": \"DRAFT\",\n      \"created_by\": \"user_01234567890\",\n      \"created_at\": \"2025-01-15T10:00:00.000Z\",\n      \"updated_at\": \"2025-01-15T14:30:00.000Z\",\n      \"days\": [\n        {\n          \"id\": \"itin_day_01234567890\",\n          \"itinerary_id\": \"itin_01234567890\",\n          \"date\": \"2025-02-01T00:00:00.000Z\",\n          \"title\": \"Departure Day\",\n          \"sort_order\": 1,\n          \"events\": [\n            {\n              \"id\": \"itin_event_01234567890\",\n              \"day_id\": \"itin_day_01234567890\",\n              \"category\": \"Flight\",\n              \"type\": \"Departure\",\n              \"title\": \"Flight to Barcelona\",\n              \"notes\": \"Check-in 2 hours before departure\",\n              \"start_time\": \"08:00\",\n              \"end_time\": \"12:00\",\n              \"duration\": \"4h\",\n              \"timezone\": \"UTC\",\n              \"details\": {\n                \"flight_number\": \"BA456\",\n                \"airline\": \"British Airways\",\n                \"departure_airport\": \"LHR\",\n                \"arrival_airport\": \"BCN\",\n                \"booked_through\": \"Expedia\",\n                \"confirmation_number\": \"ABC123\",\n                \"provider\": \"British Airways\"\n              },\n              \"price\": 45000,\n              \"currency\": \"USD\",\n              \"media\": [],\n              \"attachments\": [],\n              \"people\": []\n            }\n          ]\n        }\n      ]\n    },\n    \"summary\": {\n      \"total_days\": 7,\n      \"total_events\": 15,\n      \"event_categories\": [\"Flight\", \"Lodging\", \"Activity\", \"Cruise\"],\n      \"date_range\": {\n        \"start_date\": \"2025-02-01T00:00:00.000Z\",\n        \"end_date\": \"2025-02-08T00:00:00.000Z\"\n      }\n    },\n    \"meta\": {\n      \"retrieved_at\": \"2025-01-22T15:30:00.000Z\",\n      \"api_version\": \"1.0\",\n      \"endpoint\": \"store/concierge-management/itineraries\"\n    }\n  }\n}"}, {"name": "Itinerary Not Found", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/store/concierge-management/itineraries/invalid_id", "host": ["{{baseUrl}}"], "path": ["store", "concierge-management", "itineraries", "invalid_id"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": false,\n  \"error\": \"Itinerary not found\",\n  \"code\": \"ITINERARY_NOT_FOUND\",\n  \"itinerary_id\": \"invalid_id\"\n}"}]}]}, {"name": "Admin Preview API", "description": "Admin endpoints for generating itinerary previews via CRM integration", "item": [{"name": "Generate Itinerary Preview", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "Content type header"}, {"key": "Accept", "value": "application/json", "description": "Accept header"}, {"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "description": "Admin authentication token"}], "body": {"mode": "raw", "raw": "{\n  \"metadata\": {\n    \"source\": \"medusa-admin\",\n    \"user_id\": \"user_01234567890\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{adminBaseUrl}}/admin/concierge-management/itineraries/{{itineraryId}}/preview", "host": ["{{adminBaseUrl}}"], "path": ["admin", "concierge-management", "itineraries", "{{itineraryId}}", "preview"]}, "description": "Generates a preview URL for the specified itinerary by calling the configured CRM API. Returns a preview URL that can be used to redirect users to the CRM preview page."}, "response": [{"name": "Preview Generated Successfully", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer admin_token_here"}], "body": {"mode": "raw", "raw": "{\n  \"metadata\": {\n    \"source\": \"medusa-admin\",\n    \"user_id\": \"user_01234567890\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{adminBaseUrl}}/admin/concierge-management/itineraries/itin_01234567890/preview", "host": ["{{adminBaseUrl}}"], "path": ["admin", "concierge-management", "itineraries", "itin_01234567890", "preview"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"preview_url\": \"https://your-crm-preview.com/itinerary/itin_01234567890?token=preview_token_123\",\n    \"itinerary_id\": \"itin_01234567890\",\n    \"booking_id\": \"order_01234567890\"\n  },\n  \"meta\": {\n    \"generated_at\": \"2025-01-22T15:30:00.000Z\",\n    \"crm_response\": {\n      \"preview_url\": \"https://your-crm-preview.com/itinerary/itin_01234567890?token=preview_token_123\",\n      \"expires_at\": \"2025-01-23T15:30:00.000Z\"\n    }\n  }\n}"}, {"name": "Development Mock Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{adminBaseUrl}}/admin/concierge-management/itineraries/itin_01234567890/preview", "host": ["{{adminBaseUrl}}"], "path": ["admin", "concierge-management", "itineraries", "itin_01234567890", "preview"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"preview_url\": \"https://your-crm-preview.com/itinerary/itin_01234567890?mock=true\",\n    \"itinerary_id\": \"itin_01234567890\",\n    \"booking_id\": \"order_01234567890\"\n  },\n  \"meta\": {\n    \"generated_at\": \"2025-01-22T15:30:00.000Z\",\n    \"mode\": \"development_mock\"\n  }\n}"}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set dynamic variables if needed", "// pm.environment.set('timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test scripts", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has correct content type', function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});"]}}]}