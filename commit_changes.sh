#!/bin/bash

# Add all changes
git add .

# Commit with detailed message
git commit -m "feat: implement Apply button functionality for hotel pricing cost/margin system

- Add new bulk-populate API endpoint for applying cost/margin defaults to weekday prices
- Integrate Apply button with backend API for existing pricing rules (bpr_ prefix)
- Add comprehensive error handling and user feedback via toast notifications
- Implement cost/margin override functionality with proper data validation
- Update frontend to call API and update local state when Apply button is clicked
- Add proper logging for debugging and monitoring API calls
- Ensure data persistence by saving cost/margin values to database
- Maintain existing UI/UX patterns while adding new functionality

The Apply button now successfully:
- Calculates total price from gross cost + fixed margin + margin percentage
- Calls backend API to save cost/margin data to database
- Populates all weekday price fields with calculated total
- Provides user feedback on success/error states
- Only works for existing pricing rules to prevent data inconsistencies

Files modified:
- src/api/admin/hotel-management/room-configs/[id]/pricing/bulk-populate/route.ts (new)
- src/admin/components/hotel/pricing/comprehensive-pricing-table.tsx (modified)"

echo "Changes committed successfully!"
